<?php
/**
 * Wizard
 *
 * @package Boat_Rental_Whizzie
 * <AUTHOR> Themes
 * @since 1.0.0
 */

class Boat_Rental_Whizzie {
	
	protected $version = '1.1.0';
	
	/** @var string Current theme name, used as namespace in actions. */
	protected $boat_rental_theme_name = '';
	protected $boat_rental_theme_title = '';
	
	/** @var string Wizard page slug and title. */
	protected $boat_rental_page_slug = '';
	protected $boat_rental_page_title = '';
	
	/** @var array Wizard steps set by user. */
	protected $config_steps = array();
	
	/**
	 * Relative plugin url for this plugin folder
	 * @since 1.0.0
	 * @var string
	 */
	protected $boat_rental_plugin_url = '';

	public $boat_rental_plugin_path;
	public $parent_slug;
	
	/**
	 * TGMPA instance storage
	 *
	 * @var object
	 */
	protected $tgmpa_instance;
	
	/**
	 * TGMPA Menu slug
	 *
	 * @var string
	 */
	protected $tgmpa_menu_slug = 'tgmpa-install-plugins';
	
	/**
	 * TGMPA Menu url
	 *
	 * @var string
	 */
	protected $tgmpa_url = 'themes.php?page=tgmpa-install-plugins';
	
	/**
	 * Constructor
	 *
	 * @param $config	Our config parameters
	 */
	public function __construct( $config ) {
		$this->set_vars( $config );
		$this->init();
	}
	
	/**
	 * Set some settings
	 * @since 1.0.0
	 * @param $config	Our config parameters
	 */
	public function set_vars( $config ) {
	
		require_once trailingslashit( WHIZZIE_DIR ) . 'tgm/class-tgm-plugin-activation.php';
		require_once trailingslashit( WHIZZIE_DIR ) . 'tgm/tgm.php';

		if( isset( $config['boat_rental_page_slug'] ) ) {
			$this->boat_rental_page_slug = esc_attr( $config['boat_rental_page_slug'] );
		}
		if( isset( $config['boat_rental_page_title'] ) ) {
			$this->boat_rental_page_title = esc_attr( $config['boat_rental_page_title'] );
		}
		if( isset( $config['steps'] ) ) {
			$this->config_steps = $config['steps'];
		}
		
		$this->boat_rental_plugin_path = trailingslashit( dirname( __FILE__ ) );
		$relative_url = str_replace( get_template_directory(), '', $this->boat_rental_plugin_path );
		$this->boat_rental_plugin_url = trailingslashit( get_template_directory_uri() . $relative_url );
		$boat_rental_current_theme = wp_get_theme();
		$this->boat_rental_theme_title = $boat_rental_current_theme->get( 'Name' );
		$this->boat_rental_theme_name = strtolower( preg_replace( '#[^a-zA-Z]#', '', $boat_rental_current_theme->get( 'Name' ) ) );
		$this->boat_rental_page_slug = apply_filters( $this->boat_rental_theme_name . '_theme_setup_wizard_boat_rental_page_slug', $this->boat_rental_theme_name . '-wizard' );
		$this->parent_slug = apply_filters( $this->boat_rental_theme_name . '_theme_setup_wizard_parent_slug', '' );

	}
	
	/**
	 * Hooks and filters
	 * @since 1.0.0
	 */	
	public function init() {
		
		if ( class_exists( 'TGM_Plugin_Activation' ) && isset( $GLOBALS['tgmpa'] ) ) {
			add_action( 'init', array( $this, 'get_tgmpa_instance' ), 30 );
			add_action( 'init', array( $this, 'set_tgmpa_url' ), 40 );
		}
		add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_scripts' ) );
		add_action( 'admin_menu', array( $this, 'menu_page' ) );
		add_action( 'admin_init', array( $this, 'get_plugins' ), 30 );
		add_filter( 'tgmpa_load', array( $this, 'tgmpa_load' ), 10, 1 );
		add_action( 'wp_ajax_setup_plugins', array( $this, 'setup_plugins' ) );
		add_action( 'wp_ajax_boat_rental_setup_widgets', array( $this, 'boat_rental_setup_widgets' ) );
		
	}
	
	public function enqueue_scripts() {
		wp_enqueue_style( 'boat-rental-homepage-setup-style', get_template_directory_uri() . '/inc/homepage-setup/assets/css/homepage-setup-style.css');
		wp_register_script( 'boat-rental-homepage-setup-script', get_template_directory_uri() . '/inc/homepage-setup/assets/js/homepage-setup-script.js', array( 'jquery' ), time() );
		wp_localize_script( 
			'boat-rental-homepage-setup-script',
			'whizzie_params',
			array(
				'ajaxurl' 		=> admin_url( 'admin-ajax.php' ),
				'wpnonce' 		=> wp_create_nonce( 'whizzie_nonce' ),
				'verify_text'	=> esc_html( 'verifying', 'boat-rental' )
			)
		);
		wp_enqueue_script( 'boat-rental-homepage-setup-script' );
	}
	
	public static function get_instance() {
		if ( ! self::$instance ) {
			self::$instance = new self;
		}
		return self::$instance;
	}
	
	public function tgmpa_load( $status ) {
		return is_admin() || current_user_can( 'install_themes' );
	}
			
	/**
	 * Get configured TGMPA instance
	 *
	 * @access public
	 * @since 1.1.2
	 */
	public function get_tgmpa_instance() {
		$this->tgmpa_instance = call_user_func( array( get_class( $GLOBALS['tgmpa'] ), 'get_instance' ) );
	}
	
	/**
	 * Update $tgmpa_menu_slug and $tgmpa_parent_slug from TGMPA instance
	 *
	 * @access public
	 * @since 1.1.2
	 */
	public function set_tgmpa_url() {
		$this->tgmpa_menu_slug = ( property_exists( $this->tgmpa_instance, 'menu' ) ) ? $this->tgmpa_instance->menu : $this->tgmpa_menu_slug;
		$this->tgmpa_menu_slug = apply_filters( $this->boat_rental_theme_name . '_theme_setup_wizard_tgmpa_menu_slug', $this->tgmpa_menu_slug );
		$tgmpa_parent_slug = ( property_exists( $this->tgmpa_instance, 'parent_slug' ) && $this->tgmpa_instance->parent_slug !== 'themes.php' ) ? 'admin.php' : 'themes.php';
		$this->tgmpa_url = apply_filters( $this->boat_rental_theme_name . '_theme_setup_wizard_tgmpa_url', $tgmpa_parent_slug . '?page=' . $this->tgmpa_menu_slug );
	}
	
	/**
	 * Make a modal screen for the wizard
	 */
	public function menu_page() {
		add_theme_page( esc_html( $this->boat_rental_page_title ), esc_html( $this->boat_rental_page_title ), 'manage_options', $this->boat_rental_page_slug, array( $this, 'wizard_page' ) );
	}
	
	/**
	 * Make an interface for the wizard
	 */
	public function wizard_page() { 
		tgmpa_load_bulk_installer();

		if ( ! class_exists( 'TGM_Plugin_Activation' ) || ! isset( $GLOBALS['tgmpa'] ) ) {
			die( esc_html__( 'Failed to find TGM', 'boat-rental' ) );
		}

		$url = wp_nonce_url( add_query_arg( array( 'plugins' => 'go' ) ), 'whizzie-setup' );
		$method = '';
		$fields = array_keys( $_POST );

		if ( false === ( $creds = request_filesystem_credentials( esc_url_raw( $url ), $method, false, false, $fields ) ) ) {
			return true;
		}

		if ( ! WP_Filesystem( $creds ) ) {
			request_filesystem_credentials( esc_url_raw( $url ), $method, true, false, $fields );
			return true;
		}

		$boat_rental_theme = wp_get_theme();
		$boat_rental_theme_title = $boat_rental_theme->get( 'Name' );
		$boat_rental_theme_version = $boat_rental_theme->get( 'Version' );

		?>
		<div class="wrap">
			<?php
				printf( '<h1>%s %s</h1>', esc_html( $boat_rental_theme_title ), esc_html( '(Version :- ' . $boat_rental_theme_version . ')' ) );
			?>
			<div class="homepage-setup">
				<div class="homepage-setup-theme-bundle">
					<div class="homepage-setup-theme-bundle-one">
						<h1><?php echo esc_html__( 'WP Theme Bundle', 'boat-rental' ); ?></h1>
						<p><?php echo wp_kses_post( 'Get <span>15% OFF</span> on all WordPress themes! Use code <span>"BNDL15OFF"</span> at checkout. Limited time offer!' ); ?></p>
					</div>
					<div class="homepage-setup-theme-bundle-two">
						<p><?php echo wp_kses_post( 'Extra <span>15%</span> OFF' ); ?></p>
					</div>
					<div class="homepage-setup-theme-bundle-three">
						<img src="<?php echo esc_url( get_template_directory_uri() . '/inc/homepage-setup/assets/homepage-setup-images/bundle-banner.png' ); ?>" alt="<?php echo esc_attr__( 'Theme Bundle Image', 'boat-rental' ); ?>">
					</div>
					<div class="homepage-setup-theme-bundle-four">
						<p><?php echo wp_kses_post( '<span>$2795</span>$69' ); ?></p>
						<a target="_blank" href="<?php echo esc_url( BOAT_RENTAL_BUNDLE_BUTTON ); ?>"><?php echo esc_html__( 'SHOP NOW', 'boat-rental' ); ?> <span class="dashicons dashicons-arrow-right-alt2"></span></a>
					</div>
				</div>
			</div>
			
			<div class="card whizzie-wrap">
				<div class="demo_content_image">
					<div class="demo_content">
						<?php
							$boat_rental_steps = $this->get_steps();
							echo '<ul class="whizzie-menu">';
							foreach ( $boat_rental_steps as $boat_rental_step ) {
								$class = 'step step-' . esc_attr( $boat_rental_step['id'] );
								echo '<li data-step="' . esc_attr( $boat_rental_step['id'] ) . '" class="' . esc_attr( $class ) . '">';
								printf( '<h2>%s</h2>', esc_html( $boat_rental_step['title'] ) );

								$content = call_user_func( array( $this, $boat_rental_step['view'] ) );
								if ( isset( $content['summary'] ) ) {
									printf(
										'<div class="summary">%s</div>',
										wp_kses_post( $content['summary'] )
									);
								}
								if ( isset( $content['detail'] ) ) {
									printf(
										'<div class="detail">%s</div>',
										wp_kses_post( $content['detail'] )
									);
								}
								if ( isset( $boat_rental_step['button_text'] ) && $boat_rental_step['button_text'] ) {
									printf( 
										'<div class="button-wrap"><a href="#" class="button button-primary do-it" data-callback="%s" data-step="%s">%s</a></div>',
										esc_attr( $boat_rental_step['callback'] ),
										esc_attr( $boat_rental_step['id'] ),
										esc_html( $boat_rental_step['button_text'] )
									);
								}
								echo '</li>';
							}
							echo '</ul>';
						?>
						
						<ul class="whizzie-nav">
							<?php
							$step_number = 1;	
							foreach ( $boat_rental_steps as $boat_rental_step ) {
								echo '<li class="nav-step-' . esc_attr( $boat_rental_step['id'] ) . '">';
								echo '<span class="step-number">' . esc_html( $step_number ) . '</span>';
								echo '</li>';
								$step_number++;
							}
							?>
							<div class="blank-border"></div>
						</ul>

						<div class="homepage-setup-links">
							<div class="homepage-setup-links buttons">
								<a href="<?php echo esc_url( BOAT_RENTAL_LITE_DOCS_PRO ); ?>" target="_blank" class="button button-primary"><?php echo esc_html__( 'Free Documentation', 'boat-rental' ); ?></a>
								<a href="<?php echo esc_url( BOAT_RENTAL_BUY_NOW ); ?>" class="button button-primary" target="_blank"><?php echo esc_html__( 'Get Premium', 'boat-rental' ); ?></a>
								<a href="<?php echo esc_url( BOAT_RENTAL_DEMO_PRO ); ?>" class="button button-primary" target="_blank"><?php echo esc_html__( 'Premium Demo', 'boat-rental' ); ?></a>
								<a href="<?php echo esc_url( BOAT_RENTAL_SUPPORT_FREE ); ?>" target="_blank" class="button button-primary"><?php echo esc_html__( 'Support Forum', 'boat-rental' ); ?></a>
							</div>
						</div> <!-- .demo_image -->

						<div class="step-loading"><span class="spinner"></span></div>
					</div> <!-- .demo_content -->

					<div class="homepage-setup-image">
						<div class="homepage-setup-theme-buynow">
							<div class="homepage-setup-theme-buynow-one">
								<h1><?php echo wp_kses_post( 'Boat Rental<br>WordPress Theme' ); ?></h1>
								<p><?php echo wp_kses_post( '<span>25%<br>Off</span> SHOP NOW' ); ?></p>
							</div>
							<div class="homepage-setup-theme-buynow-two">
								<img src="<?php echo esc_url( get_template_directory_uri() . '/inc/homepage-setup/assets/homepage-setup-images/boat-rental.png' ); ?>" alt="<?php echo esc_attr__( 'Theme Bundle Image', 'boat-rental' ); ?>">
							</div>
							<div class="homepage-setup-theme-buynow-three">
								<p><?php echo wp_kses_post( 'Get <span>25% OFF</span> on Boat Rental WordPress Theme Use code <span>"NYTHEMES25"</span> at checkout.' ); ?></p>
							</div>
							<div class="homepage-setup-theme-buynow-four">
								<a target="_blank" href="<?php echo esc_url( BOAT_RENTAL_BUY_NOW ); ?>"><?php echo esc_html__( 'Upgrade To Pro With Just $40', 'boat-rental' ); ?></a>
							</div>
						</div>
					</div> <!-- .demo_image -->

				</div> <!-- .demo_content_image -->
			</div> <!-- .whizzie-wrap -->
		</div> <!-- .wrap -->
		<?php
	}


	/**
	 * Set options for the steps
	 * Incorporate any options set by the theme dev
	 * Return the array for the steps
	 * @return Array
	 */
	public function get_steps() {
		$boat_rental_dev_steps = $this->config_steps;
		$boat_rental_steps = array( 
			'plugins' => array(
				'id'			=> 'plugins',
				'title'			=> __( 'Install and Activate Essential Plugins', 'boat-rental' ),
				'icon'			=> 'admin-plugins',
				'view'			=> 'get_step_plugins',
				'callback'		=> 'install_plugins',
				'button_text'	=> __( 'Install Plugins', 'boat-rental' ),
				'can_skip'		=> true
			),
			'widgets' => array(
				'id'			=> 'widgets',
				'title'			=> __( 'Setup Home Page', 'boat-rental' ),
				'icon'			=> 'welcome-widgets-menus',
				'view'			=> 'get_step_widgets',
				'callback'		=> 'boat_rental_install_widgets',
				'button_text'	=> __( 'Start Home Page Setup', 'boat-rental' ),
				'can_skip'		=> false
			),
			'done' => array(
				'id'			=> 'done',
				'title'			=> __( 'Customize Your Site', 'boat-rental' ),
				'icon'			=> 'yes',
				'view'			=> 'get_step_done',
				'callback'		=> ''
			)
		);
		
		// Iterate through each step and replace with dev config values
		if( $boat_rental_dev_steps ) {
			// Configurable elements - these are the only ones the dev can update from homepage-setup-settings.php
			$can_config = array( 'title', 'icon', 'button_text', 'can_skip' );
			foreach( $boat_rental_dev_steps as $boat_rental_dev_step ) {
				// We can only proceed if an ID exists and matches one of our IDs
				if( isset( $boat_rental_dev_step['id'] ) ) {
					$id = $boat_rental_dev_step['id'];
					if( isset( $boat_rental_steps[$id] ) ) {
						foreach( $can_config as $element ) {
							if( isset( $boat_rental_dev_step[$element] ) ) {
								$boat_rental_steps[$id][$element] = $boat_rental_dev_step[$element];
							}
						}
					}
				}
			}
		}
		return $boat_rental_steps;
	}

	/**
	 * Get the content for the plugins step
	 * @return $content Array
	 */
	public function get_step_plugins() {
		$plugins = $this->get_plugins();
		$content = array(); 
		
		// Add plugin name and type at the top
		$content['detail'] = '<div class="plugin-info">';
		$content['detail'] .= '<p><strong>Plugin</strong></p>';
		$content['detail'] .= '<p><strong>Type</strong></p>';
		$content['detail'] .= '</div>';
		
		// The detail element is initially hidden from the user
		$content['detail'] .= '<ul class="whizzie-do-plugins">';
		
		// Add each plugin into a list
		foreach( $plugins['all'] as $slug=>$plugin ) {
			if ( $slug != 'easy-post-views-count') {
				$content['detail'] .= '<li data-slug="' . esc_attr( $slug ) . '">' . esc_html( $plugin['name'] ) . '<span>';
				$keys = array();
				if ( isset( $plugins['install'][ $slug ] ) ) {
					$keys[] = 'Installation';
				}
				if ( isset( $plugins['update'][ $slug ] ) ) {
					$keys[] = 'Update';
				}
				if ( isset( $plugins['activate'][ $slug ] ) ) {
					$keys[] = 'Activation';
				}
				$content['detail'] .= implode( ' and ', $keys ) . ' required';
				$content['detail'] .= '</span></li>';
			}
		}
		
		$content['detail'] .= '</ul>';
		
		return $content;
	}
	
	/**
	 * Print the content for the widgets step
	 * @since 1.1.0
	 */
	public function get_step_widgets() { ?> <?php }
	
	/**
	 * Print the content for the final step
	 */
	public function get_step_done() { ?>
		<div id="boat-rental-demo-setup-guid">
			<div class="customize_div">
				<div class="customize_div finish">
					<div class="customize_div finish btns">
						<h3><?php echo esc_html( 'Your Site Is Ready To View' ); ?></h3>
						<div class="btnsss">
							<a target="_blank" href="<?php echo esc_url( get_home_url() ); ?>" class="button button-primary">
								<?php esc_html_e( 'View Your Site', 'boat-rental' ); ?>
							</a>
							<a target="_blank" href="<?php echo esc_url( admin_url( 'customize.php' ) ); ?>" class="button button-primary">
								<?php esc_html_e( 'Customize Your Site', 'boat-rental' ); ?>
							</a>
							<a href="<?php echo esc_url(admin_url()); ?>" class="button button-primary">
								<?php esc_html_e( 'Finsh', 'boat-rental' ); ?>
							</a>
						</div>
					</div>
					<div class="boat-rental-setup-finish">
						<img src="<?php echo esc_url( get_template_directory_uri() . '/screenshot.png' ); ?>"/>
					</div>
				</div>
			</div>
		</div>
	<?php }

	/**
	 * Get the plugins registered with TGMPA
	 */
	public function get_plugins() {
		$instance = call_user_func( array( get_class( $GLOBALS['tgmpa'] ), 'get_instance' ) );
		$plugins = array(
			'all' 		=> array(),
			'install'	=> array(),
			'update'	=> array(),
			'activate'	=> array()
		);
		foreach( $instance->plugins as $slug=>$plugin ) {
			if( $instance->is_plugin_active( $slug ) && false === $instance->does_plugin_have_update( $slug ) ) {
				// Plugin is installed and up to date
				continue;
			} else {
				$plugins['all'][$slug] = $plugin;
				if( ! $instance->is_plugin_installed( $slug ) ) {
					$plugins['install'][$slug] = $plugin;
				} else {
					if( false !== $instance->does_plugin_have_update( $slug ) ) {
						$plugins['update'][$slug] = $plugin;
					}
					if( $instance->can_plugin_activate( $slug ) ) {
						$plugins['activate'][$slug] = $plugin;
					}
				}
			}
		}
		return $plugins;
	}

	/**
	 * Get the widgets.wie file from the /content folder
	 * @return Mixed	Either the file or false
	 * @since 1.1.0
	 */
	public function has_widget_file() {
		if( file_exists( $this->widget_file_url ) ) {
			return true;
		}
		return false;
	}
	
	public function setup_plugins() {
		if ( ! check_ajax_referer( 'whizzie_nonce', 'wpnonce' ) || empty( $_POST['slug'] ) ) {
			wp_send_json_error( array( 'error' => 1, 'message' => esc_html__( 'No Slug Found','boat-rental' ) ) );
		}
		$json = array();
		// send back some json we use to hit up TGM
		$plugins = $this->get_plugins();
		
		// what are we doing with this plugin?
		foreach ( $plugins['activate'] as $slug => $plugin ) {
			if ( $_POST['slug'] == $slug ) {
				$json = array(
					'url'           => admin_url( $this->tgmpa_url ),
					'plugin'        => array( $slug ),
					'tgmpa-page'    => $this->tgmpa_menu_slug,
					'plugin_status' => 'all',
					'_wpnonce'      => wp_create_nonce( 'bulk-plugins' ),
					'action'        => 'tgmpa-bulk-activate',
					'action2'       => - 1,
					'message'       => esc_html__( 'Activating Plugin','boat-rental' ),
				);
				break;
			}
		}
		foreach ( $plugins['update'] as $slug => $plugin ) {
			if ( $_POST['slug'] == $slug ) {
				$json = array(
					'url'           => admin_url( $this->tgmpa_url ),
					'plugin'        => array( $slug ),
					'tgmpa-page'    => $this->tgmpa_menu_slug,
					'plugin_status' => 'all',
					'_wpnonce'      => wp_create_nonce( 'bulk-plugins' ),
					'action'        => 'tgmpa-bulk-update',
					'action2'       => - 1,
					'message'       => esc_html__( 'Updating Plugin','boat-rental' ),
				);
				break;
			}
		}
		foreach ( $plugins['install'] as $slug => $plugin ) {
			if ( $_POST['slug'] == $slug ) {
				$json = array(
					'url'           => admin_url( $this->tgmpa_url ),
					'plugin'        => array( $slug ),
					'tgmpa-page'    => $this->tgmpa_menu_slug,
					'plugin_status' => 'all',
					'_wpnonce'      => wp_create_nonce( 'bulk-plugins' ),
					'action'        => 'tgmpa-bulk-install',
					'action2'       => - 1,
					'message'       => esc_html__( 'Installing Plugin','boat-rental' ),
				);
				break;
			}
		}
		if ( $json ) {
			$json['hash'] = md5( serialize( $json ) ); // used for checking if duplicates happen, move to next plugin
			wp_send_json( $json );
		} else {
			wp_send_json( array( 'done' => 1, 'message' => esc_html__( 'Success','boat-rental' ) ) );
		}
		exit;
	}


	public function boat_rental_customizer_nav_menu() {

		/* -+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+- Boat Rental Primary Menu -+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-*/

		$boat_rental_themename = 'Boat Rental';
		$boat_rental_menuname = $boat_rental_themename . ' Primary Menu';
		$boat_rental_menulocation = 'boat-rental-primary-menu';
		$boat_rental_menu_exists = wp_get_nav_menu_object($boat_rental_menuname);

		if (!$boat_rental_menu_exists) {
			$boat_rental_menu_id = wp_create_nav_menu($boat_rental_menuname);

			// Home
			wp_update_nav_menu_item($boat_rental_menu_id, 0, array(
				'menu-item-title' => __('Home', 'boat-rental'),
				'menu-item-classes' => 'home',
				'menu-item-url' => home_url('/'),
				'menu-item-status' => 'publish'
			));

			// About
			$boat_rental_page_about = get_page_by_path('about');
			if($boat_rental_page_about){
				wp_update_nav_menu_item($boat_rental_menu_id, 0, array(
					'menu-item-title' => __('About', 'boat-rental'),
					'menu-item-classes' => 'about',
					'menu-item-url' => get_permalink($boat_rental_page_about),
					'menu-item-status' => 'publish'
				));
			}

			// Services
			$boat_rental_page_services = get_page_by_path('services');
			if($boat_rental_page_services){
				wp_update_nav_menu_item($boat_rental_menu_id, 0, array(
					'menu-item-title' => __('Services', 'boat-rental'),
					'menu-item-classes' => 'services',
					'menu-item-url' => get_permalink($boat_rental_page_services),
					'menu-item-status' => 'publish'
				));
			}

			// Shop Page (WooCommerce)
			if (class_exists('WooCommerce')) {
				$boat_rental_shop_page_id = wc_get_page_id('shop');
				if ($boat_rental_shop_page_id) {
					wp_update_nav_menu_item($boat_rental_menu_id, 0, array(
						'menu-item-title' => __('Shop', 'boat-rental'),
						'menu-item-classes' => 'shop',
						'menu-item-url' => get_permalink($boat_rental_shop_page_id),
						'menu-item-status' => 'publish'
					));
				}
			}

			// Blog
			$boat_rental_page_blog = get_page_by_path('blog');
			if($boat_rental_page_blog){
				wp_update_nav_menu_item($boat_rental_menu_id, 0, array(
					'menu-item-title' => __('Blog', 'boat-rental'),
					'menu-item-classes' => 'blog',
					'menu-item-url' => get_permalink($boat_rental_page_blog),
					'menu-item-status' => 'publish'
				));
			}

			// 404 Page
			$boat_rental_notfound = get_page_by_path('404 Page');
			if($boat_rental_notfound){
				wp_update_nav_menu_item($boat_rental_menu_id, 0, array(
					'menu-item-title' => __('404 Page', 'boat-rental'),
					'menu-item-classes' => '404',
					'menu-item-url' => get_permalink($boat_rental_notfound),
					'menu-item-status' => 'publish'
				));
			}

			if (!has_nav_menu($boat_rental_menulocation)) {
				$boat_rental_locations = get_theme_mod('nav_menu_locations');
				$boat_rental_locations[$boat_rental_menulocation] = $boat_rental_menu_id;
				set_theme_mod('nav_menu_locations', $boat_rental_locations);
			}
		}
	}

	
	/**
	 * Imports the Demo Content
	 * @since 1.1.0
	 */
	public function boat_rental_setup_widgets(){

		/* -+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+- MENUS PAGES -+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-*/
		
			// Creation of home page //
			$boat_rental_home_content = '';
			$boat_rental_home_title = 'Home';
			$boat_rental_home = array(
					'post_type' => 'page',
					'post_title' => $boat_rental_home_title,
					'post_content'  => $boat_rental_home_content,
					'post_status' => 'publish',
					'post_author' => 1,
					'post_slug' => 'home'
			);
			$boat_rental_home_id = wp_insert_post($boat_rental_home);

			add_post_meta( $boat_rental_home_id, '_wp_page_template', 'frontpage.php' );

			$boat_rental_home = get_page_by_path( 'Home' );
			update_option( 'page_on_front', $boat_rental_home->ID );
			update_option( 'show_on_front', 'page' );

			// Creation of blog page //
			$boat_rental_blog_title = 'Blog';
			$boat_rental_blog_check = get_page_by_path('blog');
			if (!$boat_rental_blog_check) {
				$boat_rental_blog = array(
					'post_type'    => 'page',
					'post_title'   => $boat_rental_blog_title,
					'post_status'  => 'publish',
					'post_author'  => 1,
					'post_name'    => 'blog'
				);
				$boat_rental_blog_id = wp_insert_post($boat_rental_blog);

				if (!is_wp_error($boat_rental_blog_id)) {
					update_option('page_for_posts', $boat_rental_blog_id);
				}
			}

			// Creation of about page //
			$boat_rental_about_title = 'About';
			$boat_rental_about_content = 'What is Lorem Ipsum?
														Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industrys standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
														&nbsp;
														Why do we use it?
														It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using Content here, content here, making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for lorem ipsum will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).
														&nbsp;
														Where does it come from?
														There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which dont look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isnt anything embarrassing hidden in the middle of text. All the Lorem Ipsum generators on the Internet tend to repeat predefined chunks as necessary, making this the first true generator on the Internet. It uses a dictionary of over 200 Latin words, combined with a handful of model sentence structures, to generate Lorem Ipsum which looks reasonable. The generated Lorem Ipsum is therefore always free from repetition, injected humour, or non-characteristic words etc.
														&nbsp;
														Why do we use it?
														It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using Content here, content here, making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for lorem ipsum will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).
														&nbsp;
														Where does it come from?
														There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which dont look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isnt anything embarrassing hidden in the middle of text. All the Lorem Ipsum generators on the Internet tend to repeat predefined chunks as necessary, making this the first true generator on the Internet. It uses a dictionary of over 200 Latin words, combined with a handful of model sentence structures, to generate Lorem Ipsum which looks reasonable. The generated Lorem Ipsum is therefore always free from repetition, injected humour, or non-characteristic words etc.';
			$boat_rental_about_check = get_page_by_path('about');
			if (!$boat_rental_about_check) {
				$boat_rental_about = array(
					'post_type'    => 'page',
					'post_title'   => $boat_rental_about_title,
					'post_content'   => $boat_rental_about_content,
					'post_status'  => 'publish',
					'post_author'  => 1,
					'post_name'    => 'about'
				);
				wp_insert_post($boat_rental_about);
			}

			// Creation of services page //
			$boat_rental_services_title = 'Services';
			$boat_rental_services_content = 'What is Lorem Ipsum?
														Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industrys standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
														&nbsp;
														Why do we use it?
														It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using Content here, content here, making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for lorem ipsum will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).
														&nbsp;
														Where does it come from?
														There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which dont look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isnt anything embarrassing hidden in the middle of text. All the Lorem Ipsum generators on the Internet tend to repeat predefined chunks as necessary, making this the first true generator on the Internet. It uses a dictionary of over 200 Latin words, combined with a handful of model sentence structures, to generate Lorem Ipsum which looks reasonable. The generated Lorem Ipsum is therefore always free from repetition, injected humour, or non-characteristic words etc.
														&nbsp;
														Why do we use it?
														It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using Content here, content here, making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for lorem ipsum will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).
														&nbsp;
														Where does it come from?
														There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which dont look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isnt anything embarrassing hidden in the middle of text. All the Lorem Ipsum generators on the Internet tend to repeat predefined chunks as necessary, making this the first true generator on the Internet. It uses a dictionary of over 200 Latin words, combined with a handful of model sentence structures, to generate Lorem Ipsum which looks reasonable. The generated Lorem Ipsum is therefore always free from repetition, injected humour, or non-characteristic words etc.';
			$boat_rental_services_check = get_page_by_path('services');
			if (!$boat_rental_services_check) {
				$boat_rental_services = array(
					'post_type'    => 'page',
					'post_title'   => $boat_rental_services_title,
					'post_content'   => $boat_rental_services_content,
					'post_status'  => 'publish',
					'post_author'  => 1,
					'post_name'    => 'services'
				);
				wp_insert_post($boat_rental_services);
			}

			// Creation of 404 page //
			$boat_rental_notfound_title = '404 Page';
			$boat_rental_notfound = array(
				'post_type'   => 'page',
				'post_title'  => $boat_rental_notfound_title,
				'post_status' => 'publish',
				'post_author' => 1,
				'post_slug'   => '404'
			);
			$boat_rental_notfound_id = wp_insert_post($boat_rental_notfound);
			add_post_meta($boat_rental_notfound_id, '_wp_page_template', '404.php');


		/* -+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+- SLIDER POST -+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-*/

			$boat_rental_slider_title = array('Rent Your Perfect Boat');
			for($boat_rental_i=1;$boat_rental_i<=1;$boat_rental_i++){

				$boat_rental_title = $boat_rental_slider_title[$boat_rental_i-1];
				$boat_rental_content = 'Affordable boat rentals for every adventure on water.';

				// Create post object
				$boat_rental_my_post = array(
						'post_title'    => wp_strip_all_tags( $boat_rental_title ),
						'post_content'  => $boat_rental_content,
						'post_status'   => 'publish',
						'post_type'     => 'post',
				);
				// Insert the post into the database
				$boat_rental_post_id = wp_insert_post( $boat_rental_my_post );

				wp_set_object_terms($boat_rental_post_id, 'Car', 'category', true);

				wp_set_object_terms($boat_rental_post_id, 'Car', 'post_tag', true);

				$boat_rental_image_url = get_template_directory_uri().'/inc/homepage-setup/assets/homepage-setup-images/banner.png';

				$boat_rental_image_name= 'banner.png';
				$upload_dir       = wp_upload_dir();
				// Set upload folder
				$boat_rental_image_data       = file_get_contents($boat_rental_image_url);
				// Get image data
				$unique_file_name = wp_unique_filename( $upload_dir['path'], $boat_rental_image_name );

				$boat_rental_filename = basename( $unique_file_name ); 
				
				// Check folder permission and define file location
				if( wp_mkdir_p( $upload_dir['path'] ) ) {
						$boat_rental_file = $upload_dir['path'] . '/' . $boat_rental_filename;
				} else {
						$boat_rental_file = $upload_dir['basedir'] . '/' . $boat_rental_filename;
				}
				// Create the image  file on the server
				// Generate unique name
				if ( ! function_exists( 'WP_Filesystem' ) ) {
					require_once( ABSPATH . 'wp-admin/includes/file.php' );
				}
				
				WP_Filesystem();
				global $wp_filesystem;
				
				if ( ! $wp_filesystem->put_contents( $boat_rental_file, $boat_rental_image_data, FS_CHMOD_FILE ) ) {
					wp_die( 'Error saving file!' );
				}
				// Check image file type
				$wp_filetype = wp_check_filetype( $boat_rental_filename, null );
				// Set attachment data
				$boat_rental_attachment = array(
						'post_mime_type' => $wp_filetype['type'],
						'post_title'     => sanitize_file_name( $boat_rental_filename ),
						'post_content'   => '',
						'post_type'     => 'post',
						'post_status'    => 'inherit'
				);
				// Create the attachment
				$boat_rental_attach_id = wp_insert_attachment( $boat_rental_attachment, $boat_rental_file, $boat_rental_post_id );
				// Include image.php
				require_once(ABSPATH . 'wp-admin/includes/image.php');
				// Define attachment metadata
				$boat_rental_attach_data = wp_generate_attachment_metadata( $boat_rental_attach_id, $boat_rental_file );
				// Assign metadata to attachment
					wp_update_attachment_metadata( $boat_rental_attach_id, $boat_rental_attach_data );
				// And finally assign featured image to post
				set_post_thumbnail( $boat_rental_post_id, $boat_rental_attach_id );

			}

			    $boat_rental_title = "Search Form";
			    $boat_rental_content = '[email email-641 placeholder "Email Id"][text Pick-up-location class:Pick-up-location placeholder "Pick-up-location"][date date-859 "25/10/2025"] [text Drop-of location class:Pick-up-location placeholder "Drop-of location"][submit "Send a Request"]"
				    [_site_title] <<EMAIL>>
				    From: [your-name] <[your-email]>
				    Subject: [your-subject]

				    Message Body:
				    [your-message]

				    --
				    This e-mail was sent from a contact form on [_site_title] ([_site_url])
				    [_site_admin_email]
				    Reply-To: [your-email]

				    0
				    0

				    [_site_title] "[your-subject]"
				    [_site_title] <<EMAIL>>
				    Message Body:
				    [your-message]

				    --
				    This e-mail was sent from a contact form on [_site_title] ([_site_url])
				    [your-email]
				    Reply-To: [_site_admin_email]

				    0
				    0
				    Thank you for your message. It has been sent.
				    There was an error trying to send your message. Please try again later.
				    One or more fields have an error. Please check and try again.
				    There was an error trying to send your message. Please try again later.
				    You must accept the terms and conditions before sending your message.
				    The field is required.
				    The field is too long.
				    The field is too short.
				    There was an unknown error uploading the file.
				    You are not allowed to upload files of this type.
				    The file is too big.
				    There was an error uploading the file.';

			    $boat_rental_post = array(
				    'post_title'    => wp_strip_all_tags( $boat_rental_title ),
				    'post_content'  => $boat_rental_content,
				    'post_status'   => 'publish',
				    'post_type'     => 'wpcf7_contact_form',
			    );
			    // Insert the post into the database
			    $boat_rental_post_id = wp_insert_post( $boat_rental_post );

			    add_post_meta($boat_rental_post_id, "_form", '[email email-641 placeholder "Email Id"][text Pick-up-location class:Pick-up-location placeholder "Pick-up-location"][date date-859 "25/10/2025"] [text Drop-of location class:Pick-up-location placeholder "Drop-of location"][submit "Send a Request"]');

			    $boat_rental_mail_data  = array('subject' => '[_site_title] "[your-subject]"',
			        'sender' => '[_site_title] <<EMAIL>>',
			        'body' => 'From: [your-name] <[your-email]>
			    	Subject: [your-subject]

				    Message Body:
				    [your-message]

				    --
				    This e-mail was sent from a contact form on [_site_title] ([_site_url])',
				        'recipient' => '[_site_admin_email]',
				        'additional_headers' => 'Reply-To: [your-email]',
				        'attachments' => '',
				        'use_html' => 0,
				        'exclude_blank' => 0 );

			    add_post_meta($boat_rental_post_id, "_mail", $boat_rental_mail_data);
			  	// Gets term object from Tree in the database.

			    $boat_rental_shortcode = '[contact-form-7 id="'.$boat_rental_post_id.'" title="'.$boat_rental_title.'"]';

			    set_theme_mod( 'boat_rental_contact_form_shortcode',$boat_rental_shortcode );


			    //--------------------------------------------boats Section---------------------------
				

				// Create post object
				$boat_rental_my_post = array(
						'post_title'    => wp_strip_all_tags( 'Boats Rental' ),
						'post_content'  => $boat_rental_content,
						'post_status'   => 'publish',
						'post_type'     => 'simple_cpt',
				);
				// Insert the post into the database
				$boat_rental_post_id = wp_insert_post( $boat_rental_my_post );

				update_post_meta($boat_rental_post_id, "simple_cpt_name", "boatsrental");
				update_post_meta($boat_rental_post_id, "simple_cpt_label", "Boats Rental");
				update_post_meta($boat_rental_post_id, "simple_cpt_singular_name", "Boats Rental");
				update_post_meta($boat_rental_post_id, "simple_cpt_description", "");
				update_post_meta($boat_rental_post_id, "simple_cpt_icon_slug", "");
				update_post_meta($boat_rental_post_id, "simple_cpt_public", "1");
				update_post_meta($boat_rental_post_id, "simple_cpt_show_ui", "1");
				update_post_meta($boat_rental_post_id, "simple_cpt_has_archive", "0");
				update_post_meta($boat_rental_post_id, "simple_cpt_exclude_from_search", "0");
				update_post_meta($boat_rental_post_id, "simple_cpt_capability_type", "post");
				update_post_meta($boat_rental_post_id, "simple_cpt_hierarchical", "0");
				update_post_meta($boat_rental_post_id, "simple_cpt_rewrite", "1");
				update_post_meta($boat_rental_post_id, "simple_cpt_withfront", "1");
				update_post_meta($boat_rental_post_id, "simple_cpt_feeds", "0");
				update_post_meta($boat_rental_post_id, "simple_cpt_show_in_menu", "1");
				update_post_meta($boat_rental_post_id, "simple_cpt_pages", "1");
				update_post_meta($boat_rental_post_id, "simple_cpt_custom_rewrite_slug", "");
				update_post_meta($boat_rental_post_id, "simple_cpt_query_var", "1");
				update_post_meta($boat_rental_post_id, "simple_cpt_show_in_rest", "1");
				update_post_meta($boat_rental_post_id, "simple_cpt_publicly_queryable", "1");
				update_post_meta($boat_rental_post_id, "simple_cpt_menu_position", "");
				update_post_meta($boat_rental_post_id, "simple_cpt_supports", array(
				    'title',
				    'editor',
				    'excerpt',
				    'custom-fields',
				    'comments',
				    'revisions',
				    'thumbnail',
				    'author',
				    'page-attributes',
				    'post-formats'
				));
				update_post_meta($boat_rental_post_id, "simple_cpt_builtin_tax", array());
				update_post_meta($boat_rental_post_id, "simple_cpt_tax_post_types", array());


				// Create post object
				$boat_rental_my_post = array(
						'post_title'    => wp_strip_all_tags( 'Boat Taxonomy' ),
						'post_content'  => $boat_rental_content,
						'post_status'   => 'publish',
						'post_type'     => 'simple_tax',
				);
				// Insert the post into the database
				$boat_rental_post_id = wp_insert_post( $boat_rental_my_post );

				update_post_meta($boat_rental_post_id, "simple_cpt_supports", array());
				update_post_meta($boat_rental_post_id, "simple_cpt_builtin_tax", array());
				update_post_meta($boat_rental_post_id, "simple_cpt_tax_name", "boattaxonomy");
				update_post_meta($boat_rental_post_id, "simple_cpt_tax_label", "");
				update_post_meta($boat_rental_post_id, "simple_cpt_tax_singular_name", "Boat Taxonomy");
				update_post_meta($boat_rental_post_id, "simple_cpt_tax_show_ui", "1");
				update_post_meta($boat_rental_post_id, "simple_cpt_tax_hierarchical", "0");
				update_post_meta($boat_rental_post_id, "simple_cpt_tax_rewrite", "1");
				update_post_meta($boat_rental_post_id, "simple_cpt_tax_custom_rewrite_slug", " ");
				update_post_meta($boat_rental_post_id, "simple_cpt_tax_query_var", "1");
				update_post_meta($boat_rental_post_id, "simple_cpt_tax_show_in_rest", "1");
				update_post_meta($boat_rental_post_id, "simple_cpt_tax_show_admin_column", "1");
				update_post_meta($boat_rental_post_id, "simple_cpt_tax_post_types", array(
				    'boatsrental'
				));

				//-----------------------------------------------------------------------

				

			    $content = array(
				    "location" => array(
				        array(
				            array(
				                "param" => "post_type",
				                "operator" => "==",
				                "value" => "boatsrental"
				            )
				        )
				    ),
				    "position" => "normal",
				    "style" => "default",
				    "label_placement" => "top",
				    "instruction_placement" => "label",
				    "hide_on_screen" => "",
				    "description" => "",
				    "show_in_rest" => 0
				);

			    $my_post = array(
				    'post_title'    => wp_strip_all_tags( 'Boat Features Main' ),
				    'post_content'  => maybe_serialize($content),
				    'post_status'   => 'publish',
				    'post_type'     => 'acf-field-group',
				);

				$acf_post_id    = wp_insert_post($my_post);

				$content_tour = array(
				    "aria-label" => "",
				    "type" => "text",
				    "instructions" => "",
				    "required" => 0,
				    "conditional_logic" => 0,
				    "wrapper" => array(
				        "width" => "",
				        "class" => "",
				        "id" => ""
				    ),
				    "default_value" => "",
				    "maxlength" => "",
				    "allow_in_bindings" => 0,
				    "placeholder" => "",
				    "prepend" => "",
				    "append" => "",
				);

				$boats_content = array( 'Boat Capacity','Boat Engine','Boat Features','Fuel Type' );
				for ($i=1; $i <=4 ; $i++) { 

					$my_post = array(
					    'post_title'    => wp_strip_all_tags( $boats_content[$i - 1] ),
					    'post_content'  => maybe_serialize($content_tour),
					    'post_status'   => 'publish',
					    'post_type'     => 'acf-field',
					    'post_excerpt'  => strtolower(str_replace(' ', '_', $boats_content[$i - 1])),
					    'post_parent'   => $acf_post_id
					);

					$post_id    = wp_insert_post($my_post);

				}
        
        $this->boat_rental_customizer_nav_menu();

	    exit;
	}
}