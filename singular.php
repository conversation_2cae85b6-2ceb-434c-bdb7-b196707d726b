<?php
/**
 * The template for displaying single posts and pages.
 * @package Boat Rental
 * @since 1.0.0
 */

get_header();

$boat_rental_default = boat_rental_get_default_theme_options();
$boat_rental_global_layout = get_theme_mod('boat_rental_global_sidebar_layout', $boat_rental_default['boat_rental_global_sidebar_layout']);
$boat_rental_page_layout = get_theme_mod('boat_rental_page_sidebar_layout', $boat_rental_global_layout);
$boat_rental_post_layout = get_theme_mod('boat_rental_post_sidebar_layout', $boat_rental_global_layout);
$boat_rental_post_meta = get_post_meta(get_the_ID(), 'boat_rental_post_sidebar_option', true);

$boat_rental_final_layout = $boat_rental_global_layout;
if (!empty($boat_rental_post_meta) && $boat_rental_post_meta !== 'default') {
    $boat_rental_final_layout = $boat_rental_post_meta;
} elseif (is_page() || (function_exists('is_shop') && is_shop())) {
    $boat_rental_final_layout = $boat_rental_page_layout;
} elseif (is_single()) {
    $boat_rental_final_layout = $boat_rental_post_layout;
}

// Set content column order based on sidebar layout
$boat_rental_sidebar_column_class = 'column-order-1';
if ($boat_rental_final_layout === 'left-sidebar') {
    $boat_rental_sidebar_column_class = 'column-order-2';
}

?>

<div id="single-page" class="singular-main-block">
    <div class="wrapper">
        <div class="column-row <?php echo esc_attr($boat_rental_final_layout === 'no-sidebar' ? 'no-sidebar-layout' : ''); ?>">

            <?php if ($boat_rental_final_layout === 'left-sidebar') : ?>
                <?php get_sidebar(); ?>
            <?php endif; ?>

            <div id="primary" class="content-area <?php echo esc_attr($boat_rental_final_layout === 'no-sidebar' ? 'full-width-content' : $boat_rental_sidebar_column_class); ?>">
                <main id="site-content" role="main">

                    <?php
                    boat_rental_breadcrumb(); // Display breadcrumb

                    if (have_posts()) : ?>

                        <div class="article-wraper">
                            <?php while (have_posts()) : the_post(); ?>

                                <?php get_template_part('template-parts/content', 'single'); ?>

                                <?php if ((is_single() || is_page()) && (comments_open() || get_comments_number()) && !post_password_required()) : ?>
                                    <div class="comments-wrapper">
                                        <?php comments_template(); ?>
                                    </div>
                                <?php endif; ?>

                            <?php endwhile; ?>
                        </div>

                    <?php else : ?>

                        <?php get_template_part('template-parts/content', 'none'); ?>

                    <?php endif;

                    do_action('boat_rental_navigation_action');
                    ?>

                </main>
            </div>

            <?php if ($boat_rental_final_layout === 'right-sidebar') : ?>
                <?php get_sidebar(); ?>
            <?php endif; ?>

        </div>
    </div>
</div>

<?php get_footer(); ?>