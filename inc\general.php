<?php

function boat_rental_enqueue_fonts() {
    $boat_rental_default_font_content = 'jost';
    $boat_rental_default_font_heading = 'saira';

    $boat_rental_font_content = esc_attr(get_theme_mod('boat_rental_content_typography_font', $boat_rental_default_font_content));
    $boat_rental_font_heading = esc_attr(get_theme_mod('boat_rental_heading_typography_font', $boat_rental_default_font_heading));

    $boat_rental_css = '';

    // Always enqueue main font
    $boat_rental_css .= '
    :root {
        --font-main: ' . $boat_rental_font_content . ', ' . (in_array($boat_rental_font_content, ['bitter', 'charis-sil']) ? 'serif' : 'sans-serif') . '!important;
    }';
    wp_enqueue_style('boat-rental-style-font-general', get_template_directory_uri() . '/fonts/' . $boat_rental_font_content . '/font.css');

    // Always enqueue header font
    $boat_rental_css .= '
    :root {
        --font-head: ' . $boat_rental_font_heading . ', ' . (in_array($boat_rental_font_heading, ['bitter', 'charis-sil']) ? 'serif' : 'sans-serif') . '!important;
    }';
    wp_enqueue_style('boat-rental-style-font-h', get_template_directory_uri() . '/fonts/' . $boat_rental_font_heading . '/font.css');

    // Add inline style
    wp_add_inline_style('boat-rental-style-font-general', $boat_rental_css);
}
add_action('wp_enqueue_scripts', 'boat_rental_enqueue_fonts', 50);