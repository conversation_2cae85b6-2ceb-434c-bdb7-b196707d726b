<?php

$boat_rental_custom_css = "";

	$boat_rental_theme_pagination_options_alignment = get_theme_mod('boat_rental_theme_pagination_options_alignment', 'Center');
	if ($boat_rental_theme_pagination_options_alignment == 'Center') {
		$boat_rental_custom_css .= '.navigation.pagination,.navigation.posts-navigation .nav-links{';
		$boat_rental_custom_css .= 'justify-content: center;margin: 0 auto;';
		$boat_rental_custom_css .= '}';
	} else if ($boat_rental_theme_pagination_options_alignment == 'Right') {
		$boat_rental_custom_css .= '.navigation.pagination,.navigation.posts-navigation .nav-links{';
		$boat_rental_custom_css .= 'justify-content: right;margin: 0 0 0 auto;';
		$boat_rental_custom_css .= '}';
	} else if ($boat_rental_theme_pagination_options_alignment == 'Left') {
		$boat_rental_custom_css .= '.navigation.pagination,.navigation.posts-navigation .nav-links{';
		$boat_rental_custom_css .= 'justify-content: left;margin: 0 auto 0 0;';
		$boat_rental_custom_css .= '}';
	}

	$boat_rental_theme_breadcrumb_enable = get_theme_mod('boat_rental_theme_breadcrumb_enable',true);
    if($boat_rental_theme_breadcrumb_enable != true){
        $boat_rental_custom_css .='nav.breadcrumb-trail.breadcrumbs,nav.woocommerce-breadcrumb{';
            $boat_rental_custom_css .='display: none;';
        $boat_rental_custom_css .='}';
    }

	$boat_rental_theme_breadcrumb_options_alignment = get_theme_mod('boat_rental_theme_breadcrumb_options_alignment', 'Left');
	if ($boat_rental_theme_breadcrumb_options_alignment == 'Center') {
	    $boat_rental_custom_css .= '.breadcrumbs ul,nav.woocommerce-breadcrumb{';
	    $boat_rental_custom_css .= 'text-align: center !important;';
	    $boat_rental_custom_css .= '}';
	} else if ($boat_rental_theme_breadcrumb_options_alignment == 'Right') {
	    $boat_rental_custom_css .= '.breadcrumbs ul,nav.woocommerce-breadcrumb{';
	    $boat_rental_custom_css .= 'text-align: Right !important;';
	    $boat_rental_custom_css .= '}';
	} else if ($boat_rental_theme_breadcrumb_options_alignment == 'Left') {
	    $boat_rental_custom_css .= '.breadcrumbs ul,nav.woocommerce-breadcrumb{';
	    $boat_rental_custom_css .= 'text-align: Left !important;';
	    $boat_rental_custom_css .= '}';
	}

	$boat_rental_single_page_content_alignment = get_theme_mod('boat_rental_single_page_content_alignment', 'left');
	if ($boat_rental_single_page_content_alignment == 'left') {
	    $boat_rental_custom_css .= '#single-page .type-page,section.theme-custom-block.theme-error-sectiontheme-error-section.error-block-middle,section.theme-custom-block.theme-error-section.error-block-heading .theme-area-header{';
	    $boat_rental_custom_css .= 'text-align: left !important;';
	    $boat_rental_custom_css .= '}';
	} else if ($boat_rental_single_page_content_alignment == 'center') {
	    $boat_rental_custom_css .= '#single-page .type-page,section.theme-custom-block.theme-error-sectiontheme-error-section.error-block-middle,section.theme-custom-block.theme-error-section.error-block-heading .theme-area-header{';
	    $boat_rental_custom_css .= 'text-align: center !important;';
	    $boat_rental_custom_css .= '}';
	} else if ($boat_rental_single_page_content_alignment == 'right') {
	    $boat_rental_custom_css .= '#single-page .type-page,section.theme-custom-block.theme-error-sectiontheme-error-section.error-block-middle,section.theme-custom-block.theme-error-section.error-block-heading .theme-area-header{';
	    $boat_rental_custom_css .= 'text-align: right !important;';
	    $boat_rental_custom_css .= '}';
	}

	$boat_rental_single_post_content_alignment = get_theme_mod('boat_rental_single_post_content_alignment', 'left');
	if ($boat_rental_single_post_content_alignment == 'left') {
	    $boat_rental_custom_css .= '#single-page .type-post,#single-page .type-post .entry-meta,#single-page .type-post .is-layout-flex{';
	    $boat_rental_custom_css .= 'text-align: left !important;justify-content: left;';
	    $boat_rental_custom_css .= '}';
	} else if ($boat_rental_single_post_content_alignment == 'center') {
	    $boat_rental_custom_css .= '#single-page .type-post,#single-page .type-post .entry-meta,#single-page .type-post .is-layout-flex{';
	    $boat_rental_custom_css .= 'text-align: center !important;justify-content: center;';
	    $boat_rental_custom_css .= '}';
	} else if ($boat_rental_single_post_content_alignment == 'right') {
	    $boat_rental_custom_css .= '#single-page .type-post,#single-page .type-post .entry-meta,#single-page .type-post .is-layout-flex{';
	    $boat_rental_custom_css .= 'text-align: right !important;justify-content: right;';
	    $boat_rental_custom_css .= '}';
	}

	$boat_rental_menu_text_transform = get_theme_mod('boat_rental_menu_text_transform', 'Capitalize');
	if ($boat_rental_menu_text_transform == 'Capitalize') {
	    $boat_rental_custom_css .= '.site-navigation .primary-menu > li a{';
	    $boat_rental_custom_css .= 'text-transform: Capitalize !important;';
	    $boat_rental_custom_css .= '}';
	} else if ($boat_rental_menu_text_transform == 'uppercase') {
	    $boat_rental_custom_css .= '.site-navigation .primary-menu > li a{';
	    $boat_rental_custom_css .= 'text-transform: uppercase !important;';
	    $boat_rental_custom_css .= '}';
	} else if ($boat_rental_menu_text_transform == 'lowercase') {
	    $boat_rental_custom_css .= '.site-navigation .primary-menu > li a{';
	    $boat_rental_custom_css .= 'text-transform: lowercase !important;';
	    $boat_rental_custom_css .= '}';
	}

	$boat_rental_footer_widget_title_alignment = get_theme_mod('boat_rental_footer_widget_title_alignment', 'left');
	if ($boat_rental_footer_widget_title_alignment == 'left') {
	    $boat_rental_custom_css .= 'h2.widget-title{';
	    $boat_rental_custom_css .= 'text-align: left !important;';
	    $boat_rental_custom_css .= '}';
	} else if ($boat_rental_footer_widget_title_alignment == 'center') {
	    $boat_rental_custom_css .= 'h2.widget-title{';
	    $boat_rental_custom_css .= 'text-align: center !important;';
	    $boat_rental_custom_css .= '}';
	} else if ($boat_rental_footer_widget_title_alignment == 'right') {
	    $boat_rental_custom_css .= 'h2.widget-title{';
	    $boat_rental_custom_css .= 'text-align: right !important;';
	    $boat_rental_custom_css .= '}';
	}

    $boat_rental_show_hide_related_product = get_theme_mod('boat_rental_show_hide_related_product',true);
    if($boat_rental_show_hide_related_product != true){
        $boat_rental_custom_css .='.related.products{';
            $boat_rental_custom_css .='display: none;';
        $boat_rental_custom_css .='}';
    }

	$boat_rental_sticky_sidebar = get_theme_mod('boat_rental_sticky_sidebar',true);
    if($boat_rental_sticky_sidebar != true){
        $boat_rental_custom_css .='.widget-area-wrapper{';
            $boat_rental_custom_css .='position: relative !important;';
        $boat_rental_custom_css .='}';
    }

	/*-------------------- Global First Color -------------------*/

	$boat_rental_global_color = get_theme_mod('boat_rental_global_color', '#0B5394'); // Add a fallback if the color isn't set

	if ($boat_rental_global_color) {
		$boat_rental_custom_css .= ':root {';
		$boat_rental_custom_css .= '--global-color: ' . esc_attr($boat_rental_global_color) . ';';
		$boat_rental_custom_css .= '}';
	}
	
	/*-------------------- Content Font -------------------*/

	$boat_rental_content_typography_font = get_theme_mod('boat_rental_content_typography_font', 'jost'); // Add a fallback if the color isn't set

	if ($boat_rental_content_typography_font) {
		$boat_rental_custom_css .= ':root {';
		$boat_rental_custom_css .= '--font-main: ' . esc_attr($boat_rental_content_typography_font) . ';';
		$boat_rental_custom_css .= '}';
	}

	/*-------------------- Heading Font -------------------*/

	$boat_rental_heading_typography_font = get_theme_mod('boat_rental_heading_typography_font', 'jost'); // Add a fallback if the color isn't set

	if ($boat_rental_heading_typography_font) {
		$boat_rental_custom_css .= ':root {';
		$boat_rental_custom_css .= '--font-head: ' . esc_attr($boat_rental_heading_typography_font) . ';';
		$boat_rental_custom_css .= '}';
	}

	/*-------------------- FOOTER -------------------*/

	$boat_rental_footer_widget_background_color = get_theme_mod('boat_rental_footer_widget_background_color');
    if ($boat_rental_footer_widget_background_color) {

        $boat_rental_custom_css .= "
            .footer-widgetarea {
                background-color: ". esc_attr($boat_rental_footer_widget_background_color) .";
            }
        ";
    }

    $boat_rental_footer_widget_background_image = get_theme_mod('boat_rental_footer_widget_background_image');
	if ($boat_rental_footer_widget_background_image) {
		$boat_rental_custom_css .= "
			.footer-widgetarea {
				background-image: url(" . esc_url($boat_rental_footer_widget_background_image) . ");
			}
		";
	}

    $boat_rental_copyright_font_size = get_theme_mod('boat_rental_copyright_font_size');
    if ($boat_rental_copyright_font_size) {

        $boat_rental_custom_css .= "
            .footer-copyright {
                font-size: ". esc_attr($boat_rental_copyright_font_size) ."px;
            }
        ";
    }

	$boat_rental_columns = get_theme_mod('boat_rental_posts_per_columns', 3);
    $boat_rental_columns = absint($boat_rental_columns);
    if ( $boat_rental_columns < 1 || $boat_rental_columns > 6 ) {
        $boat_rental_columns = 3;
    }
    $boat_rental_custom_css .= "
        .site-content .article-wraper-archive {
            grid-template-columns: repeat({$boat_rental_columns}, 1fr);
        }
    ";

	$boat_rental_copyright_alignment = get_theme_mod( 'boat_rental_copyright_alignment', 'Default' );
	if ( $boat_rental_copyright_alignment === 'Reverse' ) {
		$boat_rental_custom_css .= '.site-info .column-row { flex-direction: row-reverse; }';
		$boat_rental_custom_css .= '.footer-credits { justify-content: flex-end; }';
		$boat_rental_custom_css .= '.footer-copyright { text-align: right; }';
		$boat_rental_custom_css .= '.site-info .column.column-3 { text-align: left; }';
	} elseif ( $boat_rental_copyright_alignment === 'Center' ) {
		$boat_rental_custom_css .= '.site-info .column-row { flex-direction: column; align-items: center; gap: 15px; }';
		$boat_rental_custom_css .= '.footer-credits { justify-content: center; }';
		$boat_rental_custom_css .= '.footer-copyright { text-align: center; }';
		$boat_rental_custom_css .= '.site-info .column.column-3 { text-align: center; }';
	}