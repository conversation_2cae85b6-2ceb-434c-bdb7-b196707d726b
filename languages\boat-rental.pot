# Copyright (C) 2025 Omega Themes
# This file is distributed under the GNU General Public License v2 or later.
msgid ""
msgstr ""
"Project-Id-Version: Boat Rental 1.0.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/theme/boat-rental\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-08-28T07:40:13+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: boat-rental\n"

#. Theme Name of the theme
#: style.css
msgid "Boat Rental"
msgstr ""

#. Theme URI of the theme
#: style.css
msgid "https://www.omegathemes.com/products/boat-rental"
msgstr ""

#. Description of the theme
#: style.css
msgid "The Boat Rental is a multipurpose, modern, and luxurious WordPress theme designed for businesses offering boat rentals, yacht charters, water sports, Boat Rental, Yacht Rental, Luxury Boat Rental, Private Boat Hire, Sailboat Rental, Speed Boat Rental, Fishing Boat Rental, Pontoon Boat Rental, Jet Ski Rental or adventure services. Its minimal, elegant, and sophisticated layout ensures a visually appealing presentation that captivates visitors instantly. Built with clean, secure and clean code, this theme is optimized for performance, guaranteeing a faster page load time and smooth navigation. Whether you’re a developer building client sites or a business owner managing your own, its user-friendly interface and customization options make setup effortless. The theme is responsive and mobile-friendly, ensuring your website looks beautiful and retina-ready on all devices. It includes key sections such as a stunning banner, engaging testimonial section, team showcase, and strategically placed Call to Action (CTA) buttons to boost conversions. Its interactive and animated elements create an engaging experience for users, while Bootstrap integration and shortcodes simplify customization and layout design. With extensive personalization options and a translation-ready framework, this theme is perfect for global audiences. It is agency-friendly, allowing designers to create high-end websites for multiple industries beyond boat rentals, making it a truly multipurpose solution. The addition of social media integration enhances customer engagement, while the theme’s modern and stunning aesthetics establish a professional online presence. Optimized for speed and functionality, the Boat Rental empowers businesses to showcase their services with style and efficiency. Its clean code structure and SEO-friendly design ensure improved visibility and growth. Whether you want a sleek brochure-style site or a dynamic online booking platform, this theme offers unmatched flexibility and a polished, professional look tailored to impress."
msgstr ""

#. Author of the theme
#: style.css
msgid "Omega Themes"
msgstr ""

#. Author URI of the theme
#: style.css
msgid "https://www.omegathemes.com/"
msgstr ""

#: 404.php:38
msgid "Homepage"
msgstr ""

#. translators: %d: ID of a post.
#: classes/class-walker-menu.php:76
msgid "#%d (no title)"
msgstr ""

#: classes/class-walker-menu.php:120
#: inc/custom-functions.php:45
msgid "Show sub menu"
msgstr ""

#. translators: 1: title.
#: comments.php:22
msgid "One thought on &ldquo;%1$s&rdquo;"
msgstr ""

#. translators: 1: comment count number, 2: title.
#: comments.php:28
msgctxt "comments title"
msgid "%1$s thought on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s thoughts on &ldquo;%2$s&rdquo;"
msgstr[0] ""
msgstr[1] ""

#: comments.php:52
msgid "Comments are closed."
msgstr ""

#. Template Name of the theme
msgid "Front Page Template"
msgstr ""

#: functions.php:166
msgid "Primary Menu"
msgstr ""

#: header.php:36
msgid "Skip to the content"
msgstr ""

#: inc/custom-functions.php:162
msgid "-- Select Category --"
msgstr ""

#: inc/custom-functions.php:199
msgid "Previous post:"
msgstr ""

#: inc/custom-functions.php:200
msgid "Next post:"
msgstr ""

#: inc/custom-functions.php:236
msgid "Close"
msgstr ""

#: inc/custom-functions.php:242
#: template-parts/header/header-layout.php:37
msgid "Horizontal"
msgstr ""

#: inc/custom-functions.php:314
msgid "Footer Image"
msgstr ""

#: inc/custom-functions.php:319
msgid "The Boat Rental is a multipurpose, modern, and luxurious WordPress theme designed for businesses offering boat rentals, yacht charters, water sports, Boat Rental, Yacht Rental, Luxury Boat Rental, Private Boat Hire, Sailboat Rental, Speed Boat Rental, Fishing Boat Rental, Pontoon Boat Rental, Jet Ski Rental or adventure services. Its minimal, elegant, and sophisticated layout ensures a visually appealing presentation that captivates visitors instantly. Built with clean, secure and clean code, this theme is optimized for performance, guaranteeing a faster page load time and smooth navigation."
msgstr ""

#: inc/custom-functions.php:326
msgid "Calendar"
msgstr ""

#: inc/custom-functions.php:332
msgid "Enter Keywords Here"
msgstr ""

#: inc/custom-functions.php:380
msgid "Theme: "
msgstr ""

#: inc/custom-functions.php:380
msgid "Boat Rental "
msgstr ""

#: inc/custom-functions.php:380
msgid " By "
msgstr ""

#: inc/custom-functions.php:380
msgid "OMEGA "
msgstr ""

#: inc/custom-functions.php:381
msgid "Powered by "
msgstr ""

#: inc/custom-functions.php:381
msgid "WordPress"
msgstr ""

#: inc/custom-functions.php:381
msgid "WordPress."
msgstr ""

#: inc/custom-functions.php:393
msgid "To the Top"
msgstr ""

#: inc/custom-functions.php:518
msgid "Video"
msgstr ""

#: inc/custom-functions.php:521
msgid "Audio"
msgstr ""

#: inc/custom-functions.php:524
msgid "Gallery"
msgstr ""

#: inc/custom-functions.php:527
msgid "Quote"
msgstr ""

#: inc/custom-functions.php:530
msgid "Image"
msgstr ""

#: inc/custom-functions.php:533
msgid "Link"
msgstr ""

#: inc/custom-functions.php:536
msgid "Status"
msgstr ""

#: inc/custom-functions.php:539
msgid "Aside"
msgstr ""

#: inc/custom-functions.php:542
msgid "Chat"
msgstr ""

#: inc/customizer/404-page-settings.php:12
msgid "404 Page Settings"
msgstr ""

#: inc/customizer/404-page-settings.php:28
msgid "404 Main Title"
msgstr ""

#: inc/customizer/404-page-settings.php:43
msgid "404 Sub Title One"
msgstr ""

#: inc/customizer/404-page-settings.php:58
msgid "404 Para Text One"
msgstr ""

#: inc/customizer/404-page-settings.php:73
msgid "404 Sub Title Two"
msgstr ""

#: inc/customizer/404-page-settings.php:88
msgid "404 Para Text Two"
msgstr ""

#: inc/customizer/additional-woocommerce.php:13
msgid "Additional Woocommerce Options"
msgstr ""

#: inc/customizer/additional-woocommerce.php:29
msgid "Products Per Column"
msgstr ""

#: inc/customizer/additional-woocommerce.php:49
msgid "Products Per Page"
msgstr ""

#: inc/customizer/additional-woocommerce.php:69
msgid "Enable Related Products"
msgstr ""

#: inc/customizer/additional-woocommerce.php:84
msgid "Related Products Per Page"
msgstr ""

#: inc/customizer/additional-woocommerce.php:104
msgid "Related Products Per Row"
msgstr ""

#: inc/customizer/colors.php:21
msgid "Text Color"
msgstr ""

#: inc/customizer/colors.php:39
msgid "Border Color"
msgstr ""

#: inc/customizer/custom-addon.php:10
msgid "Customizer Custom Settings"
msgstr ""

#: inc/customizer/custom-addon.php:26
msgid "Enable Preloader"
msgstr ""

#: inc/customizer/custom-addon.php:44
msgid "Enable Pagination"
msgstr ""

#: inc/customizer/custom-addon.php:60
msgid "Pagination Style"
msgstr ""

#: inc/customizer/custom-addon.php:64
msgid "Numeric (Page Numbers)"
msgstr ""

#: inc/customizer/custom-addon.php:65
msgid "Newer/Older (Previous/Next)"
msgstr ""

#: inc/customizer/custom-addon.php:79
msgid "Pagination Alignment"
msgstr ""

#: inc/customizer/custom-addon.php:83
#: inc/customizer/custom-addon.php:118
#: inc/customizer/footer.php:68
#: inc/customizer/post.php:109
#: inc/customizer/post.php:129
msgid "Center"
msgstr ""

#: inc/customizer/custom-addon.php:84
#: inc/customizer/custom-addon.php:119
#: inc/customizer/footer.php:69
#: inc/customizer/post.php:110
#: inc/customizer/post.php:130
msgid "Right"
msgstr ""

#: inc/customizer/custom-addon.php:85
#: inc/customizer/custom-addon.php:120
#: inc/customizer/footer.php:67
#: inc/customizer/post.php:108
#: inc/customizer/post.php:128
msgid "Left"
msgstr ""

#: inc/customizer/custom-addon.php:99
msgid "Enable Breadcrumb"
msgstr ""

#: inc/customizer/custom-addon.php:114
msgid "Breadcrumb Alignment"
msgstr ""

#: inc/customizer/custom-addon.php:134
msgid "Breadcrumb Font Size"
msgstr ""

#: inc/customizer/customizer.php:32
msgid "Color Options"
msgstr ""

#: inc/customizer/customizer.php:53
msgid "General Settings"
msgstr ""

#: inc/customizer/customizer.php:61
msgid "Color Settings"
msgstr ""

#: inc/customizer/customizer.php:70
msgid "Footer Setting"
msgstr ""

#: inc/customizer/customizer.php:79
msgid "Frontpage Settings"
msgstr ""

#: inc/customizer/customizer.php:88
msgid "Theme Addons"
msgstr ""

#: inc/customizer/customizer.php:97
msgid "Theme Options"
msgstr ""

#: inc/customizer/customizer.php:111
msgid "Logo width"
msgstr ""

#: inc/customizer/customizer.php:112
msgid "Specify the range for logo size with a minimum of 200px and a maximum of 700px, in increments of 20px."
msgstr ""

#: inc/customizer/customizer.php:132
msgid "Boat Rental Pro"
msgstr ""

#: inc/customizer/customizer.php:133
msgid "Upgrade To Pro"
msgstr ""

#: inc/customizer/default.php:19
msgid "Book Now"
msgstr ""

#: inc/customizer/default.php:33
msgid "All rights reserved."
msgstr ""

#: inc/customizer/default.php:61
msgid "Explore Our Wide Range of Boats"
msgstr ""

#: inc/customizer/default.php:66
msgid "To The Top"
msgstr ""

#: inc/customizer/default.php:71
msgid "Oops! That page can’t be found."
msgstr ""

#: inc/customizer/default.php:72
msgid "Maybe it’s out there, somewhere..."
msgstr ""

#: inc/customizer/default.php:73
msgid "You can always find insightful stories on our"
msgstr ""

#: inc/customizer/default.php:74
msgid "Still feeling lost? You’re not alone."
msgstr ""

#: inc/customizer/default.php:75
msgid "Enjoy these stories about getting lost, losing things, and finding what you never knew you were looking for."
msgstr ""

#: inc/customizer/footer.php:12
msgid "Footer Settings"
msgstr ""

#: inc/customizer/footer.php:28
msgid "Enable Footer"
msgstr ""

#: inc/customizer/footer.php:43
msgid "Footer Column Layout"
msgstr ""

#: inc/customizer/footer.php:47
msgid "One Column"
msgstr ""

#: inc/customizer/footer.php:48
msgid "Two Column"
msgstr ""

#: inc/customizer/footer.php:49
msgid "Three Column"
msgstr ""

#: inc/customizer/footer.php:63
msgid "Footer Widget Title Alignment"
msgstr ""

#: inc/customizer/footer.php:83
msgid "Footer Copyright Text"
msgstr ""

#: inc/customizer/footer.php:98
msgid "Copyright Font Size"
msgstr ""

#: inc/customizer/footer.php:116
msgid "Copyright Section Alignment"
msgstr ""

#: inc/customizer/footer.php:120
msgid "Default View"
msgstr ""

#: inc/customizer/footer.php:121
msgid "Reverse View"
msgstr ""

#: inc/customizer/footer.php:122
msgid "Centered Content"
msgstr ""

#: inc/customizer/footer.php:131
msgid "Footer Widget Background Color"
msgstr ""

#: inc/customizer/footer.php:132
msgid "It will change the complete footer widget background color."
msgstr ""

#: inc/customizer/footer.php:142
msgid "Footer Widget Background Image"
msgstr ""

#: inc/customizer/footer.php:155
msgid "Enable To The Top"
msgstr ""

#: inc/customizer/footer.php:170
msgid "Edit Text Here"
msgstr ""

#: inc/customizer/global-color-setting.php:13
msgid "Global Color Settings"
msgstr ""

#: inc/customizer/global-color-setting.php:32
msgid "Global Color"
msgstr ""

#: inc/customizer/header-button.php:13
msgid "Header Settings"
msgstr ""

#: inc/customizer/header-button.php:29
msgid "Enable Sticky Header"
msgstr ""

#: inc/customizer/header-button.php:44
msgid "Menu Font Size"
msgstr ""

#: inc/customizer/header-button.php:64
msgid "Menu Text Transform"
msgstr ""

#: inc/customizer/header-button.php:68
msgid "Capitalize"
msgstr ""

#: inc/customizer/header-button.php:69
msgid "Uppercase"
msgstr ""

#: inc/customizer/header-button.php:70
msgid "Lowercase"
msgstr ""

#: inc/customizer/homepage-content.php:13
msgid "Slider Settings"
msgstr ""

#: inc/customizer/homepage-content.php:27
msgid "Enable / Disable Site Logo"
msgstr ""

#: inc/customizer/homepage-content.php:39
msgid "Enable / Disable Site Title"
msgstr ""

#: inc/customizer/homepage-content.php:54
msgid "Enable / Disable Site Tagline"
msgstr ""

#: inc/customizer/homepage-content.php:69
msgid "Enable Slider"
msgstr ""

#: inc/customizer/homepage-content.php:84
msgid "Slider Post Category"
msgstr ""

#: inc/customizer/homepage-content.php:100
msgid "Form Shortcode "
msgstr ""

#: inc/customizer/homepage-content.php:115
msgid "Explore Our Boats Setting"
msgstr ""

#: inc/customizer/homepage-content.php:131
msgid "Enable Our Boats"
msgstr ""

#: inc/customizer/homepage-content.php:146
msgid "Title "
msgstr ""

#: inc/customizer/homepage-content.php:158
msgid "Our Boats Tab Heading "
msgstr ""

#: inc/customizer/homepage-content.php:184
msgid "Select Category to display Our Boats 1"
msgstr ""

#: inc/customizer/homepage-content.php:207
msgid "Select Category to display Our Boats 2"
msgstr ""

#: inc/customizer/homepage-content.php:230
msgid "Select Category to display Our Boats 3"
msgstr ""

#: inc/customizer/layout-setting.php:13
msgid "Sidebar Settings"
msgstr ""

#: inc/customizer/layout-setting.php:29
msgid "Global Sidebar Layout"
msgstr ""

#: inc/customizer/layout-setting.php:33
#: inc/customizer/layout-setting.php:51
#: inc/customizer/layout-setting.php:68
msgid "Right Sidebar"
msgstr ""

#: inc/customizer/layout-setting.php:34
#: inc/customizer/layout-setting.php:52
#: inc/customizer/layout-setting.php:69
msgid "Left Sidebar"
msgstr ""

#: inc/customizer/layout-setting.php:35
#: inc/customizer/layout-setting.php:53
#: inc/customizer/layout-setting.php:70
msgid "No Sidebar"
msgstr ""

#: inc/customizer/layout-setting.php:47
msgid "Single Page Sidebar Layout"
msgstr ""

#: inc/customizer/layout-setting.php:64
msgid "Single Post Sidebar Layout"
msgstr ""

#: inc/customizer/layout-setting.php:83
msgid "Enable/Disable Sticky Sidebar"
msgstr ""

#: inc/customizer/post.php:13
msgid "Single Meta Information Settings"
msgstr ""

#: inc/customizer/post.php:29
msgid "Enable Single Posts Image"
msgstr ""

#: inc/customizer/post.php:44
msgid "Enable Posts Author"
msgstr ""

#: inc/customizer/post.php:59
msgid "Enable Posts Date"
msgstr ""

#: inc/customizer/post.php:74
#: inc/customizer/post.php:184
msgid "Enable Posts Category"
msgstr ""

#: inc/customizer/post.php:89
msgid "Enable Posts Tags"
msgstr ""

#: inc/customizer/post.php:104
msgid "Single Page Content Alignment"
msgstr ""

#: inc/customizer/post.php:124
msgid "Single Post Content Alignment"
msgstr ""

#: inc/customizer/post.php:138
msgid "Archive Meta Information Settings"
msgstr ""

#: inc/customizer/post.php:154
msgid "Enable Posts Format Icon"
msgstr ""

#: inc/customizer/post.php:169
msgid "Enable Posts Image"
msgstr ""

#: inc/customizer/post.php:199
msgid "Enable Posts Title"
msgstr ""

#: inc/customizer/post.php:214
msgid "Enable Posts Content"
msgstr ""

#: inc/customizer/post.php:229
msgid "Enable Posts Button"
msgstr ""

#: inc/customizer/post.php:244
msgid "Blog Posts Excerpt limit"
msgstr ""

#: inc/customizer/post.php:264
msgid "Blog Posts Image Size"
msgstr ""

#: inc/customizer/post.php:268
msgid "Large Size Image"
msgstr ""

#: inc/customizer/post.php:269
msgid "Big Size Image"
msgstr ""

#: inc/customizer/post.php:270
msgid "Medium Size Image"
msgstr ""

#: inc/customizer/post.php:271
msgid "Small Size Image"
msgstr ""

#: inc/customizer/post.php:272
msgid "Extra Small Size Image"
msgstr ""

#: inc/customizer/post.php:273
msgid "Thumbnail Size Image"
msgstr ""

#: inc/customizer/post.php:287
msgid "Blog Posts Per Column"
msgstr ""

#: inc/customizer/typography.php:13
msgid "Typography Settings"
msgstr ""

#: inc/customizer/typography.php:22
msgid "Default Font"
msgstr ""

#: inc/customizer/typography.php:46
msgid "General Content Font"
msgstr ""

#: inc/customizer/typography.php:60
msgid "General Heading Font"
msgstr ""

#: inc/metabox.php:12
msgid "Global sidebar"
msgstr ""

#: inc/metabox.php:17
msgid "Right sidebar"
msgstr ""

#: inc/metabox.php:22
msgid "Left sidebar"
msgstr ""

#: inc/metabox.php:27
msgid "No sidebar"
msgstr ""

#: inc/pagination.php:26
msgid "&laquo; Newer"
msgstr ""

#: inc/pagination.php:27
msgid "Older &raquo;"
msgstr ""

#: inc/pagination.php:28
#: inc/pagination.php:38
msgid "Posts navigation"
msgstr ""

#: inc/pagination.php:35
msgid "&laquo; Previous"
msgstr ""

#: inc/pagination.php:36
msgid "Next &raquo;"
msgstr ""

#. translators: used between list items, there is a space after the comma
#: inc/template-tags.php:290
msgctxt "list item separator"
msgid ", "
msgstr ""

#. translators: %s: Name of current post. Only visible to screen readers
#: inc/template-tags.php:312
msgid "Edit <span class=\"screen-reader-text\">%s</span>"
msgstr ""

#: inc/widgets/widgets.php:11
msgid "Main Sidebar"
msgstr ""

#: inc/widgets/widgets.php:13
#: inc/widgets/widgets.php:33
msgid "Add widgets here."
msgstr ""

#: inc/widgets/widgets.php:26
msgid "One"
msgstr ""

#: inc/widgets/widgets.php:27
msgid "Two"
msgstr ""

#: inc/widgets/widgets.php:28
msgid "Three"
msgstr ""

#: inc/widgets/widgets.php:31
msgid "Footer Widget "
msgstr ""

#: lib/breadcrumbs/breadcrumbs.php:308
msgid "Browse:"
msgstr ""

#: lib/breadcrumbs/breadcrumbs.php:309
msgctxt "breadcrumbs aria label"
msgid "Breadcrumbs"
msgstr ""

#: lib/breadcrumbs/breadcrumbs.php:310
msgid "Home"
msgstr ""

#: lib/breadcrumbs/breadcrumbs.php:311
msgid "404 Not Found"
msgstr ""

#: lib/breadcrumbs/breadcrumbs.php:312
#: sidebar.php:35
msgid "Archives"
msgstr ""

#. Translators: %s is the search query.
#: lib/breadcrumbs/breadcrumbs.php:314
msgid "Search results for: %s"
msgstr ""

#. Translators: %s is the page number.
#: lib/breadcrumbs/breadcrumbs.php:316
msgid "Page %s"
msgstr ""

#. Translators: %s is the page number.
#: lib/breadcrumbs/breadcrumbs.php:318
msgid "Comment Page %s"
msgstr ""

#. Translators: Minute archive title. %s is the minute time format.
#: lib/breadcrumbs/breadcrumbs.php:320
msgid "Minute %s"
msgstr ""

#. Translators: Weekly archive title. %s is the week date format.
#: lib/breadcrumbs/breadcrumbs.php:322
msgid "Week %s"
msgstr ""

#: lib/breadcrumbs/breadcrumbs.php:782
msgctxt "minute and hour archives time format"
msgid "g:i a"
msgstr ""

#: lib/breadcrumbs/breadcrumbs.php:799
msgctxt "minute archives time format"
msgid "i"
msgstr ""

#: lib/breadcrumbs/breadcrumbs.php:816
msgctxt "hour archives time format"
msgid "g a"
msgstr ""

#: lib/breadcrumbs/breadcrumbs.php:832
#: lib/breadcrumbs/breadcrumbs.php:861
#: lib/breadcrumbs/breadcrumbs.php:888
#: lib/breadcrumbs/breadcrumbs.php:915
#: lib/breadcrumbs/breadcrumbs.php:1255
msgctxt "yearly archives date format"
msgid "Y"
msgstr ""

#: lib/breadcrumbs/breadcrumbs.php:833
#: lib/breadcrumbs/breadcrumbs.php:889
#: lib/breadcrumbs/breadcrumbs.php:1259
msgctxt "monthly archives date format"
msgid "F"
msgstr ""

#: lib/breadcrumbs/breadcrumbs.php:834
#: lib/breadcrumbs/breadcrumbs.php:1263
msgctxt "daily archives date format"
msgid "j"
msgstr ""

#: lib/breadcrumbs/breadcrumbs.php:862
msgctxt "weekly archives date format"
msgid "W"
msgstr ""

#: searchform.php:11
msgid "Search..."
msgstr ""

#: sidebar.php:19
msgid "Search"
msgstr ""

#: sidebar.php:24
msgid "Pages"
msgstr ""

#: sidebar.php:42
msgid "Categories"
msgstr ""

#: sidebar.php:49
msgid "Tags"
msgstr ""

#: sidebar.php:60
msgid "No tags found."
msgstr ""

#: template-parts/content-none.php:11
msgid "Nothing Found"
msgstr ""

#. translators: %1$s: Link to create a new post.
#: template-parts/content-none.php:22
msgid "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr ""

#: template-parts/content-none.php:33
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr ""

#: template-parts/content-none.php:40
msgid "It seems we can’t find what you’re looking for. Perhaps searching can help."
msgstr ""

#: template-parts/content-single.php:27
msgid "Boat Rental Default Image"
msgstr ""

#. translators: %s: Name of current post.
#: template-parts/content-single.php:69
msgid "Read More %s <span class=\"meta-nav\">&rarr;</span>"
msgstr ""

#: template-parts/content-single.php:74
#: template-parts/content.php:95
msgid "Pages:"
msgstr ""

#: template-parts/content.php:102
msgid "Read More"
msgstr ""
