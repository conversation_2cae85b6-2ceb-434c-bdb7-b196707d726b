<?php
$boat_rental_layout = boat_rental_get_final_sidebar_layout();
$boat_rental_sidebar_class = 'column-order-1';

if ( $boat_rental_layout === 'left-sidebar' ) {
    $boat_rental_sidebar_class = 'column-order-1';
} elseif ( $boat_rental_layout === 'right-sidebar' ) {
    $boat_rental_sidebar_class = 'column-order-2';
}

if ( $boat_rental_layout !== 'no-sidebar' ) : ?>
    <aside id="secondary" class="widget-area <?php echo esc_attr( $boat_rental_sidebar_class ); ?>">
        <div class="widget-area-wrapper">
            <?php if ( is_active_sidebar('sidebar-1') ) : ?>
                <?php dynamic_sidebar( 'sidebar-1' ); ?>
            <?php else : ?>
                <!-- Default widgets -->
                <div class="widget widget_block widget_search">
                    <h3 class="widget-title"><?php esc_html_e('Search', 'boat-rental'); ?></h3>
                    <?php get_search_form(); ?>
                </div>

                <div class="widget widget_pages">
                    <h3 class="widget-title"><?php esc_html_e('Pages', 'boat-rental'); ?></h3>
                    <ul>
                        <?php
                        wp_list_pages(array(
                            'title_li' => '',
                        ));
                        ?>
                    </ul>
                </div>

                <div class="widget widget_archive">
                    <h3 class="widget-title"><?php esc_html_e('Archives', 'boat-rental'); ?></h3>
                    <ul>
                        <?php wp_get_archives(['type' => 'monthly', 'show_post_count' => true]); ?>
                    </ul>
                </div>

                <div class="widget widget_categories">
                    <h3 class="widget-title"><?php esc_html_e('Categories', 'boat-rental'); ?></h3>
                    <ul class="wp-block-categories-list wp-block-categories">
                        <?php wp_list_categories(['orderby' => 'name', 'title_li' => '', 'show_count' => true]); ?>
                    </ul>
                </div>

                <div class="widget widget_tag_cloud">
                    <h3 class="widget-title"><?php esc_html_e('Tags', 'boat-rental'); ?></h3>
                    <?php
                    $boat_rental_tags = get_tags();
                    if ( $boat_rental_tags ) {
                        echo '<div class="tagcloud">';
                        foreach ( $boat_rental_tags as $boat_rental_tag ) {
                            $boat_rental_link = get_tag_link($boat_rental_tag->term_id);
                            echo '<a href="' . esc_url($boat_rental_link) . '" class="tag-cloud-link">' . esc_html($boat_rental_tag->name) . '</a> ';
                        }
                        echo '</div>';
                    } else {
                        echo '<p>' . esc_html__('No tags found.', 'boat-rental') . '</p>';
                    }
                    ?>
                </div>

            <?php endif; ?>
        </div>
    </aside>
<?php endif; ?>
