<?php
/**
 * Body Classes.
 * @package Boat Rental
 */

if (!function_exists('boat_rental_body_classes')) :

    function boat_rental_body_classes($boat_rental_classes)
    {
        $boat_rental_defaults = boat_rental_get_default_theme_options();
        $boat_rental_layout = boat_rental_get_final_sidebar_layout();

        // Adds a class of hfeed to non-singular pages.
        if (!is_singular()) {
            $boat_rental_classes[] = 'hfeed';
        }

        // Sidebar layout logic
        $boat_rental_classes[] = $boat_rental_layout;

        // Copyright alignment
        $copyright_alignment = get_theme_mod('boat_rental_copyright_alignment', 'Default');
        $boat_rental_classes[] = 'copyright-' . strtolower($copyright_alignment);

        return $boat_rental_classes;
    }

endif;

add_filter('body_class', 'boat_rental_body_classes');