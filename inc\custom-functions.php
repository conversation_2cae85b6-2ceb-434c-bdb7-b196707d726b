<?php
/**
 * Custom Functions.
 *
 * @package Boat Rental
 */

if( !function_exists( 'boat_rental_fonts_url' ) ) :

    //Google Fonts URL
    function boat_rental_fonts_url(){

        $boat_rental_font_families = array(
            'Jost:ital,wght@0,100..900;1,100..900',
            'Source+Serif+4:ital,opsz,wght@0,8..60,200..900;1,8..60,200..900',
        );

        $boat_rental_fonts_url = add_query_arg( array(
            'family' => implode( '&family=', $boat_rental_font_families ),
            'display' => 'swap',
        ), 'https://fonts.googleapis.com/css2' );

        return esc_url_raw($boat_rental_fonts_url);
    }

endif;

if ( ! function_exists( 'boat_rental_sub_menu_toggle_button' ) ) :

    function boat_rental_sub_menu_toggle_button( $boat_rental_args, $boat_rental_item, $depth ) {

        // Add sub menu toggles to the main menu with toggles
        if ( $boat_rental_args->theme_location == 'boat-rental-primary-menu' && isset( $boat_rental_args->show_toggles ) ) {
            
            // Wrap the menu item link contents in a div, used for positioning
            $boat_rental_args->before = '<div class="submenu-wrapper">';
            $boat_rental_args->after  = '';

            // Add a toggle to items with children
            if ( in_array( 'menu-item-has-children', $boat_rental_item->classes ) ) {

                $toggle_target_string = '.menu-item.menu-item-' . $boat_rental_item->ID . ' > .sub-menu';

                // Add the sub menu toggle
                $boat_rental_args->after .= '<button type="button" class="theme-aria-button submenu-toggle" data-toggle-target="' . $toggle_target_string . '" data-toggle-type="slidetoggle" data-toggle-duration="250" aria-expanded="false"><span class="btn__content" tabindex="-1"><span class="screen-reader-text">' . esc_html__( 'Show sub menu', 'boat-rental' ) . '</span>' . boat_rental_get_theme_svg( 'chevron-down' ) . '</span></button>';

            }

            // Close the wrapper
            $boat_rental_args->after .= '</div><!-- .submenu-wrapper -->';
            // Add sub menu icons to the main menu without toggles (the fallback menu)

        }elseif( $boat_rental_args->theme_location == 'boat-rental-primary-menu' ) {

            if ( in_array( 'menu-item-has-children', $boat_rental_item->classes ) ) {

                $boat_rental_args->before = '<div class="link-icon-wrapper">';
                $boat_rental_args->after  = boat_rental_get_theme_svg( 'chevron-down' ) . '</div>';

            } else {

                $boat_rental_args->before = '';
                $boat_rental_args->after  = '';

            }

        }

        return $boat_rental_args;

    }

endif;

add_filter( 'nav_menu_item_args', 'boat_rental_sub_menu_toggle_button', 10, 3 );

if ( ! function_exists( 'boat_rental_the_theme_svg' ) ):
    
    function boat_rental_the_theme_svg( $boat_rental_svg_name, $boat_rental_return = false ) {

        if( $boat_rental_return ){

            return boat_rental_get_theme_svg( $boat_rental_svg_name ); //phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped -- Escaped in boat_rental_get_theme_svg();.

        }else{

            echo boat_rental_get_theme_svg( $boat_rental_svg_name ); //phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped -- Escaped in boat_rental_get_theme_svg();.

        }
    }

endif;

if ( ! function_exists( 'boat_rental_get_theme_svg' ) ):

    function boat_rental_get_theme_svg( $boat_rental_svg_name ) {

        // Make sure that only our allowed tags and attributes are included.
        $boat_rental_svg = wp_kses(
            boat_rental_SVG_Icons::get_svg( $boat_rental_svg_name ),
            array(
                'svg'     => array(
                    'class'       => true,
                    'xmlns'       => true,
                    'width'       => true,
                    'height'      => true,
                    'viewbox'     => true,
                    'aria-hidden' => true,
                    'role'        => true,
                    'focusable'   => true,
                ),
                'path'    => array(
                    'fill'      => true,
                    'fill-rule' => true,
                    'd'         => true,
                    'transform' => true,
                ),
                'polygon' => array(
                    'fill'      => true,
                    'fill-rule' => true,
                    'points'    => true,
                    'transform' => true,
                    'focusable' => true,
                ),
                'polyline' => array(
                    'fill'      => true,
                    'points'    => true,
                ),
                'line' => array(
                    'fill'      => true,
                    'x1'      => true,
                    'x2' => true,
                    'y1'    => true,
                    'y2' => true,
                ),
            )
        );
        if ( ! $boat_rental_svg ) {
            return false;
        }
        return $boat_rental_svg;

    }

endif;

if( !function_exists( 'boat_rental_post_category_list' ) ) :

    // Post Category List.
    function boat_rental_post_category_list( $boat_rental_select_cat = true ){

        $boat_rental_post_cat_lists = get_categories(
            array(
                'hide_empty' => '0',
                'exclude' => '1',
            )
        );

        $boat_rental_post_cat_cat_array = array();
        if( $boat_rental_select_cat ){

            $boat_rental_post_cat_cat_array[''] = esc_html__( '-- Select Category --','boat-rental' );

        }

        foreach ( $boat_rental_post_cat_lists as $boat_rental_post_cat_list ) {

            $boat_rental_post_cat_cat_array[$boat_rental_post_cat_list->slug] = $boat_rental_post_cat_list->name;

        }

        return $boat_rental_post_cat_cat_array;
    }

endif;

if( !function_exists('boat_rental_single_post_navigation') ):

    function boat_rental_single_post_navigation(){

        $boat_rental_default = boat_rental_get_default_theme_options();
        $boat_rental_twp_navigation_type = esc_attr( get_post_meta( get_the_ID(), 'twp_disable_ajax_load_next_post', true ) );
        $boat_rental_current_id = '';
        $article_wrap_class = '';
        global $post;
        $boat_rental_current_id = $post->ID;
        if( $boat_rental_twp_navigation_type == '' || $boat_rental_twp_navigation_type == 'global-layout' ){
            $boat_rental_twp_navigation_type = get_theme_mod('twp_navigation_type', $boat_rental_default['twp_navigation_type']);
        }

        if( $boat_rental_twp_navigation_type != 'no-navigation' && 'post' === get_post_type() ){

            if( $boat_rental_twp_navigation_type == 'theme-normal-navigation' ){ ?>

                <div class="navigation-wrapper">
                    <?php
                    // Previous/next post navigation.
                    the_post_navigation(array(
                        'prev_text' => '<span class="arrow" aria-hidden="true">' . boat_rental_the_theme_svg('arrow-left',$boat_rental_return = true ) . '</span><span class="screen-reader-text">' . esc_html__('Previous post:', 'boat-rental') . '</span><span class="post-title">%title</span>',
                        'next_text' => '<span class="arrow" aria-hidden="true">' . boat_rental_the_theme_svg('arrow-right',$boat_rental_return = true ) . '</span><span class="screen-reader-text">' . esc_html__('Next post:', 'boat-rental') . '</span><span class="post-title">%title</span>',
                    )); ?>
                </div>
                <?php

            }else{

                $boat_rental_next_post = get_next_post();
                if( isset( $boat_rental_next_post->ID ) ){

                    $boat_rental_next_post_id = $boat_rental_next_post->ID;
                    echo '<div loop-count="1" next-post="' . absint( $boat_rental_next_post_id ) . '" class="twp-single-infinity"></div>';

                }
            }

        }

    }

endif;

add_action( 'boat_rental_navigation_action','boat_rental_single_post_navigation',30 );

if( !function_exists('boat_rental_content_offcanvas') ):

    // Offcanvas Contents
    function boat_rental_content_offcanvas(){ ?>

        <div id="offcanvas-menu">
            <div class="offcanvas-wraper">
                <div class="close-offcanvas-menu">
                    <div class="offcanvas-close">
                        <a href="javascript:void(0)" class="skip-link-menu-start"></a>
                        <button type="button" class="button-offcanvas-close">
                            <span class="offcanvas-close-label">
                                <?php echo esc_html__('Close', 'boat-rental'); ?>
                            </span>
                        </button>
                    </div>
                </div>
                <div id="primary-nav-offcanvas" class="offcanvas-item offcanvas-main-navigation">
                    <nav class="primary-menu-wrapper" aria-label="<?php esc_attr_e('Horizontal', 'boat-rental'); ?>" role="navigation">
                        <ul class="primary-menu theme-menu">
                            <?php
                            if (has_nav_menu('boat-rental-primary-menu')) {
                                wp_nav_menu(
                                    array(
                                        'container' => '',
                                        'items_wrap' => '%3$s',
                                        'theme_location' => 'boat-rental-primary-menu',
                                        'show_toggles' => true,
                                    )
                                );
                            }else{

                                wp_list_pages(
                                    array(
                                        'match_menu_classes' => true,
                                        'show_sub_menu_icons' => true,
                                        'title_li' => false,
                                        'show_toggles' => true,
                                        'walker' => new Boat_Rental_Walker_Page(),
                                    )
                                );
                            }
                            ?>
                        </ul>
                    </nav><!-- .primary-menu-wrapper -->
                </div>
                <a href="javascript:void(0)" class="skip-link-menu-end"></a>
            </div>
        </div>

    <?php
    }

endif;

add_action( 'boat_rental_before_footer_content_action','boat_rental_content_offcanvas',30 );

if( !function_exists('boat_rental_footer_content_widget') ):

    function boat_rental_footer_content_widget(){
        
        $boat_rental_default = boat_rental_get_default_theme_options();
        
        $boat_rental_footer_column_layout = absint(get_theme_mod('boat_rental_footer_column_layout', $boat_rental_default['boat_rental_footer_column_layout']));
        $boat_rental_footer_sidebar_class = 12;
        
        if($boat_rental_footer_column_layout == 2) {
            $boat_rental_footer_sidebar_class = 6;
        }
        
        if($boat_rental_footer_column_layout == 3) {
            $boat_rental_footer_sidebar_class = 4;
        }
        ?>
        
        <?php if ( get_theme_mod('boat_rental_display_footer', true) == true ) : ?>
            <div class="footer-widgetarea">
                <div class="wrapper">
                    <div class="column-row">
                    
                        <?php for ($i = 0; $i < $boat_rental_footer_column_layout; $i++) : ?>
                            
                            <div class="column <?php echo 'column-' . absint($boat_rental_footer_sidebar_class); ?> column-sm-12">
                                
                                <?php 
                                // If no widgets are assigned, display default widgets
                                if ( ! is_active_sidebar( 'boat-rental-footer-widget-' . $i ) ) : 

                                    if ($i === 0) : ?>
                                        <div id="media_image-3" class="widget widget_media_image">
                                            <img src="<?php echo esc_url(get_template_directory_uri() . '/assets/images/logo.png'); ?>" alt="<?php echo esc_attr__( 'Footer Image', 'boat-rental' ); ?>" style="max-width: 100%; height: auto;">
                                        </div>
                                        <div id="text-3" class="widget widget_text">
                                            <div class="textwidget">
                                                <p class="widget_text">
                                                    <?php esc_html_e('The Boat Rental is a multipurpose, modern, and luxurious WordPress theme designed for businesses offering boat rentals, yacht charters, water sports, Boat Rental, Yacht Rental, Luxury Boat Rental, Private Boat Hire, Sailboat Rental, Speed Boat Rental, Fishing Boat Rental, Pontoon Boat Rental, Jet Ski Rental or adventure services. Its minimal, elegant, and sophisticated layout ensures a visually appealing presentation that captivates visitors instantly. Built with clean, secure and clean code, this theme is optimized for performance, guaranteeing a faster page load time and smooth navigation.', 'boat-rental'); ?>
                                                </p>
                                            </div>
                                        </div>

                                    <?php elseif ($i === 1) : ?>
                                        <div id="pages-2" class="widget widget_pages">
                                            <h2 class="widget-title"><?php esc_html_e('Calendar', 'boat-rental'); ?></h2>
                                            <?php get_calendar(); ?>
                                        </div>

                                    <?php elseif ($i === 2) : ?>
                                        <div id="search-2" class="widget widget_search">
                                            <h2 class="widget-title"><?php esc_html_e('Enter Keywords Here', 'boat-rental'); ?></h2>
                                            <?php get_search_form(); ?>
                                        </div>
                                    <?php endif; 
                                    
                                else :
                                    // Display dynamic sidebar widget if assigned
                                    dynamic_sidebar('boat-rental-footer-widget-' . $i);
                                endif;
                                ?>
                                
                            </div>
                            
                        <?php endfor; ?>

                    </div>
                </div>
            </div>
        <?php endif; ?> 

    <?php
    }

endif;

add_action( 'boat_rental_footer_content_action', 'boat_rental_footer_content_widget', 10 );

if( !function_exists('boat_rental_footer_content_info') ):

    /**
     * Footer Copyright Area
    **/
    function boat_rental_footer_content_info(){

        $boat_rental_default = boat_rental_get_default_theme_options(); ?>
        <div class="site-info">
            <div class="wrapper">
                <div class="column-row">

                    <div class="column column-9">
                        <div class="footer-credits">

                            <div class="footer-copyright">

                                <?php
                                $boat_rental_footer_copyright_text = wp_kses_post( get_theme_mod( 'boat_rental_footer_copyright_text', $boat_rental_default['boat_rental_footer_copyright_text'] ) );
                                    echo esc_html( $boat_rental_footer_copyright_text );
                                    echo '<br>';

                                    echo esc_html__('Theme: ', 'boat-rental') . '<a href="' . esc_url('https://www.omegathemes.com/products/boat-rental') . '" title="' . esc_attr__('Boat Rental ', 'boat-rental') . '" target="_blank"><span>' . esc_html__('Boat Rental  ', 'boat-rental') . '</span></a>' . esc_html__(' By ', 'boat-rental') . '  <span>' . esc_html__('OMEGA ', 'boat-rental') . '</span>';
                                    echo esc_html__('Powered by ', 'boat-rental') . '<a href="' . esc_url('https://wordpress.org') . '" title="' . esc_attr__('WordPress', 'boat-rental') . '" target="_blank"><span>' . esc_html__('WordPress.', 'boat-rental') . '</span></a>';
                                 ?>
                            </div>
                        </div>
                    </div>


                    <div class="column column-3 align-text-right">
                        <a class="to-the-top" href="#site-header">
                            <span class="to-the-top-long">
                                <?php if ( get_theme_mod('boat_rental_enable_to_the_top', true) == true ) : ?>
                                    <?php
                                    $boat_rental_to_the_top_text = get_theme_mod( 'boat_rental_to_the_top_text', __( 'To the Top', 'boat-rental' ) );
                                    printf( 
                                        wp_kses( 
                                            /* translators: %s is the arrow icon markup */
                                            '%s %s', 
                                            array( 'span' => array( 'class' => array(), 'aria-hidden' => array() ) ) 
                                        ), 
                                        esc_html( $boat_rental_to_the_top_text ),
                                        '<span class="arrow" aria-hidden="true">&uarr;</span>' 
                                    );
                                    ?>
                                <?php endif; ?>
                            </span>
                        </a>

                    </div>
                </div>
            </div>
        </div>

    <?php
    }

endif;

add_action( 'boat_rental_footer_content_action','boat_rental_footer_content_info',20 );


if( !function_exists( 'boat_rental_main_slider' ) ) :

    function boat_rental_main_slider(){

        $boat_rental_defaults = boat_rental_get_default_theme_options();
        $boat_rental_header_banner = get_theme_mod( 'boat_rental_header_banner', $boat_rental_defaults['boat_rental_header_banner'] );
        $boat_rental_header_banner_cat = get_theme_mod( 'boat_rental_header_banner_cat' );

        if( $boat_rental_header_banner ){

            $boat_rental_rtl = '';
            if( is_rtl() ){
                $boat_rental_rtl = 'dir="rtl"';
            }

            $boat_rental_banner_query = new WP_Query( array('post_type' => 'post', 'posts_per_page' => 4,'post__not_in' => get_option("sticky_posts"), 'category_name' => esc_html( $boat_rental_header_banner_cat ) ) );
            $boat_rental_contact_form_shortcode = get_theme_mod( 'boat_rental_contact_form_shortcode' );

          if( $boat_rental_banner_query->have_posts() ): ?>

            <div class="theme-custom-block theme-banner-block">
                 <div class="slider-box">
                    <div class="swiper-container theme-main-carousel" <?php echo is_rtl() ? 'dir="rtl"' : ''; ?>>
                        <div class="swiper-wrapper">
                            <?php while ( $boat_rental_banner_query->have_posts() ) : $boat_rental_banner_query->the_post();
                                $boat_rental_featured_image = wp_get_attachment_image_src( get_post_thumbnail_id(), 'large' )[0] ?? get_template_directory_uri() . '/assets/images/slide-bg.png';
                                ?>
                                <div class="swiper-slide main-carousel-item">
                                    <div class="slider-main">
                                        <div class="data-bg banner-img" data-background="<?php echo esc_url( $boat_rental_featured_image ); ?>">
                                            <a href="<?php the_permalink(); ?>" class="theme-image-responsive"></a>
                                        </div>
                                        <?php boat_rental_post_format_icon(); ?>
                                        <div class="main-carousel-caption">
                                            <div class="post-content ">
                                                <header class="entry-header">
                                                    <h2 class="entry-title entry-title-big">
                                                        <a href="<?php the_permalink(); ?>" rel="bookmark"><span><?php the_title(); ?></span></a>
                                                    </h2>
                                                </header>
                                                <div class="entry-content">
                                                    <?php
                                                        if (has_excerpt()) {

                                                            the_excerpt();

                                                        } else {

                                                        echo esc_html(wp_trim_words(get_the_content(), 25, '...'));

                                                    } ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                        <div class="custom-navigation">
                            <div class="swiper-button-prev custom-prev">
                                <span class="arrow"></span>
                            </div>
                            <div class="swiper-button-next custom-next">
                                <span class="arrow"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php if( $boat_rental_contact_form_shortcode ){ ?>
                    <div class="form-shortcode">
                        <div class="wrapper">
                            <?php echo do_shortcode( $boat_rental_contact_form_shortcode ); ?>
                        </div>
                    </div>
                <?php }?>
            </div>

          <?php
          wp_reset_postdata();
          endif;

        }

    }

endif;

if (!function_exists('boat_rental_post_format_icon')):

    // Post Format Icon.
    function boat_rental_post_format_icon() {

        $boat_rental_format = get_post_format(get_the_ID()) ?: 'standard';
        $boat_rental_icon = '';
        $boat_rental_title = '';
        if( $boat_rental_format == 'video' ){
            $boat_rental_icon = boat_rental_get_theme_svg( 'video' );
            $boat_rental_title = esc_html__('Video','boat-rental');
        }elseif( $boat_rental_format == 'audio' ){
            $boat_rental_icon = boat_rental_get_theme_svg( 'audio' );
            $boat_rental_title = esc_html__('Audio','boat-rental');
        }elseif( $boat_rental_format == 'gallery' ){
            $boat_rental_icon = boat_rental_get_theme_svg( 'gallery' );
            $boat_rental_title = esc_html__('Gallery','boat-rental');
        }elseif( $boat_rental_format == 'quote' ){
            $boat_rental_icon = boat_rental_get_theme_svg( 'quote' );
            $boat_rental_title = esc_html__('Quote','boat-rental');
        }elseif( $boat_rental_format == 'image' ){
            $boat_rental_icon = boat_rental_get_theme_svg( 'image' );
            $boat_rental_title = esc_html__('Image','boat-rental');
        } elseif( $boat_rental_format == 'link' ){
            $boat_rental_icon = boat_rental_get_theme_svg( 'link' );
            $boat_rental_title = esc_html__('Link','boat-rental');
        } elseif( $boat_rental_format == 'status' ){
            $boat_rental_icon = boat_rental_get_theme_svg( 'status' );
            $boat_rental_title = esc_html__('Status','boat-rental');
        } elseif( $boat_rental_format == 'aside' ){
            $boat_rental_icon = boat_rental_get_theme_svg( 'aside' );
            $boat_rental_title = esc_html__('Aside','boat-rental');
        } elseif( $boat_rental_format == 'chat' ){
            $boat_rental_icon = boat_rental_get_theme_svg( 'chat' );
            $boat_rental_title = esc_html__('Chat','boat-rental');
        }
        
        if (!empty($boat_rental_icon)) { ?>
            <div class="theme-post-format">
                <span class="post-format-icom"><?php echo boat_rental_svg_escape($boat_rental_icon); ?></span>
                <?php if( $boat_rental_title ){ echo '<span class="post-format-label">'.esc_html( $boat_rental_title ).'</span>'; } ?>
            </div>
        <?php }
    }

endif;

if ( ! function_exists( 'boat_rental_svg_escape' ) ):

    /**
     * Get information about the SVG icon.
     *
     * @param string $boat_rental_svg_name The name of the icon.
     * @param string $group The group the icon belongs to.
     * @param string $color Color code.
     */
    function boat_rental_svg_escape( $boat_rental_input ) {

        // Make sure that only our allowed tags and attributes are included.
        $boat_rental_svg = wp_kses(
            $boat_rental_input,
            array(
                'svg'     => array(
                    'class'       => true,
                    'xmlns'       => true,
                    'width'       => true,
                    'height'      => true,
                    'viewbox'     => true,
                    'aria-hidden' => true,
                    'role'        => true,
                    'focusable'   => true,
                ),
                'path'    => array(
                    'fill'      => true,
                    'fill-rule' => true,
                    'd'         => true,
                    'transform' => true,
                ),
                'polygon' => array(
                    'fill'      => true,
                    'fill-rule' => true,
                    'points'    => true,
                    'transform' => true,
                    'focusable' => true,
                ),
            )
        );

        if ( ! $boat_rental_svg ) {
            return false;
        }

        return $boat_rental_svg;

    }

endif;

if( !function_exists( 'boat_rental_sanitize_sidebar_option_meta' ) ) :

    // Sidebar Option Sanitize.
    function boat_rental_sanitize_sidebar_option_meta( $boat_rental_input ){

        $boat_rental_metabox_options = array( 'global-sidebar','left-sidebar','right-sidebar','no-sidebar' );
        if( in_array( $boat_rental_input,$boat_rental_metabox_options ) ){

            return $boat_rental_input;

        }else{

            return '';

        }
    }

endif;

if( !function_exists( 'boat_rental_sanitize_pagination_meta' ) ) :

    // Sidebar Option Sanitize.
    function boat_rental_sanitize_pagination_meta( $boat_rental_input ){

        $boat_rental_metabox_options = array( 'Center','Right','Left');
        if( in_array( $boat_rental_input,$boat_rental_metabox_options ) ){

            return $boat_rental_input;

        }else{

            return '';

        }
    }

endif;

if( !function_exists( 'boat_rental_product_section' ) ) :

    function boat_rental_product_section(){ 

        $boat_rental_default = boat_rental_get_default_theme_options();

        $boat_rental_header_case_studies = get_theme_mod( 'boat_rental_header_case_studies', $boat_rental_default['boat_rental_header_case_studies'] );
        $boat_rental_locations_post_cat = get_theme_mod( 'boat_rental_locations_post_cat' );

        $boat_rental_team_section_title = esc_html( get_theme_mod( 'boat_rental_team_section_title',
        $boat_rental_default['boat_rental_team_section_title'] ) );

        $boat_rental_locations_query = new WP_Query( array('post_type' => 'boatsrental', 'posts_per_page' => 6,'post__not_in' => get_option("sticky_posts"), 'category_name' => esc_html( $boat_rental_locations_post_cat ) ) );

            if( $boat_rental_locations_query->have_posts() ): 
            if( $boat_rental_header_case_studies ){
            ?>
                <div class="theme-product-block">
                    <div class="wrapper">
                        <div class="section-heading">
                            <?php if( $boat_rental_team_section_title ){ ?>
                                <h4><?php echo esc_html( $boat_rental_team_section_title ); ?></h4>
                            <?php } ?>
                        </div>
                        <div class="tab-box">
                            <!-- Tab Header -->
                            <div class="tab-header">
                                <?php
                                for ($i = 1; $i <= 3; $i++) {
                                    $boat_rental_tab_title = get_theme_mod('boat_rental_price_list_tab_title' . $i);
                                    if ($boat_rental_tab_title) {
                                        echo '<div class="tab-item ' . (($i == 1) ? 'active' : '') . '" data-tab-id="tab-content-'. $i . '"  onclick="showTabContent(' . $i . ')">' . esc_html($boat_rental_tab_title) . '</div>';
                                    }
                                }
                                ?>
                            </div>
                            <!-- Tab Content -->
                            <?php if( get_theme_mod('boat_rental_category_tab1','Web Design')  || get_theme_mod('boat_rental_category_tab2','uncategorized') || get_theme_mod('boat_rental_category_tab3','Web Design') || get_theme_mod('boat_rental_category_tab4') ) { ?>
                            <div class="tabcontent">
                                <?php

                                // Separate post query outside the tab content loop
                                $boat_rental_header_pricing_plans_cats = array(
                                    get_theme_mod('boat_rental_category_tab1'),
                                    get_theme_mod('boat_rental_category_tab2'),
                                    get_theme_mod('boat_rental_category_tab3')
                                );

                                for ($i = 1; $i <= 3; $i++) {
                                    $category_slug = esc_html($boat_rental_header_pricing_plans_cats[$i - 1]);

                                    $boat_rental_tab_banner_query = new WP_Query(array(
                                        'post_type' => 'boatsrental',
                                        'posts_per_page' => 4,
                                        'post__not_in' => get_option("sticky_posts"),
                                        'tax_query' => array(
                                            array(
                                                'taxonomy' => 'boattaxonomy',
                                                'field' => 'slug',
                                                'terms' => $category_slug
                                            )
                                        )
                                    ));

                                    if ($boat_rental_tab_banner_query->have_posts()) :
                                        echo '<div class="tab-content" id="tab-content-' . $i . '">'; ?>
                                        <div class="post-main-content owl-carousel"> 
                                            <?php
                                                $s=1;
                                                while( $boat_rental_tab_banner_query->have_posts() ):
                                                $boat_rental_tab_banner_query->the_post();
                                                $boat_rental_featured_image = wp_get_attachment_image_src(get_post_thumbnail_id(), 'large');
                                                $boat_rental_featured_image = isset( $boat_rental_featured_image[0] ) ? $boat_rental_featured_image[0] : ''; 

                                                $boat_rental_team_section_designation = esc_html( get_theme_mod( 'boat_rental_team_section_designation'.$s ) );

                                                ?>                                
                                                <div class="theme-article-post slider-post-content">
                                                    <div class="entry-thumbnail">
                                                        <div class="data-bg featured-img" data-background="<?php echo esc_url($boat_rental_featured_image ? $boat_rental_featured_image : get_template_directory_uri() . '/assets/images/post.png'); ?>">
                                                            <a href="<?php the_permalink(); ?>" class="theme-image-responsive" tabindex="0"></a>
                                                        </div>
                                                        <?php boat_rental_post_format_icon(); ?>
                                                    </div>
                                                    <div class="main-owl-caption">
                                                        <div class="post-content-location">
                                                            <div class="boat-meta-review">
                                                                <div class="slide-cat">
                                                                    <?php 
                                                                    $terms = get_the_terms( get_the_ID(), 'boattaxonomy' );
                                                                    if ( ! empty( $terms ) && ! is_wp_error( $terms ) ) {
                                                                        echo esc_html( $terms[0]->name ); // Display first boattaxonomy term
                                                                    }
                                                                    ?>
                                                                </div>
                                                                <div class="review-box">
                                                                    <?php $boat_rental_boad_reviews = get_post_meta( get_the_ID(), 'boad_reviews', true );

                                                                    if ( ! empty( $boat_rental_boad_reviews ) ) : ?>
                                                                        <p class="review">
                                                                            <span><?php echo esc_html('Reviews :','boat-rental'); ?></span>
                                                                            <?php echo esc_html( $boat_rental_boad_reviews ); ?>
                                                                            <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 640 640"><!--!Font Awesome Free v7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M341.5 45.1C337.4 37.1 329.1 32 320.1 32C311.1 32 302.8 37.1 298.7 45.1L225.1 189.3L65.2 214.7C56.3 216.1 48.9 222.4 46.1 231C43.3 239.6 45.6 249 51.9 255.4L166.3 369.9L141.1 529.8C139.7 538.7 143.4 547.7 150.7 553C158 558.3 167.6 559.1 175.7 555L320.1 481.6L464.4 555C472.4 559.1 482.1 558.3 489.4 553C496.7 547.7 500.4 538.8 499 529.8L473.7 369.9L588.1 255.4C594.5 249 596.7 239.6 593.9 231C591.1 222.4 583.8 216.1 574.8 214.7L415 189.3L341.5 45.1z"/></svg>
                                                                        </p>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                            
                                                            <header class="entry-header">
                                                                <h2 class="entry-title entry-title-big">
                                                                    <a href="<?php the_permalink(); ?>" rel="bookmark"><span><?php the_title(); ?></span></a>
                                                                </h2> 
                                                                <div class="boat-meta">
                                                                    <div class="meta1">
                                                                        <?php $boat_rental_boat_capacity = get_post_meta( get_the_ID(), 'boat_capacity', true );

                                                                        if ( ! empty( $boat_rental_boat_capacity ) ) : ?>
                                                                            <p class="location">
                                                                                <?php echo esc_html('Capacity:','boat-rental'); ?>
                                                                                <?php echo esc_html( $boat_rental_boat_capacity ); ?>
                                                                            </p>
                                                                        <?php endif; ?>
                                                                        <?php $boat_rental_boat_engine = get_post_meta( get_the_ID(), 'boat_engine', true );

                                                                        if ( ! empty( $boat_rental_boat_engine ) ) : ?>
                                                                            <p class="location">
                                                                                <?php echo esc_html('Engine:','boat-rental'); ?>
                                                                                <?php echo esc_html( $boat_rental_boat_engine ); ?>
                                                                            </p>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                    <div class="meta2 ">
                                                                        <div class="features-box">
                                                                            <?php $boat_rental_boat_features = get_post_meta( get_the_ID(), 'boat_features', true );

                                                                            if ( ! empty( $boat_rental_boat_features ) ) : ?>
                                                                                <p class="location">
                                                                                    <?php echo esc_html('Features:','boat-rental'); ?>
                                                                                    <?php echo esc_html( $boat_rental_boat_features ); ?>
                                                                                </p>
                                                                            <?php endif; ?>
                                                                        </div>
                                                                        <?php $boat_rental_fuel_type = get_post_meta( get_the_ID(), 'fuel_type', true );

                                                                        if ( ! empty( $boat_rental_fuel_type ) ) : ?>
                                                                            <p class="location">
                                                                                <?php echo esc_html('Fuel:','boat-rental'); ?>
                                                                                <?php echo esc_html( $boat_rental_fuel_type ); ?>
                                                                            </p>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                </div>
                                                                <a class="banner-btn" href="<?php the_permalink(); ?>"><?php echo esc_html('View Details','boat-rental'); ?><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 640 640"><!--!Font Awesome Free v7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M439.1 297.4C451.6 309.9 451.6 330.2 439.1 342.7L279.1 502.7C266.6 515.2 246.3 515.2 233.8 502.7C221.3 490.2 221.3 469.9 233.8 457.4L371.2 320L233.9 182.6C221.4 170.1 221.4 149.8 233.9 137.3C246.4 124.8 266.7 124.8 279.2 137.3L439.2 297.3z"/></svg></a>
                                                            </header>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php $s++; endwhile; 
                                        echo '</div>';
                                        echo '</div>';
                                        wp_reset_postdata();
                                    endif;
                                } ?>
                            </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            <?php } ?>
             <?php
            wp_reset_postdata();
            endif;
          ?>
    <?php }

endif;

if( !function_exists( 'boat_rental_sanitize_menu_transform' ) ) :

    // Sidebar Option Sanitize.
    function boat_rental_sanitize_menu_transform( $boat_rental_input ){

        $boat_rental_metabox_options = array( 'capitalize','uppercase','lowercase');
        if( in_array( $boat_rental_input,$boat_rental_metabox_options ) ){

            return $boat_rental_input;

        }else{

            return '';

        }
    }

endif;

if( !function_exists( 'boat_rental_sanitize_page_content_alignment' ) ) :

    // Sidebar Option Sanitize.
    function boat_rental_sanitize_page_content_alignment( $boat_rental_input ){

        $boat_rental_metabox_options = array( 'left','center','right');
        if( in_array( $boat_rental_input,$boat_rental_metabox_options ) ){

            return $boat_rental_input;

        }else{

            return '';

        }
    }

endif;

if( !function_exists( 'boat_rental_sanitize_footer_widget_title_alignment' ) ) :

    // Footer Option Sanitize.
    function boat_rental_sanitize_footer_widget_title_alignment( $boat_rental_input ){

        $boat_rental_metabox_options = array( 'left','center','right');
        if( in_array( $boat_rental_input,$boat_rental_metabox_options ) ){

            return $boat_rental_input;

        }else{

            return '';

        }
    }

endif;

if( !function_exists( 'boat_rental_sanitize_pagination_type' ) ) :

    /**
     * Sanitize the pagination type setting.
     *
     * @param string $boat_rental_input The input value from the Customizer.
     * @return string The sanitized value.
     */
    function boat_rental_sanitize_pagination_type( $boat_rental_input ) {
        // Define valid options for the pagination type.
        $boat_rental_valid_options = array( 'numeric', 'newer_older' ); // Update valid options to include 'newer_older'

        // If the input is one of the valid options, return it. Otherwise, return the default option ('numeric').
        if ( in_array( $boat_rental_input, $boat_rental_valid_options, true ) ) {
            return $boat_rental_input;
        } else {
            // Return 'numeric' as the fallback if the input is invalid.
            return 'numeric';
        }
    }

endif;


// Sanitize the enable/disable setting for pagination
if( !function_exists('boat_rental_sanitize_enable_pagination') ) :
    function boat_rental_sanitize_enable_pagination( $boat_rental_input ) {
        return (bool) $boat_rental_input;
    }
endif;

if( !function_exists( 'boat_rental_sanitize_copyright_alignment_meta' ) ) :

    // Sidebar Option Sanitize.
    function boat_rental_sanitize_copyright_alignment_meta( $boat_rental_input ){

        $boat_rental_metabox_options = array( 'Default','Reverse','Center');
        if( in_array( $boat_rental_input,$boat_rental_metabox_options ) ){

            return $boat_rental_input;

        }else{

            return '';

        }
    }

endif;

/**
 * Sidebar Layout Function
 */
function boat_rental_get_final_sidebar_layout() {
	$boat_rental_defaults       = boat_rental_get_default_theme_options();
	$boat_rental_global_layout  = get_theme_mod('boat_rental_global_sidebar_layout', $boat_rental_defaults['boat_rental_global_sidebar_layout']);
	$boat_rental_page_layout    = get_theme_mod('boat_rental_page_sidebar_layout', $boat_rental_global_layout);
	$boat_rental_post_layout    = get_theme_mod('boat_rental_post_sidebar_layout', $boat_rental_global_layout);
	$boat_rental_meta_layout    = get_post_meta(get_the_ID(), 'boat_rental_post_sidebar_option', true);

	if (!empty($boat_rental_meta_layout) && $boat_rental_meta_layout !== 'default') {
		return $boat_rental_meta_layout;
	}
	if (is_page() || (function_exists('is_shop') && is_shop())) {
		return $boat_rental_page_layout;
	}
	if (is_single()) {
		return $boat_rental_post_layout;
	}
	return $boat_rental_global_layout;
}