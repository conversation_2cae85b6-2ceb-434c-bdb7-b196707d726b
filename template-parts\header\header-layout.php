<?php
/**
 * Header Layout
 * @package Boat Rental
 */

$boat_rental_defaults = boat_rental_get_default_theme_options();

$boat_rental_sticky = get_theme_mod('boat_rental_sticky');
    $boat_rental_data_sticky = "false";
    if ($boat_rental_sticky) {
    $boat_rental_data_sticky = "true";
    }
    global $wp_customize;

$boat_rental_header_layout_button_text = esc_html( get_theme_mod( 'boat_rental_header_layout_button_text',
$boat_rental_defaults['boat_rental_header_layout_button_text'] ) );

$boat_rental_header_layout_button_url = esc_html( get_theme_mod( 'boat_rental_header_layout_button_url',
$boat_rental_defaults['boat_rental_header_layout_button_url'] ) );

?>
<header id="site-header" class="site-header-layout header-layout" role="banner">
    <div class="header-navbar <?php if( is_user_logged_in() && !isset( $wp_customize ) ){ echo "login-user";} ?>" data-sticky="<?php echo esc_attr($boat_rental_data_sticky); ?>">
        <div class="wrapper header-wrapper">
            <div class="theme-header-areas header-areas-left aa">
                <div class="header-titles">
                    <?php
                    boat_rental_site_logo();
                    boat_rental_site_description();
                    ?>
                </div>
            </div>
            <div class="header-right-box">
                <div class="theme-header-areas header-areas-right bb">
                    <div class="site-navigation">
                        <nav class="primary-menu-wrapper" aria-label="<?php esc_attr_e('Horizontal', 'boat-rental'); ?>" role="navigation">
                            <ul class="primary-menu theme-menu">
                                <?php
                                if (has_nav_menu('boat-rental-primary-menu')) {
                                    wp_nav_menu(
                                        array(
                                            'container' => '',
                                            'items_wrap' => '%3$s',
                                            'theme_location' => 'boat-rental-primary-menu',
                                        )
                                    );
                                } else {
                                    wp_list_pages(
                                        array(
                                            'match_menu_classes' => true,
                                            'show_sub_menu_icons' => true,
                                            'title_li' => false,
                                            'walker' => new Boat_Rental_Walker_Page(),
                                        )
                                    );
                                } ?>
                            </ul>
                        </nav>
                    </div>
                    <div class="navbar-controls twp-hide-js">
                        <button type="button" class="navbar-control navbar-control-offcanvas">
                            <span class="navbar-control-trigger" tabindex="-1">
                                <?php boat_rental_the_theme_svg('menu'); ?>
                            </span>
                        </button>
                    </div>
                </div>
                <div class="theme-header-areas header-areas-right header-contact cc">
                    <div class="header-button-box">
                        <?php if( $boat_rental_header_layout_button_text || $boat_rental_header_layout_button_url  ){ ?>
                            <a href="<?php echo esc_url( $boat_rental_header_layout_button_url ); ?>"><?php echo esc_html( $boat_rental_header_layout_button_text ); ?></a>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>