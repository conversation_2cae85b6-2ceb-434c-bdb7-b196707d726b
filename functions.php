<?php
/**
 * Boat Rental functions and definitions
 * @package Boat Rental
 */

if ( ! function_exists( 'boat_rental_after_theme_support' ) ) :

	function boat_rental_after_theme_support() {
		
		add_theme_support( 'automatic-feed-links' );

		add_theme_support(
			'custom-background',
			array(
				'default-color' => 'ffffff',
			)
		);

		$GLOBALS['content_width'] = apply_filters( 'boat_rental_content_width', 1140 );
		
		add_theme_support( 'post-thumbnails' );

		add_theme_support(
			'custom-logo',
			array(
				'height'      => 270,
				'width'       => 90,
				'flex-height' => true,
				'flex-width'  => true,
			)
		);
		
		add_theme_support( 'title-tag' );

        load_theme_textdomain( 'boat-rental', get_template_directory() . '/languages' );

		add_theme_support(
			'html5',
			array(
				'search-form',
				'comment-form',
				'comment-list',
				'gallery',
				'caption',
				'script',
				'style',
			)
		);

		add_theme_support( 'post-formats', array(
			'video',  
			'audio',  
			'gallery',
			'quote',  
			'image',  
			'link',   
			'status', 
			'aside',  
			'chat',   
		) );
		
		add_theme_support( 'align-wide' );
		add_theme_support( 'responsive-embeds' );
		add_theme_support( 'wp-block-styles' );

		require get_template_directory() . '/inc/metabox.php';
		require get_template_directory() . '/inc/homepage-setup/homepage-setup-settings.php';

		if (! defined( 'BOAT_RENTAL_DOCS_PRO' ) ){
		define('BOAT_RENTAL_DOCS_PRO',__('https://layout.omegathemes.com/steps/pro-boat-rental/','boat-rental'));
		}
		if (! defined( 'BOAT_RENTAL_BUY_NOW' ) ){
		define('BOAT_RENTAL_BUY_NOW',__('https://www.omegathemes.com/products/boat-rental-wordpress-theme','boat-rental'));
		}
		if (! defined( 'BOAT_RENTAL_SUPPORT_FREE' ) ){
		define('BOAT_RENTAL_SUPPORT_FREE',__('https://wordpress.org/support/theme/boat-rental/','boat-rental'));
		}
		if (! defined( 'BOAT_RENTAL_REVIEW_FREE' ) ){
		define('BOAT_RENTAL_REVIEW_FREE',__('https://wordpress.org/support/theme/boat-rental/reviews/#new-post/','boat-rental'));
		}
		if (! defined( 'BOAT_RENTAL_DEMO_PRO' ) ){
		define('BOAT_RENTAL_DEMO_PRO',__('https://layout.omegathemes.com/boat-rental/','boat-rental'));
		}
		if (! defined( 'BOAT_RENTAL_LITE_DOCS_PRO' ) ){
		define('BOAT_RENTAL_LITE_DOCS_PRO',__('https://layout.omegathemes.com/steps/free-boat-rental/','boat-rental'));
		}
		if (! defined( 'BOAT_RENTAL_BUNDLE_BUTTON' ) ){
			define('BOAT_RENTAL_BUNDLE_BUTTON',__('https://www.omegathemes.com/products/wp-theme-bundle','boat-rental'));
		}

	}

endif;

add_action( 'after_setup_theme', 'boat_rental_after_theme_support' );

/**
 * Register and Enqueue Styles.
 */
function boat_rental_register_styles() {

	wp_enqueue_style( 'dashicons' );

    $boat_rental_theme_version = wp_get_theme()->get( 'Version' );
	$boat_rental_fonts_url = boat_rental_fonts_url();
    if( $boat_rental_fonts_url ){
    	require get_theme_file_path( 'lib/custom/css/wptt-webfont-loader.php' );
        wp_enqueue_style(
			'boat-rental-google-fonts',
			wptt_get_webfont_url( $boat_rental_fonts_url ),
			array(),
			$boat_rental_theme_version
		);
    }
    wp_enqueue_style( 'owl.carousel', get_template_directory_uri() . '/lib/custom/css/owl.carousel.min.css');
    wp_enqueue_style( 'swiper', get_template_directory_uri() . '/lib/swiper/css/swiper-bundle.min.css');
	wp_enqueue_style( 'boat-rental-style', get_stylesheet_uri(), array(), $boat_rental_theme_version );

	wp_enqueue_style( 'boat-rental-style', get_stylesheet_uri() );
	require get_parent_theme_file_path( '/custom_css.php' );
	wp_add_inline_style( 'boat-rental-style',$boat_rental_custom_css );

	$boat_rental_css = '';

	if ( get_header_image() ) :

		$boat_rental_css .=  '
			.header-navbar{
				background-image: url('.esc_url(get_header_image()).');
				-webkit-background-size: cover !important;
				-moz-background-size: cover !important;
				-o-background-size: cover !important;
				background-size: cover !important;
			}';

	endif;

	wp_add_inline_style( 'boat-rental-style', $boat_rental_css );

	if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
		wp_enqueue_script( 'comment-reply' );
	}	

	wp_enqueue_script( 'imagesloaded' );
    wp_enqueue_script( 'masonry' );
    wp_enqueue_script( 'owl.carousel', get_template_directory_uri() . '/lib/custom/js/owl.carousel.js', array('jquery'), '', 1);
    wp_enqueue_script( 'swiper', get_template_directory_uri() . '/lib/swiper/js/swiper-bundle.min.js', array('jquery'), '', 1);
	wp_enqueue_script( 'boat-rental-custom', get_template_directory_uri() . '/lib/custom/js/theme-custom-script.js', array('jquery'), '', 1);

    // Global Query
    if( is_front_page() ){

    	$boat_rental_posts_per_page = absint( get_option('posts_per_page') );
        $boat_rental_c_paged = ( get_query_var( 'page' ) ) ? absint( get_query_var( 'page' ) ) : 1;
        $boat_rental_posts_args = array(
            'posts_per_page'        => $boat_rental_posts_per_page,
            'paged'                 => $boat_rental_c_paged,
        );
        $posts_qry = new WP_Query( $boat_rental_posts_args );
        $max = $posts_qry->max_num_pages;

    }else{
        global $wp_query;
        $max = $wp_query->max_num_pages;
        $boat_rental_c_paged = ( get_query_var( 'paged' ) > 1 ) ? get_query_var( 'paged' ) : 1;
    }

    $boat_rental_default = boat_rental_get_default_theme_options();
    $boat_rental_pagination_layout = get_theme_mod( 'boat_rental_pagination_layout',$boat_rental_default['boat_rental_pagination_layout'] );
}

add_action( 'wp_enqueue_scripts', 'boat_rental_register_styles',200 );

function boat_rental_admin_enqueue_scripts_callback() {
    if ( ! did_action( 'wp_enqueue_media' ) ) {
    wp_enqueue_media();
    }
    wp_enqueue_script('boat-rental-uploaderjs', get_stylesheet_directory_uri() . '/lib/custom/js/uploader.js', array(), "1.0", true);
}
add_action( 'admin_enqueue_scripts', 'boat_rental_admin_enqueue_scripts_callback' );

/**
 * Register navigation menus uses wp_nav_menu in five places.
 */
function boat_rental_menus() {

	$boat_rental_locations = array(
		'boat-rental-primary-menu'  => esc_html__( 'Primary Menu', 'boat-rental' ),
	);

	register_nav_menus( $boat_rental_locations );
}

add_action( 'init', 'boat_rental_menus' );

add_filter('loop_shop_columns', 'boat_rental_loop_columns');
if (!function_exists('boat_rental_loop_columns')) {
	function boat_rental_loop_columns() {
		$boat_rental_columns = get_theme_mod( 'boat_rental_per_columns', 3 );
		return $boat_rental_columns;
	}
}

add_filter( 'loop_shop_per_page', 'boat_rental_per_page', 20 );
function boat_rental_per_page( $boat_rental_cols ) {
  	$boat_rental_cols = get_theme_mod( 'boat_rental_product_per_page', 9 );
	return $boat_rental_cols;
}

function boat_rental_products_args( $boat_rental_args ) {

    $boat_rental_args['posts_per_page'] = get_theme_mod( 'boat_rental_custom_related_products_number', 6 );

    $boat_rental_args['columns'] = get_theme_mod( 'boat_rental_custom_related_products_number_per_row', 3 );

    return $boat_rental_args;
}

require get_template_directory() . '/inc/custom-header.php';
require get_template_directory() . '/classes/class-svg-icons.php';
require get_template_directory() . '/classes/class-walker-menu.php';
require get_template_directory() . '/inc/customizer/customizer.php';
require get_template_directory() . '/inc/custom-functions.php';
require get_template_directory() . '/inc/template-tags.php';
require get_template_directory() . '/classes/body-classes.php';
require get_template_directory() . '/inc/widgets/widgets.php';
require get_template_directory() . '/inc/pagination.php';
require get_template_directory() . '/lib/breadcrumbs/breadcrumbs.php';
require get_template_directory() . '/lib/custom/css/dynamic-style.php';
require get_template_directory() . '/inc/general.php';

function boat_rental_remove_customize_register() {
    global $wp_customize;

    $wp_customize->remove_setting( 'display_header_text' );
    $wp_customize->remove_control( 'display_header_text' );

}

add_action( 'customize_register', 'boat_rental_remove_customize_register', 11 );

function boat_rental_radio_sanitize(  $boat_rental_input, $boat_rental_setting  ) {
	$boat_rental_input = sanitize_key( $boat_rental_input );
	$boat_rental_choices = $boat_rental_setting->manager->get_control( $boat_rental_setting->id )->choices;
	return ( array_key_exists( $boat_rental_input, $boat_rental_choices ) ? $boat_rental_input : $boat_rental_setting->default );
}

add_filter( 'woocommerce_enable_setup_wizard', '__return_false' );

// NOTICE FUNCTION

function boat_rental_admin_notice() { 
    global $pagenow;
    $theme_args = wp_get_theme();
    $meta = get_option( 'boat_rental_admin_notice' );
    $name = $theme_args->get( 'Name' );
    $current_screen = get_current_screen();

    if ( ! $meta ) {
        if ( is_network_admin() ) {
            return;
        }

        if ( ! current_user_can( 'manage_options' ) ) {
            return;
        }

        if ( $current_screen->base != 'appearance_page_boatrental-wizard' ) {
            ?>
            <div class="notice notice-success notice-content">
                <h2><?php esc_html_e('Welcome! Thank you for choosing Boat Rental. Let’s Set Up Your Website!', 'boat-rental') ?> </h2>
                <p><?php esc_html_e('Before you dive into customization, let’s go through a quick setup process to ensure everything runs smoothly. Click below to start setting up your website in minutes!', 'boat-rental') ?> </p>
                <div class="info-link">
                    <a href="<?php echo esc_url( admin_url( 'themes.php?page=boatrental-wizard' ) ); ?>"><?php esc_html_e('Get Started with Boat Rental', 'boat-rental'); ?></a>
                </div>
                <p class="dismiss-link"><strong><a href="?boat_rental_admin_notice=1"><?php esc_html_e( 'Dismiss', 'boat-rental' ); ?></a></strong></p>
            </div>
            <?php
        }
    }
}
add_action( 'admin_notices', 'boat_rental_admin_notice' );

if ( ! function_exists( 'boat_rental_update_admin_notice' ) ) :
/**
 * Updating admin notice on dismiss
 */
function boat_rental_update_admin_notice() {
    if ( isset( $_GET['boat_rental_admin_notice'] ) && $_GET['boat_rental_admin_notice'] == '1' ) {
        update_option( 'boat_rental_admin_notice', true );
    }
}
endif;
add_action( 'admin_init', 'boat_rental_update_admin_notice' );

// After Switch theme function
add_action( 'after_switch_theme', 'boat_rental_getstart_setup_options' );
function boat_rental_getstart_setup_options() {
    update_option( 'boat_rental_admin_notice', false );
}

add_action( 'init', 'boat_rental_setup_defaults', 99 ); // run after most plugins

function boat_rental_setup_defaults() {

    // Make sure taxonomy is registered first
    if ( ! taxonomy_exists( 'boattaxonomy' ) ) {
        return; // do nothing until plugin registers it
    }

    // ---------------------------
    // Tab titles
    // ---------------------------
    $boat_rental_tab_title = array('Available Now','Luxury Boats','Adventure Boats');

    for ( $i = 1; $i <= 3; $i++ ) {
        $mod_key = 'boat_rental_price_list_tab_title' . $i;
        if ( ! get_theme_mod( $mod_key ) ) {
            set_theme_mod( $mod_key, $boat_rental_tab_title[$i - 1] );
        }
    }

    // ---------------------------
    // Category slugs
    // ---------------------------
    $category_slugs = array(
        'boat_rental_category_tab1' => 'cruiser-boat',
        'boat_rental_category_tab2' => 'speed-boat',
        'boat_rental_category_tab3' => 'fishing-boat',
    );

    foreach ( $category_slugs as $key => $slug ) {
        if ( ! get_theme_mod( $key ) ) {
            set_theme_mod( $key, $slug );
        }
    }

    // ---------------------------
    // Categories (taxonomy terms)
    // ---------------------------
    $tab_titles = array('Cruiser Boat', 'Speed Boat', 'Fishing Boat');

    foreach ( $tab_titles as $name ) {
        $slug = strtolower( str_replace( ' ', '-', $name ) );
        if ( ! term_exists( $slug, 'boattaxonomy' ) ) {
            wp_insert_term( $name, 'boattaxonomy', array( 'slug' => $slug ) );
        }
    }

    // ---------------------------
    // Posts
    // ---------------------------
    $post_titles = array(
        '2024 ThunderWave 350', '2024 OceanRider 420', '2024 AquaStorm 500',
        '2024 WaveMaster 360', '2024 SeaGlider 280', '2024 StormBreaker 400',
        '2024 HydroBlade 450', '2024 MarineX 300', '2024 TideRunner 370'
    );

    for ( $i = 1; $i <= 9; $i++ ) {
        $title         = $post_titles[$i - 1];
        $category_name = $tab_titles[ ($i - 1) % 3 ];
        $category_slug = strtolower( str_replace( ' ', '-', $category_name ) );

        // Check if post already exists
        $existing = get_page_by_title( $title, OBJECT, 'boatsrental' );
        if ( $existing ) {
            continue; // skip if post exists
        }

        // Create post
        $post_id = wp_insert_post( array(
            'post_title'    => wp_strip_all_tags( $title ),
            'post_content'  => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
            'post_status'   => 'publish',
            'post_type'     => 'boatsrental',
        ) );

        if ( ! $post_id ) {
            continue;
        }

        // Assign taxonomy term + tag
        wp_set_object_terms( $post_id, $category_slug, 'boattaxonomy', false );
        wp_set_object_terms( $post_id, 'Second', 'post_tag', true );

        // Meta
        update_post_meta( $post_id, 'boat_capacity', '6 Guests' );
        update_post_meta( $post_id, 'fuel_type', 'Petrol' );
        update_post_meta( $post_id, 'boat_engine', '300 hp Outboard' );
        update_post_meta( $post_id, 'boat_features', 'Jacuzzi & Lounge' );

        // Featured image (only if file exists)
        $image_url   = get_template_directory_uri() . '/inc/homepage-setup/assets/homepage-setup-images/post' . $i . '.png';
        $image_name  = 'post' . $i . '.png';
        $upload_dir  = wp_upload_dir();
        $image_data  = @file_get_contents( $image_url );

        if ( $image_data ) {
            $unique_file_name = wp_unique_filename( $upload_dir['path'], $image_name );
            $file_path        = $upload_dir['path'] . '/' . $unique_file_name;

            if ( ! function_exists( 'WP_Filesystem' ) ) {
                require_once ABSPATH . 'wp-admin/includes/file.php';
            }
            WP_Filesystem();
            global $wp_filesystem;

            $wp_filesystem->put_contents( $file_path, $image_data, FS_CHMOD_FILE );

            $filetype   = wp_check_filetype( $unique_file_name, null );
            $attachment = array(
                'post_mime_type' => $filetype['type'],
                'post_title'     => sanitize_file_name( $unique_file_name ),
                'post_content'   => '',
                'post_status'    => 'inherit'
            );

            $attach_id   = wp_insert_attachment( $attachment, $file_path, $post_id );
            require_once ABSPATH . 'wp-admin/includes/image.php';
            $attach_data = wp_generate_attachment_metadata( $attach_id, $file_path );
            wp_update_attachment_metadata( $attach_id, $attach_data );
            set_post_thumbnail( $post_id, $attach_id );
        }
    }
}
