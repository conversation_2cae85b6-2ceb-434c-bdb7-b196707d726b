<?php
/**
* Layouts Settings.
*
* @package Boat Rental
*/

$boat_rental_default = boat_rental_get_default_theme_options();

// Layout Section.
$wp_customize->add_section( 'boat_rental_layout_setting',
	array(
	'title'      => esc_html__( 'Sidebar Settings', 'boat-rental' ),
	'priority'   => 20,
	'capability' => 'edit_theme_options',
	'panel'      => 'boat_rental_theme_option_panel',
	)
);

$wp_customize->add_setting( 'boat_rental_global_sidebar_layout',
    array(
    'default'           => $boat_rental_default['boat_rental_global_sidebar_layout'],
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_sidebar_option',
    )
);
$wp_customize->add_control( 'boat_rental_global_sidebar_layout',
    array(
    'label'       => esc_html__( 'Global Sidebar Layout', 'boat-rental' ),
    'section'     => 'boat_rental_layout_setting',
    'type'        => 'select',
    'choices'     => array(
        'right-sidebar' => esc_html__( 'Right Sidebar', 'boat-rental' ),
        'left-sidebar'  => esc_html__( 'Left Sidebar', 'boat-rental' ),
        'no-sidebar'    => esc_html__( 'No Sidebar', 'boat-rental' ),
        ),
    )
);

$wp_customize->add_setting('boat_rental_page_sidebar_layout', array(
    'default'           => $boat_rental_default['boat_rental_global_sidebar_layout'],
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_sidebar_option',
));

$wp_customize->add_control('boat_rental_page_sidebar_layout', array(
    'label'       => esc_html__('Single Page Sidebar Layout', 'boat-rental'),
    'section'     => 'boat_rental_layout_setting',
    'type'        => 'select',
    'choices'     => array(
        'right-sidebar' => esc_html__('Right Sidebar', 'boat-rental'),
        'left-sidebar'  => esc_html__('Left Sidebar', 'boat-rental'),
        'no-sidebar'    => esc_html__('No Sidebar', 'boat-rental'),
    ),
));

$wp_customize->add_setting('boat_rental_post_sidebar_layout', array(
    'default'           => $boat_rental_default['boat_rental_global_sidebar_layout'],
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_sidebar_option',
));

$wp_customize->add_control('boat_rental_post_sidebar_layout', array(
    'label'       => esc_html__('Single Post Sidebar Layout', 'boat-rental'),
    'section'     => 'boat_rental_layout_setting',
    'type'        => 'select',
    'choices'     => array(
        'right-sidebar' => esc_html__('Right Sidebar', 'boat-rental'),
        'left-sidebar'  => esc_html__('Left Sidebar', 'boat-rental'),
        'no-sidebar'    => esc_html__('No Sidebar', 'boat-rental'),
    ),
));

$wp_customize->add_setting('boat_rental_sticky_sidebar',
    array(
        'default'           => true,
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_sticky_sidebar',
    array(
        'label' => esc_html__('Enable/Disable Sticky Sidebar', 'boat-rental'),
        'section' => 'boat_rental_layout_setting',
        'type' => 'checkbox',
    )
);