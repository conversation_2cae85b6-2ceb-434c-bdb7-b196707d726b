<?php
/**
* Header Options.
*
* @package Boat Rental
*/

$boat_rental_default = boat_rental_get_default_theme_options();

// Header Section.
$wp_customize->add_section( 'boat_rental_button_header_setting',
	array(
	'title'      => esc_html__( 'Header Settings', 'boat-rental' ),
	'priority'   => 10,
	'capability' => 'edit_theme_options',
	'panel'      => 'boat_rental_theme_option_panel',
	)
);

$wp_customize->add_setting('boat_rental_sticky',
    array(
        'default' => $boat_rental_default['boat_rental_sticky'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_sticky',
    array(
        'label' => esc_html__('Enable Sticky Header', 'boat-rental'),
        'section' => 'boat_rental_button_header_setting',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting('boat_rental_menu_font_size',
    array(
        'default'           => $boat_rental_default['boat_rental_menu_font_size'],
        'capability'        => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_number_range',
    )
);
$wp_customize->add_control('boat_rental_menu_font_size',
    array(
        'label'       => esc_html__('Menu Font Size', 'boat-rental'),
        'section'     => 'boat_rental_button_header_setting',
        'type'        => 'number',
        'input_attrs' => array(
           'min'   => 1,
           'max'   => 30,
           'step'   => 1,
        ),
    )
);

$wp_customize->add_setting( 'boat_rental_menu_text_transform',
    array(
    'default'           => $boat_rental_default['boat_rental_menu_text_transform'],
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_menu_transform',
    )
);
$wp_customize->add_control( 'boat_rental_menu_text_transform',
    array(
    'label'       => esc_html__( 'Menu Text Transform', 'boat-rental' ),
    'section'     => 'boat_rental_button_header_setting',
    'type'        => 'select',
    'choices'     => array(
        'capitalize' => esc_html__( 'Capitalize', 'boat-rental' ),
        'uppercase'  => esc_html__( 'Uppercase', 'boat-rental' ),
        'lowercase'    => esc_html__( 'Lowercase', 'boat-rental' ),
        ),
    )
);