<?php
/**
* Widget Functions.
*
* @package Boat Rental
*/

function boat_rental_widgets_init(){

	register_sidebar(array(
	    'name' => esc_html__('Main Sidebar', 'boat-rental'),
	    'id' => 'sidebar-1',
	    'description' => esc_html__('Add widgets here.', 'boat-rental'),
	    'before_widget' => '<div id="%1$s" class="widget %2$s">',
	    'after_widget' => '</div>',
	    'before_title' => '<h3 class="widget-title"><span>',
	    'after_title' => '</span></h3>',
	));


    $boat_rental_default = boat_rental_get_default_theme_options();
    $boat_rental_footer_column_layout = absint( get_theme_mod( 'boat_rental_footer_column_layout',$boat_rental_default['boat_rental_footer_column_layout'] ) );

    for( $i = 0; $i < $boat_rental_footer_column_layout; $i++ ){
    	
    	if( $i == 0 ){ $count = esc_html__('One','boat-rental'); }
    	if( $i == 1 ){ $count = esc_html__('Two','boat-rental'); }
    	if( $i == 2 ){ $count = esc_html__('Three','boat-rental'); }

	    register_sidebar( array(
	        'name' => esc_html__('Footer Widget ', 'boat-rental').$count,
	        'id' => 'boat-rental-footer-widget-'.$i,
	        'description' => esc_html__('Add widgets here.', 'boat-rental'),
	        'before_widget' => '<div id="%1$s" class="widget %2$s">',
	        'after_widget' => '</div>',
	        'before_title' => '<h2 class="widget-title">',
	        'after_title' => '</h2>',
	    ));
	}

}

add_action('widgets_init', 'boat_rental_widgets_init');