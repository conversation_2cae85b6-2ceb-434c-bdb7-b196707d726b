<?php
/**
 * The default template for displaying content
 * @package Boat Rental
 * @since 1.0.0
 */

$boat_rental_default = boat_rental_get_default_theme_options();

?>

<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>

	<?php if ( get_theme_mod('boat_rental_display_single_post_image', true) == true ) : ?>
		<?php if( is_single() && 'post' === get_post_type() ) {
			// Check if it is a single post page
			if ( has_post_thumbnail() ) { // If the post has a featured image
				?>
				<div class="post-thumbnail">
					<?php boat_rental_post_thumbnail(); ?>
				</div>
				<?php
			} else { 
				// No featured image, so show default image
				?>
				<div class="post-thumbnail">
					<img src="<?php echo esc_url( get_template_directory_uri() . '/assets/images/slide-bg.png' ); ?>" alt="<?php esc_attr_e( 'Boat Rental Default Image', 'boat-rental' ); ?>" />
				</div>
				<?php
			}
		} else { 
			// Don't show default image or featured image for other single pages (like static pages, etc.)
			?>
			<!-- Optionally you can add code for non-post pages here -->
		<?php } ?>
	<?php endif; ?>

	<?php if ( is_singular() ) { ?>

		<header class="entry-header entry-header-1">
			<h1 class="entry-title entry-title-large">
				<span><?php the_title(); ?></span>
			</h1>
		</header>

	<?php } ?>
	<div class="boat-meta sinle-beta-boat">
        <div class="meta1">
            <?php $boat_rental_boat_capacity = get_post_meta( get_the_ID(), 'boat_capacity', true );

            if ( ! empty( $boat_rental_boat_capacity ) ) : ?>
                <p class="location">
                    <?php echo esc_html('Capacity:','boat-rental'); ?>
                    <?php echo esc_html( $boat_rental_boat_capacity ); ?>
                </p>
            <?php endif; ?>
            <?php $boat_rental_boat_engine = get_post_meta( get_the_ID(), 'boat_engine', true );

            if ( ! empty( $boat_rental_boat_engine ) ) : ?>
                <p class="location">
                    <?php echo esc_html('Engine:','boat-rental'); ?>
                    <?php echo esc_html( $boat_rental_boat_engine ); ?>
                </p>
            <?php endif; ?>
        </div>
        <div class="meta2 ">
            <div class="features-box">
                <?php $boat_rental_boat_features = get_post_meta( get_the_ID(), 'boat_features', true );

                if ( ! empty( $boat_rental_boat_features ) ) : ?>
                    <p class="location">
                        <?php echo esc_html('Features:','boat-rental'); ?>
                        <?php echo esc_html( $boat_rental_boat_features ); ?>
                    </p>
                <?php endif; ?>
            </div>
            <?php $boat_rental_fuel_type = get_post_meta( get_the_ID(), 'fuel_type', true );

            if ( ! empty( $boat_rental_fuel_type ) ) : ?>
                <p class="location">
                    <?php echo esc_html('Fuel:','boat-rental'); ?>
                    <?php echo esc_html( $boat_rental_fuel_type ); ?>
                </p>
            <?php endif; ?>
        </div>
    </div>
	<?php if( is_single() && 'post' === get_post_type() ){ ?>

		<div class="entry-meta">
			<?php
			boat_rental_posted_by();
			boat_rental_posted_on();
			boat_rental_entry_footer( $cats = true, $tags = false, $edits = false );
			?>
		</div>

	<?php } ?>

	<div class="post-content-wrap">

		<div class="post-content">

			<div class="entry-content">

				<?php
				the_content( sprintf(
					/* translators: %s: Name of current post. */
					wp_kses( __( 'Read More %s <span class="meta-nav">&rarr;</span>', 'boat-rental' ), array( 'span' => array( 'class' => array() ) ) ),
					the_title( '<span class="screen-reader-text">"', '"</span>', false )
				) );

				wp_link_pages( array(
					'before' => '<div class="page-links">' . esc_html__( 'Pages:', 'boat-rental' ),
					'after'  => '</div>',
				) ); ?>

			</div>

			<?php
			if ( is_singular() && 'post' === get_post_type() ){ ?>

				<div class="entry-footer">
					<div class="entry-meta">
						<?php boat_rental_entry_footer( $cats = false, $tags = true, $edits = true ); ?>
					</div>
				</div>

			<?php } ?>

		</div>

	</div>

</article>