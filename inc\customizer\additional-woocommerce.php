<?php
/**
* Additional Woocommerce Settings.
*
* @package Boat Rental
*/

$boat_rental_default = boat_rental_get_default_theme_options();

// Additional Woocommerce Section.
$wp_customize->add_section( 'boat_rental_additional_woocommerce_options',
	array(
	'title'      => esc_html__( 'Additional Woocommerce Options', 'boat-rental' ),
	'priority'   => 210,
	'capability' => 'edit_theme_options',
	'panel'      => 'boat_rental_theme_option_panel',
	)
);

	$wp_customize->add_setting('boat_rental_per_columns',
		array(
		'default'           => $boat_rental_default['boat_rental_per_columns'],
		'capability'        => 'edit_theme_options',
		'sanitize_callback' => 'boat_rental_sanitize_number_range',
		)
	);
	$wp_customize->add_control('boat_rental_per_columns',
		array(
		'label'       => esc_html__('Products Per Column', 'boat-rental'),
		'section'     => 'boat_rental_additional_woocommerce_options',
		'type'        => 'number',
		'input_attrs' => array(
		'min'   => 1,
		'max'   => 6,
		'step'   => 1,
		),
		)
	);

	$wp_customize->add_setting('boat_rental_product_per_page',
		array(
		'default'           => $boat_rental_default['boat_rental_product_per_page'],
		'capability'        => 'edit_theme_options',
		'sanitize_callback' => 'boat_rental_sanitize_number_range',
		)
	);
	$wp_customize->add_control('boat_rental_product_per_page',
		array(
		'label'       => esc_html__('Products Per Page', 'boat-rental'),
		'section'     => 'boat_rental_additional_woocommerce_options',
		'type'        => 'number',
		'input_attrs' => array(
		'min'   => 1,
		'max'   => 100,
		'step'   => 1,
		),
		)
	);

	$wp_customize->add_setting('boat_rental_show_hide_related_product',
    array(
        'default' => $boat_rental_default['boat_rental_show_hide_related_product'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
	);
	$wp_customize->add_control('boat_rental_show_hide_related_product',
	    array(
	        'label' => esc_html__('Enable Related Products', 'boat-rental'),
	        'section' => 'boat_rental_additional_woocommerce_options',
	        'type' => 'checkbox',
	    )
	);

	$wp_customize->add_setting('boat_rental_custom_related_products_number',
		array(
		'default'           => $boat_rental_default['boat_rental_custom_related_products_number'],
		'capability'        => 'edit_theme_options',
		'sanitize_callback' => 'boat_rental_sanitize_number_range',
		)
	);
	$wp_customize->add_control('boat_rental_custom_related_products_number',
		array(
		'label'       => esc_html__('Related Products Per Page', 'boat-rental'),
		'section'     => 'boat_rental_additional_woocommerce_options',
		'type'        => 'number',
		'input_attrs' => array(
		'min'   => 1,
		'max'   => 10,
		'step'   => 1,
		),
		)
	);

	$wp_customize->add_setting('boat_rental_custom_related_products_number_per_row',
		array(
		'default'           => $boat_rental_default['boat_rental_custom_related_products_number_per_row'],
		'capability'        => 'edit_theme_options',
		'sanitize_callback' => 'boat_rental_sanitize_number_range',
		)
	);
	$wp_customize->add_control('boat_rental_custom_related_products_number_per_row',
		array(
		'label'       => esc_html__('Related Products Per Row', 'boat-rental'),
		'section'     => 'boat_rental_additional_woocommerce_options',
		'type'        => 'number',
		'input_attrs' => array(
		'min'   => 1,
		'max'   => 5,
		'step'   => 1,
		),
		)
	);