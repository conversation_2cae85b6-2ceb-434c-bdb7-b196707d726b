jQuery(document).ready(function ($) {
    "use strict";
    var isMobile = false;
    if (/Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        $('html').addClass('touch');
        isMobile = true;
    } else {
        $('html').addClass('no-touch');
        isMobile = false;
    }
    
    // Banner Slider
    var swiper = new Swiper('.theme-main-carousel', {
        centeredSlides: true,
        slidesPerView: 1,
        loop: true,
        spaceBetween: 30,
        speed: 1000,
        roundLengths: true,
        keyboard: true,
        parallax: true,
        mousewheel: false,
        grabCursor: true,
        navigation: {
            nextEl: '.custom-next',
            prevEl: '.custom-prev',
        },
        breakpoints: {
            768: {
                slidesPerView: 1,
            },
            1200: {
                slidesPerView: 1,
            },
            1600: {
                slidesPerView: 1,
            }
        }
    });

    // Scroll To
    $(".scroll-content").click(function () {
        $('html, body').animate({
            scrollTop: $("#site-content").offset().top
        }, 500);
    });
    // Aub Menu Toggle
    $('.submenu-toggle').click(function () {
        $(this).toggleClass('button-toggle-active');
        var currentClass = $(this).attr('data-toggle-target');
        $(currentClass).toggleClass('submenu-toggle-active');
    });
    $('.skip-link-menu-start').focus(function () {
        if (!$("#offcanvas-menu #primary-nav-offcanvas").length == 0) {
            $("#offcanvas-menu #primary-nav-offcanvas ul li:last-child a").focus();
        }
    });
    // Toggle Menu
    $('.navbar-control-offcanvas').click(function () {
        $(this).addClass('active');
        $('body').addClass('body-scroll-locked');
        $('#offcanvas-menu').toggleClass('offcanvas-menu-active');
        $('.button-offcanvas-close').focus();
    });
    $('.offcanvas-close .button-offcanvas-close').click(function () {
        $('#offcanvas-menu').removeClass('offcanvas-menu-active');
        $('.navbar-control-offcanvas').removeClass('active');
        $('body').removeClass('body-scroll-locked');
        setTimeout(function () {
            $('.navbar-control-offcanvas').focus();
        }, 300);
    });
    $('#offcanvas-menu').click(function () {
        $('#offcanvas-menu').removeClass('offcanvas-menu-active');
        $('.navbar-control-offcanvas').removeClass('active');
        $('body').removeClass('body-scroll-locked');
    });
    $(".offcanvas-wraper").click(function (e) {
        e.stopPropagation(); //stops click event from reaching document
    });
    $('.skip-link-menu-end').on('focus', function () {
        $('.button-offcanvas-close').focus();
    });
    // Data Background
    var pageSection = $(".data-bg");
    pageSection.each(function (indx) {
        if ($(this).attr("data-background")) {
            $(this).css("background-image", "url(" + $(this).data("background") + ")");
        }
    });
    // Scroll to Top on Click
    $('.to-the-top').click(function () {
        $("html, body").animate({
            scrollTop: 0
        }, 700);
        return false;
    });
});

jQuery(window).scroll(function() {
  var data_sticky = jQuery('.header-navbar').attr('data-sticky');

  if (data_sticky == "true") {
    if (jQuery(this).scrollTop() > 1){
      jQuery('.header-navbar').addClass("stick_head");
    } else {
      jQuery('.header-navbar').removeClass("stick_head");
    }
  }
});

//Loader
jQuery(window).load(function() {
  jQuery(".preloader").delay(1000).fadeOut("fast");
});

//last word js
jQuery(".header-titles .custom-logo-name").html(function(){
    var text2 = jQuery(this).text().trim().split(" ");
    var lastWord = text2.pop(); // Remove and store the last word
    if(text2.length > 0) {
        var remainingText = text2.join(" ");
        return `${remainingText} <span class='last-word'>${lastWord}</span>`;
    } else {
        return `<span class='last-word'>${lastWord}</span>`;
    }
});

document.addEventListener('DOMContentLoaded', function () {
    // Check if there's a stored active tab in the browser storage
    var storedTab = localStorage.getItem('activeTab');

    // If there's a stored active tab, show that tab
    if (storedTab) {
        showTabContent(storedTab);
    } else {
        // Otherwise, default to the first tab
        showTabContent(1);
    }
});

function showTabContent(tabNumber) {
    // Hide all tab panes
    var tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(function (tabContent) {
        tabContent.style.display = 'none';
    });

    // Show the selected tab pane
    var selectedTabContent = document.getElementById('tab-content-' + tabNumber);
    if (selectedTabContent) {
        selectedTabContent.style.display = 'block';
    }

    // Remove 'active' class from all tab items
    var tabItems = document.querySelectorAll('.tab-header .tab-item');
    tabItems.forEach(function (tabItem) {
        tabItem.classList.remove('active');
    });

    // Add 'active' class to the selected tab item
    var selectedTabItem = document.querySelector('.tab-header .tab-item:nth-child(' + tabNumber + ')');
    if (selectedTabItem) {
        selectedTabItem.classList.add('active');
    }

    // Store the active tab in the browser storage
    localStorage.setItem('activeTab', tabNumber);
}

jQuery(function($) {
  
    function boat_rental_initCarousel($carousel) {
      $carousel.owlCarousel({
        margin: 30,
        nav: false,
        autoplay: false,
        lazyLoad: true,
        autoplayTimeout: 5000,
        loop: true,
        dots: false,
        navText: ['<i class="fas fa-arrow-left" aria-hidden="true"></i>','<i class="fas fa-arrow-right" aria-hidden="true"></i>'],
        responsive: {
          0: { items: 1 },
          576: { items: 2 },
          768: { items: 2 },
          1000: { items: 2 },
          1200: { items: 3 }
        },
        autoplayHoverPause: false,
        mouseDrag: true,
        onInitialized: function(event) {
          boat_rental_updateActiveHeight($carousel);
        },
        onTranslated: function(event) {
          boat_rental_updateActiveHeight($carousel);
        }
      });
}

function boat_rental_updateActiveHeight($carousel) {
  $carousel.find('.owl-item').removeClass('active-height');

  // get active items only in this carousel
  var $activeItems = $carousel.find('.owl-item.active');

  // pick the center one
  var centerIndex = Math.floor($activeItems.length / 2);
  $activeItems.eq(centerIndex).addClass('active-height');
}

// Initial carousel setup for all
boat_rental_initCarousel($('.theme-product-block .owl-carousel'));

// Handle tab clicks
$('.tab-header .tab-item').on('click', function() {
  var $carousel = $('#' + $(this).data('tab-id') + ' .owl-carousel');

  if ($carousel.length) {
    // Destroy if already initialized
    if ($carousel.hasClass('owl-loaded')) {
      $carousel.trigger('destroy.owl.carousel');

      // Clean up Owl DOM wrappers properly
      $carousel.removeClass('owl-loaded owl-hidden');
      $carousel.find('.owl-stage-outer').children().unwrap();
      $carousel.find('.owl-stage').children().unwrap();
      $carousel.removeAttr('style');
      $carousel.children().removeAttr('style');
    }

    // Reinitialize only this carousel
    boat_rental_initCarousel($carousel);

    // Force update active height for this carousel only
    setTimeout(function() {
      boat_rental_updateActiveHeight($carousel);
    }, 200);
  }
});

});