<?php
/**
 * Custom Functions
 * @package Boat Rental
 * @since 1.0.0
 */

if( !function_exists('boat_rental_site_logo') ):

    /**
     * Logo & Description
     */
    /**
     * Displays the site logo, either text or image.
     *
     * @param array $boat_rental_args Arguments for displaying the site logo either as an image or text.
     * @param boolean $boat_rental_echo Echo or return the HTML.
     *
     * @return string $boat_rental_html Compiled HTML based on our arguments.
     */
    function boat_rental_site_logo( $boat_rental_args = array(), $boat_rental_echo = true ){
        $boat_rental_logo = get_custom_logo();
        $boat_rental_site_title = get_bloginfo('name');
        $boat_rental_contents = '';
        $boat_rental_classname = '';
        $boat_rental_defaults = array(
            'logo' => '%1$s<span class="screen-reader-text">%2$s</span>',
            'logo_class' => 'site-logo site-branding',
            'title' => '<a href="%1$s" class="custom-logo-name">%2$s</a>',
            'title_class' => 'site-title',
            'home_wrap' => '<h1 class="%1$s">%2$s</h1>',
            'single_wrap' => '<div class="%1$s">%2$s</div>',
            'condition' => (is_front_page() || is_home()) && !is_page(),
        );
        $boat_rental_args = wp_parse_args($boat_rental_args, $boat_rental_defaults);
        /**
         * Filters the arguments for `boat_rental_site_logo()`.
         *
         * @param array $boat_rental_args Parsed arguments.
         * @param array $boat_rental_defaults Function's default arguments.
         */
        $boat_rental_args = apply_filters('boat_rental_site_logo_args', $boat_rental_args, $boat_rental_defaults);
        
        $boat_rental_show_logo  = get_theme_mod('boat_rental_display_logo', false);
        $boat_rental_show_title = get_theme_mod('boat_rental_display_title', true);

        if ( has_custom_logo() && $boat_rental_show_logo ) {
            $boat_rental_contents .= sprintf($boat_rental_args['logo'], $boat_rental_logo, esc_html($boat_rental_site_title));
            $boat_rental_classname = $boat_rental_args['logo_class'];
        }

        if ( $boat_rental_show_title ) {
            $boat_rental_contents .= sprintf($boat_rental_args['title'], esc_url(get_home_url(null, '/')), esc_html($boat_rental_site_title));
            // If logo isn't shown, fallback to title class for wrapper.
            if ( !$boat_rental_show_logo ) {
                $boat_rental_classname = $boat_rental_args['title_class'];
            }
        }

        // If nothing is shown (logo or title both disabled), exit early
        if ( empty($boat_rental_contents) ) {
            return;
        }

        $boat_rental_wrap = $boat_rental_args['condition'] ? 'home_wrap' : 'single_wrap';
        // $boat_rental_wrap = 'home_wrap';
        $boat_rental_html = sprintf($boat_rental_args[$boat_rental_wrap], $boat_rental_classname, $boat_rental_contents);
        /**
         * Filters the arguments for `boat_rental_site_logo()`.
         *
         * @param string $boat_rental_html Compiled html based on our arguments.
         * @param array $boat_rental_args Parsed arguments.
         * @param string $boat_rental_classname Class name based on current view, home or single.
         * @param string $boat_rental_contents HTML for site title or logo.
         */
        $boat_rental_html = apply_filters('boat_rental_site_logo', $boat_rental_html, $boat_rental_args, $boat_rental_classname, $boat_rental_contents);
        if (!$boat_rental_echo) {
            return $boat_rental_html;
        }
        echo $boat_rental_html; //phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped

    }

endif;

if( !function_exists('boat_rental_site_description') ):

    /**
     * Displays the site description.
     *
     * @param boolean $boat_rental_echo Echo or return the html.
     *
     * @return string $boat_rental_html The HTML to display.
     */
    function boat_rental_site_description($boat_rental_echo = true){

        if ( get_theme_mod('boat_rental_display_header_text', false) == true ) :
        $boat_rental_description = get_bloginfo('description');
        if (!$boat_rental_description) {
            return;
        }
        $boat_rental_wrapper = '<div class="site-description"><span>%s</span></div><!-- .site-description -->';
        $boat_rental_html = sprintf($boat_rental_wrapper, esc_html($boat_rental_description));
        /**
         * Filters the html for the site description.
         *
         * @param string $boat_rental_html The HTML to display.
         * @param string $boat_rental_description Site description via `bloginfo()`.
         * @param string $boat_rental_wrapper The format used in case you want to reuse it in a `sprintf()`.
         * @since 1.0.0
         *
         */
        $boat_rental_html = apply_filters('boat_rental_site_description', $boat_rental_html, $boat_rental_description, $boat_rental_wrapper);
        if (!$boat_rental_echo) {
            return $boat_rental_html;
        }
        echo $boat_rental_html; //phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
        endif;
    }

endif;

if( !function_exists('boat_rental_posted_on') ):

    /**
     * Prints HTML with meta information for the current post-date/time.
     */
    function boat_rental_posted_on( $boat_rental_icon = true, $boat_rental_animation_class = '' ){

        $boat_rental_default = boat_rental_get_default_theme_options();
        $boat_rental_post_date = absint( get_theme_mod( 'boat_rental_post_date',$boat_rental_default['boat_rental_post_date'] ) );

        if( $boat_rental_post_date ){

            $boat_rental_time_string = '<time class="entry-date published updated" datetime="%1$s">%2$s</time>';
            if (get_the_time('U') !== get_the_modified_time('U')) {
                $boat_rental_time_string = '<time class="entry-date published" datetime="%1$s">%2$s</time><time class="updated" datetime="%3$s">%4$s</time>';
            }

            $boat_rental_time_string = sprintf($boat_rental_time_string,
                esc_attr(get_the_date(DATE_W3C)),
                esc_html(get_the_date()),
                esc_attr(get_the_modified_date(DATE_W3C)),
                esc_html(get_the_modified_date())
            );

            $boat_rental_year = get_the_date('Y');
            $boat_rental_month = get_the_date('m');
            $boat_rental_day = get_the_date('d');
            $boat_rental_link = get_day_link($boat_rental_year, $boat_rental_month, $boat_rental_day);

            $boat_rental_posted_on = '<a href="' . esc_url($boat_rental_link) . '" rel="bookmark">' . $boat_rental_time_string . '</a>';

            echo '<div class="entry-meta-item entry-meta-date">';
            echo '<div class="entry-meta-wrapper '.esc_attr( $boat_rental_animation_class ).'">';

            if( $boat_rental_icon ){

                echo '<span class="entry-meta-icon calendar-icon"> ';
                boat_rental_the_theme_svg('calendar');
                echo '</span>';

            }

            echo '<span class="posted-on">' . $boat_rental_posted_on . '</span>'; // phpcs:ignore Standard.Category.SniffName.ErrorCode
            echo '</div>';
            echo '</div>';

        }

    }

endif;

if( !function_exists('boat_rental_posted_by') ) :

    /**
     * Prints HTML with meta information for the current author.
     */
    function boat_rental_posted_by( $boat_rental_icon = true, $boat_rental_animation_class = '' ){   

        $boat_rental_default = boat_rental_get_default_theme_options();
        $boat_rental_post_author = absint( get_theme_mod( 'boat_rental_post_author',$boat_rental_default['boat_rental_post_author'] ) );

        if( $boat_rental_post_author ){

            echo '<div class="entry-meta-item entry-meta-author">';
            echo '<div class="entry-meta-wrapper '.esc_attr( $boat_rental_animation_class ).'">';

            if( $boat_rental_icon ){
            
                echo '<span class="entry-meta-icon author-icon"> ';
                boat_rental_the_theme_svg('user');
                echo '</span>';
                
            }

            $boat_rental_byline = '<span class="author vcard"><a class="url fn n" href="' . esc_url( get_author_posts_url( get_the_author_meta('ID') ) ) . '">' . esc_html(get_the_author()) . '</a></span>';
            echo '<span class="byline"> ' . $boat_rental_byline . '</span>'; // phpcs:ignore Standard.Category.SniffName.ErrorCode
            echo '</div>';
            echo '</div>';

        }

    }

endif;


if( !function_exists('boat_rental_posted_by_avatar') ) :

    /**
     * Prints HTML with meta information for the current author.
     */
    function boat_rental_posted_by_avatar( $boat_rental_date = false ){

        $boat_rental_default = boat_rental_get_default_theme_options();
        $boat_rental_post_author = absint( get_theme_mod( 'boat_rental_post_author',$boat_rental_default['boat_rental_post_author'] ) );

        if( $boat_rental_post_author ){



            echo '<div class="entry-meta-left">';
            echo '<div class="entry-meta-item entry-meta-avatar">';
            echo wp_kses_post( get_avatar( get_the_author_meta( 'ID' ) ) );
            echo '</div>';
            echo '</div>';

            echo '<div class="entry-meta-right">';

            $boat_rental_byline = '<span class="author vcard"><a class="url fn n" href="' . esc_url( get_author_posts_url( get_the_author_meta('ID') ) ) . '">' . esc_html(get_the_author()) . '</a></span>';

            echo '<div class="entry-meta-item entry-meta-byline"> ' . $boat_rental_byline . '</div>';

            if( $boat_rental_date ){
                boat_rental_posted_on($boat_rental_icon = false);
            }
            echo '</div>';

        }

    }

endif;

if( !function_exists('boat_rental_entry_footer') ):

    /**
     * Prints HTML with meta information for the categories, tags and comments.
     */
    function boat_rental_entry_footer( $boat_rental_cats = true, $boat_rental_tags = true, $boat_rental_edits = true){   

        $boat_rental_default = boat_rental_get_default_theme_options();
        $boat_rental_post_category = absint( get_theme_mod( 'boat_rental_post_category',$boat_rental_default['boat_rental_post_category'] ) );
        $boat_rental_post_tags = absint( get_theme_mod( 'boat_rental_post_tags',$boat_rental_default['boat_rental_post_tags'] ) );

        // Hide category and tag text for pages.
        if ('post' === get_post_type()) {

            if( $boat_rental_cats && $boat_rental_post_category ){

                /* translators: used between list items, there is a space after the comma */
                $boat_rental_categories = get_the_category();
                if ($boat_rental_categories) {
                    echo '<div class="entry-meta-item entry-meta-categories">';
                    echo '<div class="entry-meta-wrapper">';
                
                    /* translators: 1: list of categories. */
                    echo '<span class="cat-links">';
                    foreach( $boat_rental_categories as $boat_rental_category ){

                        $boat_rental_cat_name = $boat_rental_category->name;
                        $boat_rental_cat_slug = $boat_rental_category->slug;
                        $boat_rental_cat_url = get_category_link( $boat_rental_category->term_id );
                        ?>

                        <a class="twp_cat_<?php echo esc_attr( $boat_rental_cat_slug ); ?>" href="<?php echo esc_url( $boat_rental_cat_url ); ?>" rel="category tag"><?php echo esc_html( $boat_rental_cat_name ); ?></a>

                    <?php }
                    echo '</span>'; // phpcs:ignore Standard.Category.SniffName.ErrorCode
                    echo '</div>';
                    echo '</div>';
                }

            }

            if( $boat_rental_tags && $boat_rental_post_tags ){
                /* translators: used between list items, there is a space after the comma */
                $boat_rental_tags_list = get_the_tag_list('', esc_html_x(', ', 'list item separator', 'boat-rental'));
                if( $boat_rental_tags_list ){

                    echo '<div class="entry-meta-item entry-meta-tags">';
                    echo '<div class="entry-meta-wrapper">';

                    /* translators: 1: list of tags. */
                    echo '<span class="tags-links">';
                    echo wp_kses_post($boat_rental_tags_list) . '</span>'; // phpcs:ignore Standard.Category.SniffName.ErrorCode
                    echo '</div>';
                    echo '</div>';

                }

            }

            if( $boat_rental_edits ){

                edit_post_link(
                    sprintf(
                        wp_kses(
                        /* translators: %s: Name of current post. Only visible to screen readers */
                            __('Edit <span class="screen-reader-text">%s</span>', 'boat-rental'),
                            array(
                                'span' => array(
                                    'class' => array(),
                                ),
                            )
                        ),
                        get_the_title()
                    ),
                    '<span class="edit-link">',
                    '</span>'
                );
            }

        }
    }

endif;

if ( ! function_exists( 'boat_rental_post_thumbnail' ) ) :

    /**
     * Displays an optional post thumbnail.
     *
     * Shows background style image with height class on archive/search/front,
     * and a normal inline image on single post/page views.
     */
    function boat_rental_post_thumbnail( $boat_rental_image_size = 'full' ) {

        if ( post_password_required() || is_attachment() ) {
            return;
        }

        // Fallback image path
        $boat_rental_default_image = get_template_directory_uri() . '/assets/images/slide-bg.png';

        // Image size → height class map
        $boat_rental_size_class_map = array(
            'full'      => 'data-bg-large',
            'large'     => 'data-bg-big',
            'medium'    => 'data-bg-medium',
            'small'     => 'data-bg-small',
            'xsmall'    => 'data-bg-xsmall',
            'thumbnail' => 'data-bg-thumbnail',
        );

        $boat_rental_class = isset( $boat_rental_size_class_map[ $boat_rental_image_size ] )
            ? $boat_rental_size_class_map[ $boat_rental_image_size ]
            : 'data-bg-medium';

        if ( is_singular() ) {
            the_post_thumbnail();
        } else {
            // 🔵 On archives → use background image style
            $boat_rental_image = has_post_thumbnail()
                ? wp_get_attachment_image_src( get_post_thumbnail_id(), $boat_rental_image_size )
                : array( $boat_rental_default_image );

            $boat_rental_bg_image = isset( $boat_rental_image[0] ) ? $boat_rental_image[0] : $boat_rental_default_image;
            ?>
            <div class="post-thumbnail data-bg <?php echo esc_attr( $boat_rental_class ); ?>"
                 data-background="<?php echo esc_url( $boat_rental_bg_image ); ?>">
                <a href="<?php the_permalink(); ?>" class="theme-image-responsive" tabindex="0"></a>
            </div>
            <?php
        }
    }

endif;

if( !function_exists('boat_rental_is_comment_by_post_author') ):

    /**
     * Comments
     */
    /**
     * Check if the specified comment is written by the author of the post commented on.
     *
     * @param object $boat_rental_comment Comment data.
     *
     * @return bool
     */
    function boat_rental_is_comment_by_post_author($boat_rental_comment = null){

        if (is_object($boat_rental_comment) && $boat_rental_comment->user_id > 0) {
            $boat_rental_user = get_userdata($boat_rental_comment->user_id);
            $post = get_post($boat_rental_comment->comment_post_ID);
            if (!empty($boat_rental_user) && !empty($post)) {
                return $boat_rental_comment->user_id === $post->post_author;
            }
        }
        return false;
    }

endif;

if( !function_exists('boat_rental_breadcrumb') ) :

    /**
     * Boat Rental Breadcrumb
     */
    function boat_rental_breadcrumb($boat_rental_comment = null){

        echo '<div class="entry-breadcrumb">';
        boat_rental_breadcrumb_trail();
        echo '</div>';

    }

endif;