<?php
/**
* Custom Addons.
*
* @package Boat Rental
*/

$wp_customize->add_section( 'boat_rental_theme_pagination_options',
    array(
    'title'      => esc_html__( 'Customizer Custom Settings', 'boat-rental' ),
    'priority'   => 10,
    'capability' => 'edit_theme_options',
    'panel'      => 'boat_rental_theme_addons_panel',
    )
);

$wp_customize->add_setting('boat_rental_theme_loader',
    array(
        'default' => $boat_rental_default['boat_rental_theme_loader'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_theme_loader',
    array(
        'label' => esc_html__('Enable Preloader', 'boat-rental'),
        'section' => 'boat_rental_theme_pagination_options',
        'type' => 'checkbox',
    )
);

// Add Pagination Enable/Disable option to Customizer
$wp_customize->add_setting( 'boat_rental_enable_pagination', 
    array(
        'default'           => true, // Default is enabled
        'capability'        => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_enable_pagination', // Sanitize the input
    )
);

// Add the control to the Customizer
$wp_customize->add_control( 'boat_rental_enable_pagination', 
    array(
        'label'    => esc_html__( 'Enable Pagination', 'boat-rental' ),
        'section'  => 'boat_rental_theme_pagination_options', // Add to the correct section
        'type'     => 'checkbox',
    )
);

$wp_customize->add_setting( 'boat_rental_theme_pagination_type', 
    array(
        'default'           => 'numeric', // Set "numeric" as the default
        'capability'        => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_pagination_type', // Use our sanitize function
    )
);

$wp_customize->add_control( 'boat_rental_theme_pagination_type',
    array(
        'label'       => esc_html__( 'Pagination Style', 'boat-rental' ),
        'section'     => 'boat_rental_theme_pagination_options',
        'type'        => 'select',
        'choices'     => array(
            'numeric'      => esc_html__( 'Numeric (Page Numbers)', 'boat-rental' ),
            'newer_older'  => esc_html__( 'Newer/Older (Previous/Next)', 'boat-rental' ), // Renamed to "Newer/Older"
        ),
    )
);

$wp_customize->add_setting( 'boat_rental_theme_pagination_options_alignment',
    array(
    'default' => $boat_rental_default['boat_rental_theme_pagination_options_alignment'],
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_pagination_meta',
    )
);
$wp_customize->add_control( 'boat_rental_theme_pagination_options_alignment',
    array(
    'label'       => esc_html__( 'Pagination Alignment', 'boat-rental' ),
    'section'     => 'boat_rental_theme_pagination_options',
    'type'        => 'select',
    'choices'     => array(
        'Center'    => esc_html__( 'Center', 'boat-rental' ),
        'Right' => esc_html__( 'Right', 'boat-rental' ),
        'Left'  => esc_html__( 'Left', 'boat-rental' ),
        ),
    )
);

$wp_customize->add_setting('boat_rental_theme_breadcrumb_enable',
array(
    'default' => $boat_rental_default['boat_rental_theme_breadcrumb_enable'],
    'capability' => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_checkbox',
)
);
$wp_customize->add_control('boat_rental_theme_breadcrumb_enable',
    array(
        'label' => esc_html__('Enable Breadcrumb', 'boat-rental'),
        'section' => 'boat_rental_theme_pagination_options',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting( 'boat_rental_theme_breadcrumb_options_alignment',
    array(
    'default' => $boat_rental_default['boat_rental_theme_breadcrumb_options_alignment'],
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_pagination_meta',
    )
);
$wp_customize->add_control( 'boat_rental_theme_breadcrumb_options_alignment',
    array(
    'label'       => esc_html__( 'Breadcrumb Alignment', 'boat-rental' ),
    'section'     => 'boat_rental_theme_pagination_options',
    'type'        => 'select',
    'choices'     => array(
        'Center'    => esc_html__( 'Center', 'boat-rental' ),
        'Right' => esc_html__( 'Right', 'boat-rental' ),
        'Left'  => esc_html__( 'Left', 'boat-rental' ),
        ),
    )
);

$wp_customize->add_setting('boat_rental_breadcrumb_font_size',
    array(
        'default'           => $boat_rental_default['boat_rental_breadcrumb_font_size'],
        'capability'        => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_number_range',
    )
);
$wp_customize->add_control('boat_rental_breadcrumb_font_size',
    array(
        'label'       => esc_html__('Breadcrumb Font Size', 'boat-rental'),
        'section'     => 'boat_rental_theme_pagination_options',
        'type'        => 'number',
        'input_attrs' => array(
           'min'   => 1,
           'max'   => 45,
           'step'   => 1,
        ),
    )
);