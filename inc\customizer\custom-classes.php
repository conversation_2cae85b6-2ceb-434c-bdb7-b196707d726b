<?php
/**
* Customizer Custom Classes.
* @package Boat Rental
*/

if ( ! function_exists( 'boat_rental_sanitize_number_range' ) ) :
    function boat_rental_sanitize_number_range( $boat_rental_input, $boat_rental_setting ) {
        $boat_rental_input = absint( $boat_rental_input );
        $boat_rental_atts = $boat_rental_setting->manager->get_control( $boat_rental_setting->id )->input_attrs;
        $boat_rental_min = ( isset( $boat_rental_atts['min'] ) ? $boat_rental_atts['min'] : $boat_rental_input );
        $boat_rental_max = ( isset( $boat_rental_atts['max'] ) ? $boat_rental_atts['max'] : $boat_rental_input );
        $boat_rental_step = ( isset( $boat_rental_atts['step'] ) ? $boat_rental_atts['step'] : 1 );
        return ( $boat_rental_min <= $boat_rental_input && $boat_rental_input <= $boat_rental_max && is_int( $boat_rental_input / $boat_rental_step ) ? $boat_rental_input : $boat_rental_setting->default );
    }
endif;

/**
 * Upsell customizer section.
 *
 * @since  1.0.0
 * @access public
 */
class Boat_Rental_Customize_Section_Upsell extends WP_Customize_Section {

    /**
     * The type of customize section being rendered.
     *
     * @since  1.0.0
     * @access public
     * @var    string
     */
    public $type = 'upsell';

    /**
     * Custom button text to output.
     *
     * @since  1.0.0
     * @access public
     * @var    string
     */
    public $pro_text = '';

    /**
     * Custom pro button URL.
     *
     * @since  1.0.0
     * @access public
     * @var    string
     */
    public $pro_url = '';

    public $notice = '';
    public $nonotice = '';

    /**
     * Add custom parameters to pass to the JS via JSON.
     *
     * @since  1.0.0
     * @access public
     * @return void
     */
    public function json() {
        $json = parent::json();

        $json['pro_text'] = $this->pro_text;
        $json['pro_url']  = esc_url( $this->pro_url );
        $json['notice']  = esc_attr( $this->notice );
        $json['nonotice']  = esc_attr( $this->nonotice );

        return $json;
    }

    /**
     * Outputs the Underscore.js template.
     *
     * @since  1.0.0
     * @access public
     * @return void
     */
    protected function render_template() { ?>

        <li id="accordion-section-{{ data.id }}" class="accordion-section control-section control-section-{{ data.type }} cannot-expand">

            <# if ( data.notice ) { #>
                <h3 class="accordion-section-notice">
                    {{ data.title }}
                </h3>
            <# } #>

            <# if ( !data.notice ) { #>
                <h3 class="accordion-section-title">
                    {{ data.title }}

                    <# if ( data.pro_text && data.pro_url ) { #>
                        <a href="{{ data.pro_url }}" class="button button-secondary alignright" target="_blank">{{ data.pro_text }}</a>
                    <# } #>
                </h3>
            <# } #>
            
        </li>
    <?php }
}