<?php
/**
* Footer Settings.
*
* @package Boat Rental
*/

$boat_rental_default = boat_rental_get_default_theme_options();

$wp_customize->add_section( 'boat_rental_footer_widget_area',
	array(
	'title'      => esc_html__( 'Footer Settings', 'boat-rental' ),
	'priority'   => 200,
	'capability' => 'edit_theme_options',
	'panel'      => 'boat_rental_theme_option_panel',
	)
);

$wp_customize->add_setting('boat_rental_display_footer',
    array(
    'default' => $boat_rental_default['boat_rental_display_footer'],
    'capability' => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_checkbox',
)
);
$wp_customize->add_control('boat_rental_display_footer',
    array(
        'label' => esc_html__('Enable Footer', 'boat-rental'),
        'section' => 'boat_rental_footer_widget_area',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting( 'boat_rental_footer_column_layout',
	array(
	'default'           => $boat_rental_default['boat_rental_footer_column_layout'],
	'capability'        => 'edit_theme_options',
	'sanitize_callback' => 'boat_rental_sanitize_select',
	)
);
$wp_customize->add_control( 'boat_rental_footer_column_layout',
	array(
	'label'       => esc_html__( 'Footer Column Layout', 'boat-rental' ),
	'section'     => 'boat_rental_footer_widget_area',
	'type'        => 'select',
	'choices'               => array(
		'1' => esc_html__( 'One Column', 'boat-rental' ),
		'2' => esc_html__( 'Two Column', 'boat-rental' ),
		'3' => esc_html__( 'Three Column', 'boat-rental' ),
	    ),
	)
);

$wp_customize->add_setting( 'boat_rental_footer_widget_title_alignment',
    array(
    'default'           => $boat_rental_default['boat_rental_footer_widget_title_alignment'],
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_footer_widget_title_alignment',
    )
);
$wp_customize->add_control( 'boat_rental_footer_widget_title_alignment',
    array(
    'label'       => esc_html__( 'Footer Widget Title Alignment', 'boat-rental' ),
    'section'     => 'boat_rental_footer_widget_area',
    'type'        => 'select',
    'choices'     => array(
        'left' => esc_html__( 'Left', 'boat-rental' ),
        'center'  => esc_html__( 'Center', 'boat-rental' ),
        'right'    => esc_html__( 'Right', 'boat-rental' ),
        ),
    )
);

$wp_customize->add_setting( 'boat_rental_footer_copyright_text',
	array(
	'default'           => $boat_rental_default['boat_rental_footer_copyright_text'],
	'capability'        => 'edit_theme_options',
	'sanitize_callback' => 'sanitize_text_field',
	)
);
$wp_customize->add_control( 'boat_rental_footer_copyright_text',
	array(
	'label'    => esc_html__( 'Footer Copyright Text', 'boat-rental' ),
	'section'  => 'boat_rental_footer_widget_area',
	'type'     => 'text',
	)
);

$wp_customize->add_setting('boat_rental_copyright_font_size',
    array(
        'default'           => $boat_rental_default['boat_rental_copyright_font_size'],
        'capability'        => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_number_range',
    )
);
$wp_customize->add_control('boat_rental_copyright_font_size',
    array(
        'label'       => esc_html__('Copyright Font Size', 'boat-rental'),
        'section'     => 'boat_rental_footer_widget_area',
        'type'        => 'number',
        'input_attrs' => array(
           'min'   => 5,
           'max'   => 30,
           'step'   => 1,
    	),
    )
);

$wp_customize->add_setting( 'boat_rental_copyright_alignment', array(
    'default'           => 'Default',
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_copyright_alignment_meta',
) );

$wp_customize->add_control( 'boat_rental_copyright_alignment', array(
    'label'    => esc_html__( 'Copyright Section Alignment', 'boat-rental' ),
    'section'  => 'boat_rental_footer_widget_area',
    'type'     => 'select',
    'choices'  => array(
        'Default' => esc_html__( 'Default View', 'boat-rental' ),
        'Reverse' => esc_html__( 'Reverse View', 'boat-rental' ),
        'Center'  => esc_html__( 'Centered Content', 'boat-rental' ),
    ),
) );

$wp_customize->add_setting( 'boat_rental_footer_widget_background_color', array(
    'default' => '',
    'sanitize_callback' => 'sanitize_hex_color'
));
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'boat_rental_footer_widget_background_color', array(
    'label'     => __('Footer Widget Background Color', 'boat-rental'),
    'description' => __('It will change the complete footer widget background color.', 'boat-rental'),
    'section' => 'boat_rental_footer_widget_area',
    'settings' => 'boat_rental_footer_widget_background_color',
)));

$wp_customize->add_setting('boat_rental_footer_widget_background_image',array(
    'default'   => '',
    'sanitize_callback' => 'esc_url_raw',
));
$wp_customize->add_control( new WP_Customize_Image_Control($wp_customize,'boat_rental_footer_widget_background_image',array(
    'label' => __('Footer Widget Background Image','boat-rental'),
    'section' => 'boat_rental_footer_widget_area'
)));

$wp_customize->add_setting('boat_rental_enable_to_the_top',
    array(
        'default' => $boat_rental_default['boat_rental_enable_to_the_top'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_enable_to_the_top',
    array(
        'label' => esc_html__('Enable To The Top', 'boat-rental'),
        'section' => 'boat_rental_footer_widget_area',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting( 'boat_rental_to_the_top_text',
    array(
    'default'           => $boat_rental_default['boat_rental_to_the_top_text'],
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'sanitize_text_field',
    )
);
$wp_customize->add_control( 'boat_rental_to_the_top_text',
    array(
    'label'    => esc_html__( 'Edit Text Here', 'boat-rental' ),
    'section'  => 'boat_rental_footer_widget_area',
    'type'     => 'text',
    )
);