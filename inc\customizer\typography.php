<?php
/**
* Typography Settings.
*
* @package Boat Rental
*/

$boat_rental_default = boat_rental_get_default_theme_options();

// Layout Section.
$wp_customize->add_section( 'boat_rental_typography_setting',
	array(
	'title'      => esc_html__( 'Typography Settings', 'boat-rental' ),
	'priority'   => 21,
	'capability' => 'edit_theme_options',
	'panel'      => 'boat_rental_theme_option_panel',
	)
);

// -----------------  Font array
$boat_rental_fonts = array(
    'Select'           => __('Default Font', 'boat-rental'),
    'bad-script' => 'Bad Script',
    'bitter'     => 'Bitter',
    'charis-sil' => 'Charis SIL',
    'cuprum'     => 'Cuprum',
    'exo-2'      => 'Exo 2',
    'jost'       => 'Jost',
    'open-sans'  => 'Open Sans',
    'oswald'     => 'Oswald',
    'play'       => 'Play',
    'roboto'     => 'Roboto',
    'outfit'     => 'Outfit',
    'ubuntu'     => 'Ubuntu',
    'saira'      => 'Saira'
);

 // -----------------  General text font
 $wp_customize->add_setting( 'boat_rental_content_typography_font', array(
    'default'           => 'jost',
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_radio_sanitize',
) );
$wp_customize->add_control( 'boat_rental_content_typography_font', array(
    'type'     => 'select',
    'label'    => esc_html__( 'General Content Font', 'boat-rental' ),
    'section'  => 'boat_rental_typography_setting',
    'settings' => 'boat_rental_content_typography_font',
    'choices'  => $boat_rental_fonts,
) );

 // -----------------  General Heading Font
$wp_customize->add_setting( 'boat_rental_heading_typography_font', array(
    'default'           => 'jost',
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_radio_sanitize',
) );
$wp_customize->add_control( 'boat_rental_heading_typography_font', array(
    'type'     => 'select',
    'label'    => esc_html__( 'General Heading Font', 'boat-rental' ),
    'section'  => 'boat_rental_typography_setting',
    'settings' => 'boat_rental_heading_typography_font',
    'choices'  => $boat_rental_fonts,
) );