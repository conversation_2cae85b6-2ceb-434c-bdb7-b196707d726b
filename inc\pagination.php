<?php
/**
 *
 * Pagination Functions
 *
 * @package Boat Rental
 */

/**
 * Pagination for archive.
 */
function boat_rental_render_posts_pagination() {
    // Get the setting to check if pagination is enabled
    $boat_rental_is_pagination_enabled = get_theme_mod( 'boat_rental_enable_pagination', true );

    // Check if pagination is enabled
    if ( $boat_rental_is_pagination_enabled ) {
        // Get the selected pagination type from the Customizer
        $boat_rental_pagination_type = get_theme_mod( 'boat_rental_theme_pagination_type', 'numeric' );

        // Check if the pagination type is "newer_older" (Previous/Next) or "numeric"
        if ( 'newer_older' === $boat_rental_pagination_type ) :
            // Display "Newer/Older" pagination (Previous/Next navigation)
            the_posts_navigation(
                array(
                    'prev_text' => __( '&laquo; Newer', 'boat-rental' ),  // Change the label for "previous"
                    'next_text' => __( 'Older &raquo;', 'boat-rental' ),  // Change the label for "next"
                    'screen_reader_text' => __( 'Posts navigation', 'boat-rental' ),
                )
            );
        else :
            // Display numeric pagination (Page numbers)
            the_posts_pagination(
                array(
                    'prev_text' => __( '&laquo; Previous', 'boat-rental' ),
                    'next_text' => __( 'Next &raquo;', 'boat-rental' ),
                    'type'      => 'list', // Display as <ul> <li> tags
                    'screen_reader_text' => __( 'Posts navigation', 'boat-rental' ),
                )
            );
        endif;
    }
}
add_action( 'boat_rental_posts_pagination', 'boat_rental_render_posts_pagination', 10 );