<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20201107 at Fri Mar 17 16:10:02 2023
 By 
Copyright 2021 The Outfit Project Authors (https://github.com/Outfitio/Outfit-Fonts)
</metadata>
<defs>
<font id="Outfit-Regular" horiz-adv-x="590" >
  <font-face 
    font-family="Outfit"
    font-weight="400"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="0 0 0 0 0 0 0 0 0 0"
    ascent="800"
    descent="-200"
    x-height="475"
    cap-height="694"
    bbox="-128 -338 3614 988"
    underline-thickness="50"
    underline-position="-125"
    unicode-range="U+000D-FB02"
  />
<missing-glyph horiz-adv-x="686" 
d="M81 0v694h522v-694h-522zM175 94h334v506h-334v-506zM211 438l41 41l222 -222l-41 -41zM438 485l41 -42l-232 -233l-41 42z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="573" 
d="M134 0v561q0 47 20.5 83.5t57.5 58t85 21.5q37 0 65 -12.5t52 -34.5l-59 -59q-13 11 -27 16.5t-31 5.5q-35 0 -54 -21.5t-19 -57.5v-561h-90zM17 393v82h451v-82h-451zM416 0v475h91v-475h-91zM461 525q-24 0 -40 16.5t-16 41.5q0 24 16 40.5t40 16.5q26 0 41.5 -16.5
t15.5 -40.5q0 -25 -15.5 -41.5t-41.5 -16.5z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="573" 
d="M134 0v539q0 50 23 92t66.5 67.5t100.5 25.5v-84q-49 0 -74.5 -29.5t-25.5 -71.5v-539h-90zM17 393v82h437v-82h-437zM417 0v718h90v-718h-90z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="682" 
d="M134 0v527q0 55 23.5 96.5t65.5 66t97 24.5q56 0 92 -24t53.5 -65.5t17.5 -93.5h-66q-1 46 -25 73.5t-72 27.5q-47 0 -71.5 -27.5t-24.5 -75.5v-529h-90zM17 393v82h641v-82h-641zM417 0v546q0 53 23 92.5t63.5 62.5t94.5 23q41 0 71 -14t55 -41l-59 -58q-13 14 -29 21.5
t-39 7.5q-43 0 -66.5 -25t-23.5 -69v-546h-90z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="571" 
d="M134 0v561q0 47 20.5 83.5t57.5 58t85 21.5q37 0 65 -12.5t52 -34.5l-59 -59q-13 11 -27 16.5t-31 5.5q-35 0 -54 -21.5t-19 -57.5v-561h-90zM17 393v82h451v-82h-451zM348 -209q-46 0 -77 15t-56 43l58 59q14 -17 30.5 -25t39.5 -8q30 0 51.5 18.5t21.5 55.5v526h91
v-525q0 -52 -22 -87t-58 -53.5t-79 -18.5zM461 525q-24 0 -40 16.5t-16 41.5q0 24 16 40.5t40 16.5q26 0 41.5 -16.5t15.5 -40.5q0 -25 -15.5 -41.5t-41.5 -16.5z" />
    <glyph glyph-name="o_u_t_f_i_t" unicode="outfit" horiz-adv-x="2304" 
d="M283 -10q-70 0 -126 33t-89 89.5t-33 126.5q0 69 33 124.5t89 88.5t126 33q69 0 125.5 -32.5t89.5 -88.5t33 -125q0 -70 -33 -126.5t-89.5 -89.5t-125.5 -33zM283 77q45 0 80 21t55 57.5t20 83.5q0 46 -20.5 82t-55 56.5t-79.5 20.5t-80 -20.5t-55 -56.5t-20 -82
q0 -47 20 -83.5t55 -57.5t80 -21zM823 -10q-59 0 -105 26.5t-72 73.5t-26 109v276h90v-272q0 -39 13.5 -67.5t39.5 -43.5t60 -15q54 0 84 33.5t30 92.5v272h90v-276q0 -62 -26 -109t-71.5 -73.5t-106.5 -26.5zM986 393v82h1296v-82h-1296zM1203 0v674h91v-674h-91zM1486 0
v561q0 47 21 83.5t58 58t85 21.5q36 0 64.5 -12.5t51.5 -34.5l-59 -59q-12 11 -26 16.5t-31 5.5q-35 0 -54 -21.5t-19 -57.5v-561h-91zM1769 0v475h91v-475h-91zM1814 526q-23 0 -38.5 16.5t-15.5 39.5t15.5 39t38.5 16q24 0 39.5 -16t15.5 -39t-15.5 -39.5t-39.5 -16.5z
M2052 0v674h91v-674h-91z" />
    <glyph glyph-name="o_u_t_f_i_t_t_e_d" unicode="outfitted" horiz-adv-x="3680" 
d="M283 -10q-70 0 -126 33t-89 89.5t-33 126.5q0 69 33 124.5t89 88.5t126 33q69 0 125.5 -32.5t89.5 -88.5t33 -125q0 -70 -33 -126.5t-89.5 -89.5t-125.5 -33zM283 77q45 0 80 21t55 57.5t20 83.5q0 46 -20.5 82t-55 56.5t-79.5 20.5t-80 -20.5t-55 -56.5t-20 -82
q0 -47 20 -83.5t55 -57.5t80 -21zM823 -10q-59 0 -105 26.5t-72 73.5t-26 109v276h90v-272q0 -39 13.5 -67.5t39.5 -43.5t60 -15q54 0 84 33.5t30 92.5v272h90v-276q0 -62 -26 -109t-71.5 -73.5t-106.5 -26.5zM1203 0v674h91v-674h-91zM1486 0v561q0 47 21 83.5t58 58
t85 21.5q36 0 64.5 -12.5t51.5 -34.5l-59 -59q-12 11 -26 16.5t-31 5.5q-35 0 -54 -21.5t-19 -57.5v-561h-91zM1769 0v475h91v-475h-91zM986 393v82h1578v-82h-1578zM1814 526q-23 0 -38.5 16.5t-15.5 39.5t15.5 39t38.5 16q24 0 39.5 -16t15.5 -39t-15.5 -39.5t-39.5 -16.5
zM2052 0v674h91v-674h-91zM2335 0v674h91v-674h-91zM2847 -10q-71 0 -128 32.5t-90 88.5t-33 127q0 70 32.5 126t88.5 88.5t124 32.5q66 0 116.5 -30t79.5 -83t29 -120q0 -10 -1.5 -21.5t-4.5 -26.5h-400v75h353l-33 -29q-1 48 -17.5 81.5t-47.5 52t-76 18.5
q-46 0 -80.5 -20t-54 -56t-19.5 -85q0 -50 20.5 -87.5t57 -58t84.5 -20.5q40 0 74 14t57 42l58 -59q-34 -40 -83.5 -61t-105.5 -21zM3364 -10q-65 0 -117.5 32.5t-83 88.5t-30.5 126t30.5 126t83 89t117.5 33q54 0 96.5 -22.5t69.5 -62t29 -91.5v-143q-3 -51 -29 -91
t-69 -62.5t-97 -22.5zM3379 75q45 0 78.5 21t52.5 57.5t19 83.5q0 48 -19 85t-53 57.5t-79 20.5q-44 0 -78.5 -20.5t-54 -57.5t-19.5 -84q0 -48 19.5 -84.5t54 -57.5t79.5 -21zM3524 0v128l16 116l-16 115v355h90v-714h-90z" />
    <glyph glyph-name="o_u_t_f_i_t_t_e_r" unicode="outfitter" horiz-adv-x="3517" 
d="M283 -10q-70 0 -126 33t-89 89.5t-33 126.5q0 69 33 124.5t89 88.5t126 33q69 0 125.5 -32.5t89.5 -88.5t33 -125q0 -70 -33 -126.5t-89.5 -89.5t-125.5 -33zM283 77q45 0 80 21t55 57.5t20 83.5q0 46 -20.5 82t-55 56.5t-79.5 20.5t-80 -20.5t-55 -56.5t-20 -82
q0 -47 20 -83.5t55 -57.5t80 -21zM823 -10q-59 0 -105 26.5t-72 73.5t-26 109v276h90v-272q0 -39 13.5 -67.5t39.5 -43.5t60 -15q54 0 84 33.5t30 92.5v272h90v-276q0 -62 -26 -109t-71.5 -73.5t-106.5 -26.5zM1203 0v674h91v-674h-91zM1486 0v561q0 47 21 83.5t58 58
t85 21.5q36 0 64.5 -12.5t51.5 -34.5l-59 -59q-12 11 -26 16.5t-31 5.5q-35 0 -54 -21.5t-19 -57.5v-561h-91zM1769 0v475h91v-475h-91zM986 393v82h1578v-82h-1578zM1814 526q-23 0 -38.5 16.5t-15.5 39.5t15.5 39t38.5 16q24 0 39.5 -16t15.5 -39t-15.5 -39.5t-39.5 -16.5
zM2052 0v674h91v-674h-91zM2335 0v674h91v-674h-91zM2847 -10q-71 0 -128 32.5t-90 88.5t-33 127q0 70 32.5 126t88.5 88.5t124 32.5q66 0 116.5 -30t79.5 -83t29 -120q0 -10 -1.5 -21.5t-4.5 -26.5h-400v75h353l-33 -29q-1 48 -17.5 81.5t-47.5 52t-76 18.5
q-46 0 -80.5 -20t-54 -56t-19.5 -85q0 -50 20.5 -87.5t57 -58t84.5 -20.5q40 0 74 14t57 42l58 -59q-34 -40 -83.5 -61t-105.5 -21zM3163 0v475h90v-475h-90zM3253 271l-33 15q0 91 42 145t121 54q35 0 64 -12.5t54 -41.5l-59 -61q-15 16 -33 23t-42 7q-50 0 -82 -32
t-32 -97z" />
    <glyph glyph-name="t_t" unicode="tt" horiz-adv-x="651" 
d="M139 0v674h90v-674h-90zM22 393v82h607v-82h-607zM422 0v674h90v-674h-90z" />
    <glyph glyph-name=".notdef" horiz-adv-x="686" 
d="M81 0v694h522v-694h-522zM175 94h334v506h-334v-506zM211 438l41 41l222 -222l-41 -41zM438 485l41 -42l-232 -233l-41 42z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="CR" unicode="&#xd;" horiz-adv-x="600" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="208" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="261" 
d="M93 220l-13 494h96l-13 -494h-70zM127 -10q-28 0 -46 19t-18 47t18 46.5t46 18.5q29 0 47 -18.5t18 -46.5t-18 -47t-47 -19z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="400" 
d="M253 470l-21 244h100l-22 -244h-57zM90 470l-21 244h99l-21 -244h-57z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="640" 
d="M332 0l106 694h77l-106 -694h-77zM48 214v73h512v-73h-512zM118 0l106 694h77l-106 -694h-77zM79 421v73h512v-73h-512z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="588" 
d="M290 -10q-76 0 -132.5 30t-100.5 87l63 63q32 -45 74 -71t99 -26q61 0 98.5 28t37.5 78q0 39 -18 63t-48 39.5t-66 28t-71.5 27t-65.5 36t-48 56.5t-18 88q0 59 28.5 100.5t77.5 64t110 22.5q63 0 115 -26.5t86 -69.5l-63 -63q-31 36 -65 56t-75 20q-58 0 -91 -25
t-33 -73q0 -35 18.5 -56.5t49 -36.5t66 -27.5t72 -27.5t66.5 -38t48.5 -59.5t18.5 -91.5q0 -92 -63 -144t-170 -52zM276 -100v894h66v-894h-66z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="660" 
d="M62 0l445 694h91l-445 -694h-91zM469 -10q-39 0 -71 19t-51 50.5t-19 71.5q0 39 19 70.5t51 50.5t71 19t71.5 -19t51.5 -50.5t19 -70.5q0 -40 -19 -71.5t-51 -50.5t-72 -19zM469 61q30 0 49 19.5t19 50.5q0 30 -19.5 49.5t-48.5 19.5t-48 -19.5t-19 -49.5q0 -31 19 -50.5
t48 -19.5zM190 423q-38 0 -70 19t-51 51t-19 71q0 40 18.5 71t50.5 50t71 19q40 0 72 -19t51 -50t19 -71q0 -39 -19 -71t-50.5 -51t-72.5 -19zM190 494q30 0 49 19.5t19 50.5t-19.5 50t-48.5 19t-47.5 -19t-18.5 -50t18.5 -50.5t47.5 -19.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="659" 
d="M537 0l-332 356q-39 42 -58 80t-19 84q0 53 25 94.5t69 66t101 24.5q42 0 76.5 -13.5t61 -35.5t44.5 -50l-65 -59q-23 32 -50.5 51.5t-66.5 19.5q-45 0 -75 -27t-30 -70q0 -36 15 -61t45 -58l369 -402h-110zM288 -10q-69 0 -121 27t-80.5 74.5t-28.5 108.5q0 63 34 114
t99 81l43 -56q-40 -20 -63 -54.5t-23 -82.5q0 -39 18 -67t50.5 -43.5t74.5 -15.5q53 0 90 22t56 56l59 -69q-20 -27 -52.5 -48.5t-72 -34t-83.5 -12.5z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="237" 
d="M90 470l-21 244h99l-21 -244h-57z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="303" 
d="M221 -116q-55 46 -94 113.5t-60 147.5t-21 166t21 166t60 147.5t94 113.5l52 -47q-71 -62 -109.5 -160t-38.5 -220q0 -121 38.5 -219t109.5 -161z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="302" 
d="M82 -116l-52 47q71 63 109 161t38 219q0 122 -38 220t-109 160l52 47q55 -46 94 -113.5t60 -147.5t21 -166t-21 -166t-60 -147.5t-94 -113.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="490" 
d="M228 342l-8 159l-133 -86l-38 75l148 56l-122 97l59 59l99 -121l56 147l76 -38l-87 -132l160 -8l-13 -82l-155 41l42 -154z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="542" 
d="M59 294v81h424v-81h-424zM230 557h83v-445h-83v445z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="275" 
d="M71 -110l62 119l32 8q-5 -11 -12.5 -18t-18.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t48 19.5q26 0 46 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5l-66 -125z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="461" 
d="M66 212v85h329v-85h-329z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="290" 
d="M145 -10q-28 0 -46.5 19.5t-18.5 46.5q0 28 18.5 46.5t46.5 18.5t46.5 -18.5t18.5 -46.5q0 -27 -18.5 -46.5t-46.5 -19.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="390" 
d="M14 -40l278 773h84l-277 -773h-85z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="656" 
d="M330 -10q-78 0 -142.5 44t-102.5 124.5t-38 189.5t37.5 189t101.5 123.5t141 43.5q79 0 143 -43.5t101.5 -123.5t37.5 -190q0 -109 -37.5 -189.5t-101 -124t-140.5 -43.5zM328 79q55 0 96.5 30.5t65.5 90.5t24 148t-24 147.5t-66 90t-97 30.5q-54 0 -96 -30.5t-65.5 -90
t-23.5 -147.5t23.5 -148t65.5 -90.5t97 -30.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="347" 
d="M173 0v694h93v-694h-93zM31 610v84h225v-84h-225z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="541" 
d="M33 57l256 270q39 40 61 69.5t31.5 54.5t9.5 52q0 53 -34 83t-88 30q-53 0 -93 -25.5t-71 -78.5l-66 55q41 68 99 102.5t134 34.5q64 0 111.5 -25t73.5 -70t26 -105q0 -43 -10.5 -77t-37 -69.5t-73.5 -84.5l-207 -213zM33 0v57l80 27h386v-84h-466z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="538" 
d="M247 -10q-68 0 -123.5 25.5t-93.5 74.5l64 64q23 -35 63 -55.5t89 -20.5q46 0 79.5 17.5t53 49.5t19.5 74q0 43 -19.5 74.5t-54.5 48.5t-83 17q-18 0 -36 -2.5t-35 -7.5l40 54q19 9 41 14t44 5q56 0 100.5 -26t71 -73t26.5 -109q0 -66 -31.5 -116.5t-86.5 -79t-128 -28.5
zM170 349v57l203 231l112 1l-209 -237zM63 610v84h422v-56l-82 -28h-340z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="598" 
d="M33 239l253 455h106l-258 -455h-101zM33 181v58l40 26h492v-84h-532zM372 0v459h93v-459h-93z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="541" 
d="M248 -10q-71 0 -126.5 25.5t-93.5 74.5l64 64q23 -35 63.5 -55.5t90.5 -20.5q47 0 82.5 18t56 50.5t20.5 77.5q0 46 -20 78t-53.5 48.5t-73.5 16.5q-41 0 -74.5 -10t-63.5 -34l1 60q17 21 39 34t50 20t65 7q72 0 122 -30t76.5 -81t26.5 -114q0 -66 -32.5 -118
t-89.5 -81.5t-130 -29.5zM120 323l-51 51l31 320h86l-35 -329zM119 610l-19 84h365v-84h-346z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="564" 
d="M282 -10q-69 0 -123.5 32t-86.5 86t-32 120q0 92 64 183l204 283h109l-209 -285l-34 -18q12 21 29 35.5t42 22.5t59 8q61 0 110.5 -29t80 -79.5t30.5 -117.5q0 -66 -32.5 -121t-88 -87.5t-122.5 -32.5zM282 78q42 0 75.5 20.5t53 55t19.5 77.5t-19.5 77.5t-53 54.5
t-75.5 20t-75.5 -20t-53 -54.5t-19.5 -77.5t19.5 -78t53 -55t75.5 -20z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="525" 
d="M140 0l260 639l93 -3l-254 -636h-99zM31 610v84h462v-58l-41 -26h-421z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="550" 
d="M275 -10q-69 0 -121.5 26t-81.5 72t-29 104q0 47 18 86t53 66.5t82 39.5l3 -27q-40 11 -68 35.5t-42.5 58t-14.5 71.5q0 54 26 95t71.5 64.5t103.5 23.5q59 0 104 -23.5t71 -64.5t26 -95q0 -38 -14.5 -71.5t-42 -58t-67.5 -35.5l2 27q48 -12 82.5 -39.5t53 -66.5
t18.5 -86q0 -58 -29 -104t-81.5 -72t-122.5 -26zM275 75q42 0 73.5 16.5t50 45.5t18.5 67q0 37 -18.5 66t-50 45.5t-73.5 16.5q-41 0 -73 -16.5t-50.5 -45.5t-18.5 -66q0 -38 18.5 -67t50.5 -45.5t73 -16.5zM275 405q50 0 82 31t32 80q0 48 -32 78.5t-82 30.5
q-49 0 -81 -30.5t-32 -78.5q0 -49 32 -80t81 -31z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="564" 
d="M147 0l209 285l34 18q-12 -20 -29 -35t-41.5 -23t-59.5 -8q-60 0 -110.5 29t-80.5 80.5t-30 116.5q0 67 33 121.5t88 87t122 32.5q68 0 123 -32t87 -85.5t32 -120.5q0 -92 -64 -183l-204 -283h-109zM282 311q42 0 75.5 20.5t53 55t19.5 76.5q0 44 -19.5 78.5t-53 54.5
t-75.5 20t-75.5 -20t-53.5 -54.5t-20 -78.5q0 -43 20 -77t54 -54.5t75 -20.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="271" 
d="M135 -10q-28 0 -46.5 19.5t-18.5 45.5q0 29 18.5 47.5t46.5 18.5t46.5 -18.5t18.5 -47.5q0 -26 -18.5 -45.5t-46.5 -19.5zM135 302q-28 0 -46.5 19t-18.5 46q0 29 18.5 47.5t46.5 18.5t46.5 -18.5t18.5 -47.5q0 -27 -18.5 -46t-46.5 -19z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="269" 
d="M66 -110l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 28 19.5 48t47.5 20q26 0 46 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5l-67 -125zM135 302q-28 0 -46 19t-18 46q0 29 18 47.5t46 18.5t46.5 -18.5t18.5 -47.5q0 -27 -18.5 -46
t-46.5 -19z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="542" 
d="M483 113l-424 190v65l424 190v-91l-351 -149v34l351 -149v-90z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="542" 
d="M59 409v81h424v-81h-424zM59 179v81h424v-81h-424z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="542" 
d="M59 467v91l424 -190v-65l-424 -190v90l351 149v-34z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="505" 
d="M219 220l-1 190l27 1q35 0 61.5 15t41.5 41t15 58q0 33 -14.5 58t-41 39t-62.5 14q-48 0 -83 -24t-56 -68l-66 55q29 59 83.5 92t125.5 33q61 0 108 -24.5t73 -68t26 -100.5q0 -52 -23.5 -94.5t-66 -70t-100.5 -35.5l39 40l-8 -151h-78zM257 -10q-28 0 -46.5 19t-18.5 47
t18.5 46.5t46.5 18.5t46.5 -18.5t18.5 -46.5t-18.5 -47t-46.5 -19z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="750" 
d="M378 -90q-68 0 -127.5 24.5t-103.5 68.5t-69 103.5t-25 129.5t25.5 129.5t70.5 104t105.5 69.5t129.5 25q92 0 162.5 -42t111 -115t40.5 -168q0 -43 -8.5 -77.5t-27.5 -64.5l-77 4q14 20 22.5 40.5t12.5 45.5t4 57q0 79 -28.5 134.5t-82 85t-128.5 29.5q-76 0 -133.5 -32
t-89 -89.5t-31.5 -136.5q0 -78 30.5 -135t86.5 -88.5t128 -31.5q53 0 95 14.5t74 43.5l50 -51q-44 -38 -99 -57.5t-118 -19.5zM360 90q-39 0 -70.5 19.5t-51 52.5t-19.5 74t19.5 74.5t51 53t70.5 19.5q48 0 78.5 -23.5t30.5 -68.5v-109q0 -44 -30 -68t-79 -24zM364 153
q36 0 57.5 23.5t21.5 60.5q0 38 -21.5 61t-57.5 23q-33 0 -55.5 -23.5t-22.5 -60.5t22 -60.5t56 -23.5zM476 97v59h186v-59h-186zM448 97v74l10 70l-10 67v69h65v-280h-65z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="696" 
d="M31 0l284 694h68l283 -694h-102l-233 587h35l-235 -587h-100zM166 157v82h365v-82h-365z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="623" 
d="M145 0v82h210q60 0 94 35.5t34 85.5q0 34 -15 61.5t-43.5 43.5t-66.5 16h-213v82h199q50 0 79.5 26.5t29.5 76.5t-31 76.5t-82 26.5h-195v82h197q68 0 113.5 -24.5t69 -64.5t23.5 -88q0 -56 -30.5 -98t-89.5 -66l8 30q67 -24 104.5 -72.5t37.5 -114.5q0 -55 -27 -99
t-77.5 -70.5t-123.5 -26.5h-205zM82 0v694h94v-694h-94z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="688" 
d="M397 -10q-75 0 -139 27.5t-111.5 76.5t-73.5 114t-26 139q0 75 26 140t73.5 113.5t111 76t138.5 27.5q82 0 144 -28t110 -76l-65 -65q-33 37 -80.5 58t-108.5 21q-55 0 -101 -19.5t-80 -55.5t-52.5 -85t-18.5 -107t18.5 -107t52.5 -85t80 -55.5t101 -19.5q65 0 113 21.5
t81 58.5l65 -64q-48 -50 -112.5 -78t-145.5 -28z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="740" 
d="M144 0v86h193q77 0 135 33.5t90.5 92.5t32.5 136q0 76 -33 135t-91 92t-134 33h-192v86h194q76 0 140.5 -26t112 -73t74 -110t26.5 -138q0 -74 -26.5 -137.5t-73.5 -110.5t-111.5 -73t-139.5 -26h-197zM82 0v694h94v-694h-94z" />
    <glyph glyph-name="E" unicode="E" 
d="M82 0v694h94v-694h-94zM146 0v86h398v-86h-398zM146 314v82h365v-82h-365zM146 608v86h393v-86h-393z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="566" 
d="M82 0v694h94v-694h-94zM146 305v86h362v-86h-362zM146 608v86h379v-86h-379z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="773" 
d="M396 -10q-73 0 -136.5 27.5t-111 76.5t-74.5 114t-27 140t27 139.5t75 113.5t112.5 76t140.5 27q83 0 153 -32t117 -92l-65 -65q-33 48 -87 73.5t-118 25.5q-76 0 -134.5 -34t-91 -94t-32.5 -138q0 -79 33 -139t89 -94t128 -34q73 0 126.5 28t82.5 82.5t29 133.5l57 -41
h-300v86h340v-14q0 -121 -42.5 -202.5t-118 -122.5t-172.5 -41z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="706" 
d="M82 0v694h94v-694h-94zM530 0v694h94v-694h-94zM146 316v86h407v-86h-407z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="258" 
d="M82 0v694h94v-694h-94z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="495" 
d="M210 -10q-60 0 -105.5 23.5t-74.5 63.5l66 61q19 -28 47.5 -43t63.5 -15t62 15t42.5 44.5t15.5 72.5v482h94v-477q0 -69 -27.5 -120t-75 -79t-108.5 -28z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="666" 
d="M511 0l-350 362l343 332h122l-370 -356v52l380 -390h-125zM82 0v694h94v-694h-94z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="552" 
d="M82 0v694h94v-694h-94zM146 0v86h372v-86h-372z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="839" 
d="M82 0v694h66l294 -483h-45l294 483h66v-694h-94v529l22 -6l-232 -381h-66l-232 381l21 6v-529h-94z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="710" 
d="M82 0v694h66l28 -109v-585h-94zM562 0l-426 569l12 125l427 -568zM562 0l-28 103v591h94v-694h-66z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="794" 
d="M398 -10q-74 0 -138 27.5t-112 77t-74.5 114.5t-26.5 139q0 75 26.5 139.5t74 113.5t111 76t137.5 27t137.5 -27t111.5 -76t75 -114t27 -140q0 -74 -27 -139t-74.5 -114t-111 -76.5t-136.5 -27.5zM396 80q75 0 131.5 34.5t89.5 95t33 138.5q0 58 -19 106.5t-53 84.5
t-80 55.5t-102 19.5q-74 0 -130.5 -34t-89 -94t-32.5 -138q0 -58 18.5 -107.5t52 -85t80 -55.5t101.5 -20z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="606" 
d="M145 270v82h189q39 0 69.5 16t48 45t17.5 69t-17.5 69t-48 45t-69.5 16h-189v82h195q64 0 114.5 -26t80 -73.5t29.5 -112.5q0 -64 -29.5 -111.5t-80 -74t-114.5 -26.5h-195zM82 0v694h94v-694h-94z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="817" 
d="M399 -10q-74 0 -138 27.5t-112 77t-75 114.5t-27 139q0 75 27 139.5t74.5 113.5t111 76t137.5 27t137.5 -27t111.5 -76t74.5 -114t26.5 -140q0 -74 -26.5 -139t-74 -114t-111 -76.5t-136.5 -27.5zM731 -49l-325 325l61 61l324 -325zM397 80q74 0 131 34.5t89.5 95
t32.5 138.5q0 58 -18.5 106.5t-52.5 84.5t-80 55.5t-102 19.5q-74 0 -131 -34t-89.5 -94t-32.5 -138q0 -58 19 -107.5t52.5 -85t80 -55.5t101.5 -20z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="630" 
d="M145 302v79h188q61 0 94 31t33 84q0 50 -32.5 83t-93.5 33h-189v82h192q66 0 115 -25.5t75.5 -69.5t26.5 -101q0 -59 -26.5 -103t-75.5 -68.5t-115 -24.5h-192zM82 0v694h94v-694h-94zM482 0l-252 311l89 31l282 -342h-119z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="555" 
d="M275 -10q-82 0 -140 30t-103 87l64 64q33 -46 76 -69.5t106 -23.5q62 0 99.5 27t37.5 74q0 39 -18 63t-48.5 39.5t-66.5 28t-72 27t-66 36t-48.5 56.5t-18.5 88q0 59 28.5 100.5t78 64t111.5 22.5q68 0 122 -26.5t88 -69.5l-64 -64q-31 36 -66.5 54t-81.5 18
q-56 0 -89 -24.5t-33 -68.5q0 -35 18.5 -56.5t48.5 -36.5t66.5 -27.5t72.5 -27.5t66 -38t48.5 -59.5t18.5 -91.5q0 -92 -63.5 -144t-171.5 -52z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="625" 
d="M266 0v674h94v-674h-94zM30 608v86h565v-86h-565z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="683" 
d="M342 -10q-77 0 -137.5 34.5t-95.5 95t-35 137.5v437h94v-435q0 -55 23 -95t62.5 -62t88.5 -22q51 0 89.5 22t61 62t22.5 94v436h95v-438q0 -77 -35 -137t-95 -94.5t-138 -34.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="688" 
d="M309 0l-278 694h102l229 -587h-37l231 587h101l-280 -694h-68z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="981" 
d="M259 0l-227 694h94l180 -566h-27l177 566h68l177 -566h-26l180 566h93l-226 -694h-67l-178 565h26l-177 -565h-67z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="691" 
d="M551 0l-218 318h-17l-274 376h112l216 -299h17l274 -395h-110zM30 0l269 369l64 -56l-227 -313h-106zM393 335l-63 56l214 303h106z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="665" 
d="M302 282l-273 412h109l220 -337h-49l221 337h107l-275 -412h-60zM286 0v344h94v-344h-94z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="583" 
d="M39 65l392 565h110l-392 -565h-110zM39 0v65l80 21h415v-86h-495zM60 608v86h481v-64l-81 -22h-400z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="338" 
d="M79 -96v810h76v-810h-76zM116 -96v72h186v-72h-186zM116 643v71h186v-71h-186z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="390" 
d="M292 -40l-278 773h85l277 -773h-84z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="338" 
d="M183 -96v810h76v-810h-76zM36 -96v72h186v-72h-186zM36 643v71h186v-71h-186z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="453" 
d="M324 457l-115 207h36l-116 -207h-76l143 257h60l144 -257h-76z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="505" 
d="M20 -124v78h465v-78h-465z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="281" 
d="M200 539l-160 131l71 70l130 -161z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="582" 
d="M268 -10q-65 0 -118 32.5t-83.5 88.5t-30.5 126t30.5 126t83 89t118.5 33q54 0 96.5 -22.5t68.5 -62t29 -91.5v-143q-3 -51 -28.5 -91t-68 -62.5t-97.5 -22.5zM283 75q67 0 108 45.5t41 116.5q0 49 -18.5 85.5t-52.5 57t-79 20.5t-79.5 -21t-54 -57.5t-19.5 -83.5
q0 -48 19.5 -84.5t54.5 -57.5t80 -21zM426 0v128l17 116l-17 115v116h91v-475h-91z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="582" 
d="M316 -10q-54 0 -97.5 22.5t-69.5 62.5t-29 91v143q3 52 29.5 91.5t70 62t96.5 22.5q65 0 117.5 -33t82.5 -89t30 -126t-30 -126t-82.5 -88.5t-117.5 -32.5zM66 0v714h90v-355l-17 -115l17 -116v-128h-90zM301 75q45 0 79 21t54 57.5t20 84.5q0 47 -20 84t-54 57.5
t-78 20.5q-45 0 -79 -20.5t-53 -57.5t-19 -85q0 -47 18.5 -83.5t53 -57.5t78.5 -21z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="489" 
d="M283 -10q-70 0 -126.5 33t-89 89.5t-32.5 125.5q0 70 32.5 126t89 88.5t126.5 32.5q55 0 102 -20.5t81 -59.5l-60 -60q-22 26 -53.5 39.5t-69.5 13.5q-45 0 -80 -20.5t-55 -56.5t-20 -83t20 -83t55 -57t80 -21q38 0 69.5 13.5t54.5 39.5l59 -60q-33 -38 -80.5 -59
t-102.5 -21z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="582" 
d="M267 -10q-66 0 -118 32.5t-82.5 88.5t-30.5 126t30.5 126t82.5 89t118 33q53 0 96 -22.5t69.5 -62t29.5 -91.5v-143q-3 -51 -29 -91t-69 -62.5t-97 -22.5zM282 75q45 0 78.5 21t52.5 57.5t19 83.5q0 49 -19.5 85t-53 57t-78.5 21t-79 -21t-53.5 -57.5t-19.5 -83.5
q0 -48 19.5 -84.5t54 -57.5t79.5 -21zM517 0h-91v128l17 116l-17 115v355h91v-714z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="536" 
d="M286 -10q-71 0 -128 32.5t-90 88.5t-33 127q0 70 32.5 126t88 88.5t124.5 32.5q66 0 116.5 -30t79 -83t28.5 -120q0 -10 -1 -21.5t-4 -26.5h-401v75h353l-33 -29q0 48 -17 81.5t-48 52t-75 18.5q-46 0 -81 -20t-54 -56t-19 -85q0 -50 20 -87.5t57 -58t85 -20.5
q40 0 73.5 14t57.5 42l58 -59q-34 -40 -83.5 -61t-105.5 -21z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="399" 
d="M134 0v546q0 53 23 92.5t63.5 62.5t94.5 23q41 0 71 -14t55 -41l-59 -58q-13 14 -29 21.5t-39 7.5q-43 0 -66.5 -25t-23.5 -69v-546h-90zM17 393v82h358v-82h-358z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="579" 
d="M263 -209q-73 0 -129.5 27t-90.5 76l58 59q29 -37 69 -56.5t95 -19.5q73 0 115.5 38.5t42.5 103.5v118l16 107l-16 106v125h90v-456q0 -68 -31.5 -119t-88 -80t-130.5 -29zM263 8q-65 0 -116.5 31t-81.5 85.5t-30 122.5t30 121.5t81.5 85t116.5 31.5q56 0 99 -22
t68.5 -61.5t27.5 -92.5v-126q-3 -52 -28.5 -91.5t-68.5 -61.5t-98 -22zM281 93q44 0 77.5 19t51.5 53.5t18 80.5t-18.5 80.5t-51.5 54t-78 19.5t-79 -19.5t-53.5 -54t-19.5 -79.5t19.5 -80t54 -54.5t79.5 -19.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="548" 
d="M402 0v277q0 54 -34 89t-88 35q-36 0 -64 -16t-44 -44t-16 -64l-37 21q0 54 24 96t67 66.5t97 24.5t95.5 -24t65.5 -67t24 -99v-295h-90zM66 0v714h90v-714h-90z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="224" 
d="M67 0v475h90v-475h-90zM112 570q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="230" 
d="M6 -209q-46 0 -77.5 15t-56.5 43l58 59q14 -17 30.5 -25t39.5 -8q30 0 52 18.5t22 55.5v526h90v-525q0 -52 -22 -87t-58 -53.5t-78 -18.5zM120 570q-24 0 -40 16.5t-16 41.5q0 24 16 40.5t40 16.5q25 0 41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="498" 
d="M370 0l-222 243l220 232h109l-245 -256l4 52l250 -271h-116zM66 0v714h90v-714h-90z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="222" 
d="M66 0v714h90v-714h-90z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="855" 
d="M66 0v475h90v-475h-90zM387 0v287q0 53 -32.5 83.5t-81.5 30.5q-33 0 -59 -14t-42 -39t-16 -60l-37 18q0 54 23.5 94t64.5 62.5t92 22.5q50 0 90.5 -22t64 -62t23.5 -95v-306h-90zM708 0v287q0 53 -32.5 83.5t-80.5 30.5q-33 0 -59.5 -14t-42.5 -39t-16 -60l-51 18
q3 55 29.5 94.5t68.5 62t92 22.5q52 0 93 -22t65.5 -62t24.5 -96v-305h-91z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="548" 
d="M402 0v277q0 54 -34 89t-88 35q-36 0 -64 -16t-44 -44t-16 -64l-37 21q0 54 24 96t67 66.5t97 24.5t95.5 -27t65.5 -70.5t24 -92.5v-295h-90zM66 0v475h90v-475h-90z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="566" 
d="M283 -10q-70 0 -126 33t-89 89.5t-33 126.5q0 69 33 124.5t89 88.5t126 33q69 0 125.5 -32.5t89.5 -88.5t33 -125q0 -70 -33 -126.5t-89.5 -89.5t-125.5 -33zM283 77q45 0 80 21t55 57.5t20 83.5q0 46 -20.5 82t-55 56.5t-79.5 20.5t-80 -20.5t-55 -56.5t-20 -82
q0 -47 20 -83.5t55 -57.5t80 -21z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="582" 
d="M316 -10q-54 0 -97.5 22.5t-69.5 62.5t-29 91v143q3 52 29.5 91.5t70 62t96.5 22.5q65 0 117.5 -33t82.5 -89t30 -126t-30 -126t-82.5 -88.5t-117.5 -32.5zM66 -199v674h90v-125l-17 -115l17 -116v-318h-90zM301 75q45 0 79 21t54 57.5t20 84.5q0 47 -20 84t-54 57.5
t-78 20.5q-45 0 -79 -20.5t-53 -57.5t-19 -85q0 -47 18.5 -83.5t53 -57.5t78.5 -21z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="583" 
d="M269 -10q-66 0 -119 32.5t-83.5 88.5t-30.5 126t30.5 126t83.5 89t118 33q55 0 97.5 -22.5t68 -62t28.5 -91.5v-143q-3 -51 -28.5 -91t-68 -62.5t-96.5 -22.5zM283 75q67 0 108 45.5t41 116.5q0 49 -18.5 85.5t-52 57t-78.5 20.5t-79.5 -21t-54.5 -57.5t-20 -83.5
q0 -48 20 -84.5t54.5 -57.5t79.5 -21zM427 -199v318l16 116l-16 115v125h90v-674h-90z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="419" 
d="M66 0v475h90v-475h-90zM156 271l-34 15q0 91 42 145t121 54q36 0 65 -12.5t54 -41.5l-59 -61q-15 16 -33 23t-42 7q-50 0 -82 -32t-32 -97z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="427" 
d="M217 -10q-40 0 -75.5 10.5t-65.5 30t-52 46.5l58 58q26 -32 60 -47.5t76 -15.5t65 14.5t23 40.5t-18.5 40.5t-47.5 24t-61.5 19t-61.5 24.5t-47.5 41t-18.5 69t21 74.5t58.5 48.5t90.5 17q56 0 99.5 -19.5t71.5 -58.5l-58 -58q-20 26 -49.5 40t-66.5 14
q-39 0 -59.5 -13.5t-20.5 -37.5t18 -37t47.5 -22t61.5 -18.5t61 -25.5t47.5 -43t18.5 -71q0 -67 -47.5 -106t-127.5 -39z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="368" 
d="M139 0v674h90v-674h-90zM22 393v82h324v-82h-324z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="516" 
d="M258 -10q-59 0 -105.5 26.5t-72.5 73.5t-26 109v276h90v-272q0 -39 13.5 -67.5t39.5 -43.5t61 -15q53 0 83 33.5t30 92.5v272h90v-276q0 -62 -26 -109t-71.5 -73.5t-105.5 -26.5z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="511" 
d="M229 0l-218 475h100l175 -401h-58l176 401h96l-218 -475h-53z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="750" 
d="M202 0l-188 475h94l136 -370h-30l135 370h52l135 -370h-30l136 370h94l-187 -475h-53l-137 359h33l-138 -359h-52z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="504" 
d="M378 0l-148 208l-17 16l-187 251h109l135 -185l17 -14l200 -276h-109zM17 0l196 264l51 -66l-141 -198h-106zM294 225l-52 64l131 186h105z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="514" 
d="M222 -4l-206 479h100l153 -375h-32l161 375h100l-222 -479h-54zM86 -199l140 287l50 -92l-90 -195h-100z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="447" 
d="M28 49l282 377h106l-282 -377h-106zM28 0v49l80 33h302v-82h-382zM45 393v82h371v-49l-83 -33h-288z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="323" 
d="M237 -96q-77 0 -115 44t-30 130l12 124q4 38 -7 56t-39 18h-28v69h28q28 0 39 18.5t7 55.5l-12 121q-8 87 30 130.5t115 43.5h50v-71h-42q-45 0 -63.5 -23t-13.5 -76l10 -110q4 -39 -3 -66.5t-28.5 -45.5t-60.5 -29v32q39 -8 60.5 -26t28.5 -45.5t3 -66.5l-10 -113
q-4 -53 14 -75.5t63 -22.5h42v-72h-50z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="278" 
d="M98 -130v894h82v-894h-82z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="323" 
d="M86 -96h-50v72h42q45 0 63 22.5t14 75.5l-10 113q-3 39 3.5 66.5t28 45.5t60.5 26v-32q-39 11 -60.5 29t-28 45.5t-3.5 66.5l10 110q5 53 -13.5 76t-63.5 23h-42v71h50q77 0 115.5 -43.5t29.5 -130.5l-12 -121q-4 -37 7 -55.5t39 -18.5h28v-69h-28q-28 0 -39 -18t-7 -56
l12 -124q8 -86 -30 -130t-115 -44z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="542" 
d="M358 278q-28 0 -49 7t-40 16t-37.5 16t-42.5 7q-26 0 -46.5 -10t-37.5 -30l-51 51q25 37 57.5 55.5t75.5 18.5q29 0 50.5 -6.5t40.5 -16t37 -16.5t40 -7q25 0 45.5 10.5t37.5 29.5l51 -51q-25 -36 -57.5 -55t-73.5 -19z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="208" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="267" 
d="M85 -199l13 454h70l13 -454h-96zM133 354q-29 0 -47 19t-18 46q0 29 18 47.5t47 18.5q28 0 46 -18.5t18 -47.5q0 -27 -18 -46t-46 -19z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="521" 
d="M298 100q-70 0 -126.5 32.5t-89.5 89t-33 126.5t33 125.5t89.5 88.5t126.5 33q54 0 101.5 -21t80.5 -59l-59 -60q-23 26 -54.5 39.5t-68.5 13.5q-46 0 -81 -21t-55 -56.5t-20 -82.5t20 -83.5t55 -57.5t81 -21q37 0 69 13.5t54 40.5l60 -60q-34 -39 -81.5 -59.5
t-101.5 -20.5zM256 10v674h66v-674h-66z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="619" 
d="M161 3v480q0 70 28 119.5t77 75.5t109 26q71 0 116 -28t70 -67l-62 -62q-22 33 -50.5 52t-74.5 19q-34 0 -61 -15t-42.5 -43t-15.5 -68v-489h-94zM66 0v86h481v-86h-481zM66 297v75h384v-75h-384z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="564" 
d="M282 147q-57 0 -103.5 26.5t-73.5 72t-27 102.5q0 56 27 101t73 71.5t104 26.5q57 0 103.5 -26t73.5 -71.5t27 -101.5q0 -57 -27 -102.5t-73 -72t-104 -26.5zM101 119l-48 46l88 89l47 -47zM282 230q33 0 58 15.5t39.5 41.5t14.5 61q0 34 -14.5 60t-39.5 41t-58 15
t-58.5 -15.5t-39.5 -41.5t-14 -59q0 -34 14 -60.5t39.5 -42t58.5 -15.5zM464 119l-89 88l47 47l88 -89zM141 441l-88 87l48 48l87 -88zM422 441l-47 47l89 88l46 -48z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="645" 
d="M291 275l-234 419h109l182 -356h-49l182 356h107l-236 -419h-61zM276 0v329h94v-329h-94zM229 472l27 -74h-178v74h151zM78 223v75h489v-75h-489zM394 398l35 74h138v-74h-173z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="278" 
d="M98 393v371h82v-371h-82zM98 -130v371h82v-371h-82z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="517" 
d="M264 139l2 67q47 0 74.5 20t27.5 54q0 33 -20.5 52t-52.5 30t-68.5 22.5t-68.5 29t-52.5 48t-20.5 82.5q0 48 22 84t63.5 56t98.5 20q60 0 108 -23.5t78 -67.5l-59 -59q-23 32 -56.5 49t-74.5 17q-44 0 -68.5 -19.5t-24.5 -51.5q0 -31 20.5 -48.5t52.5 -29.5t68.5 -24
t68.5 -30t52.5 -48.5t20.5 -80.5q0 -46 -25 -79.5t-68 -51.5t-98 -18zM249 -121q-60 0 -108 24t-78 68l59 59q23 -33 56.5 -50t73.5 -17q45 0 69 20t24 51t-20.5 49t-52.5 30t-68 23.5t-68.5 29.5t-52.5 48.5t-20 81.5q0 46 24.5 79t68 51t97.5 18l-2 -67q-47 0 -74 -19.5
t-27 -54.5q0 -33 20.5 -52t52.5 -30t68 -22.5t68.5 -29t52.5 -48t20 -81.5q0 -49 -22 -84.5t-63 -56t-98 -20.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="434" 
d="M117 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM317 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="833" 
d="M417 -10q-77 0 -142 27.5t-112.5 76.5t-73.5 114t-26 140q0 76 26 140.5t73 113t111.5 75t142.5 26.5q77 0 142.5 -27t112.5 -75t73 -113t26 -141q0 -75 -26 -140t-73 -114t-111.5 -76t-142.5 -27zM425 148q-56 0 -102 26.5t-72.5 71.5t-26.5 102q0 56 26.5 101t73 71
t102.5 26q43 0 81 -16t66 -47l-53 -54q-18 20 -41.5 30t-52.5 10q-33 0 -60 -15.5t-42.5 -43t-15.5 -62.5q0 -36 15.5 -64t42.5 -43t60 -15q30 0 54 10.5t40 29.5l53 -54q-27 -31 -65.5 -47.5t-82.5 -16.5zM416 31q92 0 162 42t109.5 114t39.5 161q0 88 -39.5 159.5
t-109.5 113.5t-162 42t-162 -41.5t-109 -113t-39 -160.5t39.5 -161t109.5 -114t161 -42z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="354" 
d="M159 400q-40 0 -72 20t-50.5 53.5t-18.5 76.5t18.5 77t50.5 53.5t72 19.5q46 0 77 -26.5t35 -67.5v-112q-4 -39 -34.5 -66.5t-77.5 -27.5zM172 466q35 0 56.5 23.5t21.5 61.5q0 37 -21.5 60.5t-56.5 23.5t-57.5 -24t-22.5 -60q0 -37 22.5 -61t57.5 -24zM247 406v77l11 71
l-11 69v71h71v-288h-71z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="458" 
d="M146 82l-109 158l109 159h92l-113 -159l113 -158h-92zM331 82l-109 158l109 159h91l-113 -159l113 -158h-91z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="542" 
d="M59 344v81h424v-81h-424zM400 409h83v-232h-83v232z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="568" 
d="M284 269q-63 0 -111.5 29t-76 79t-27.5 111t27 110t75.5 77.5t111.5 28.5q64 0 112.5 -29t76.5 -78t28 -110t-28 -110.5t-76 -78.5t-112 -29zM212 376v229h35v-229h-35zM342 376l-83 103h40l86 -103h-43zM283 298q55 0 97 25t65.5 68t23.5 97q0 53 -23.5 95t-65.5 67
t-97 25q-54 0 -95.5 -25t-65 -67t-23.5 -95q0 -54 23.5 -97t65 -68t95.5 -25zM232 475v30h63q18 0 28 9.5t10 24.5t-10 25t-28 10h-63v31h64q32 0 52 -18t20 -47t-20 -47t-52 -18h-64z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="440" 
d="M40 649h360v-74h-360v74z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="394" 
d="M197 395q-45 0 -80.5 20t-55.5 55t-20 78q0 45 20 80t55.5 55.5t80.5 20.5q46 0 81 -20.5t55 -55.5t20 -80q0 -43 -20 -78t-55 -55t-81 -20zM197 466q35 0 57 24.5t22 58.5q0 36 -22 59.5t-56 23.5q-35 0 -57.5 -23.5t-22.5 -59.5q0 -34 22.5 -58.5t56.5 -24.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="542" 
d="M59 359v81h424v-81h-424zM230 565h83v-330h-83v330zM59 105v81h424v-81h-424z" />
    <glyph glyph-name="uni00B2" unicode="&#xb2;" horiz-adv-x="336" 
d="M36 387l138 139q20 20 26.5 32.5t6.5 26.5q0 21 -13.5 33.5t-35.5 12.5q-20 0 -36.5 -11.5t-33.5 -36.5l-54 48q23 36 56 52.5t74 16.5q57 0 90 -30t33 -82q0 -31 -12.5 -56t-44.5 -55l-95 -93zM36 342v45l68 22h191v-67h-259z" />
    <glyph glyph-name="uni00B3" unicode="&#xb3;" horiz-adv-x="335" 
d="M150 336q-37 0 -68 13t-52 36l52 52q10 -15 29 -23.5t39 -8.5q26 0 43 14t17 38q0 25 -18 39t-47 14q-10 0 -20.5 -1.5t-17.5 -3.5l35 44q22 6 32.5 8t18.5 2q42 0 69.5 -28.5t27.5 -75.5q0 -53 -38 -86t-102 -33zM107 505v45l88 101l87 -1l-90 -101zM54 627v67h228v-44
l-60 -23h-168z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="281" 
d="M81 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="uni00B5" unicode="&#xb5;" horiz-adv-x="579" 
d="M98 -187v662h90v-662h-90zM316 -10q-83 0 -128 52.5t-45 134.5l45 21q0 -61 32.5 -91t80.5 -30t81 29.5t33 92.5v276h90v-287q0 -57 -24 -102t-66.5 -70.5t-98.5 -25.5z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="706" 
d="M378 270h-97q-64 0 -115 26t-80.5 73.5t-29.5 112.5q0 64 29.5 111.5t80.5 74t115 26.5h376v-82h-371q-39 0 -69.5 -16t-48 -45t-17.5 -69t17.5 -69t48 -45t69.5 -16h92v-82zM411 -100h-79v794h79v-794zM570 -100h-79v794h79v-794z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="130" 
d="M65 298q-28 0 -46.5 19.5t-18.5 46.5q0 28 18.5 46.5t46.5 18.5t46.5 -18.5t18.5 -46.5q0 -27 -18.5 -46.5t-46.5 -19.5z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="271" 
d="M129 -209q-35 0 -63 9.5t-54 26.5l41 49q12 -12 29.5 -19t40.5 -7q30 0 44 9t14 23t-14 24t-42 10h-44l31 97h68l-23 -72l-33 27q61 2 97.5 -23.5t36.5 -64.5t-35 -64t-94 -25z" />
    <glyph glyph-name="uni00B9" unicode="&#xb9;" horiz-adv-x="261" 
d="M100 342v352h80v-352h-80zM31 627v67h144v-67h-144z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="344" 
d="M172 399q-43 0 -78.5 20t-55.5 54.5t-20 76.5q0 43 20.5 76.5t55 53.5t78.5 20t78.5 -19.5t55 -53.5t20.5 -77q0 -42 -20 -76.5t-55 -54.5t-79 -20zM172 466q36 0 58.5 23.5t22.5 60.5t-23 60t-58 23q-36 0 -58.5 -23.5t-22.5 -59.5q0 -37 23 -60.5t58 -23.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="458" 
d="M221 82l113 158l-113 159h91l109 -159l-109 -158h-91zM36 82l114 158l-114 159h92l109 -159l-109 -158h-92z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="676" 
d="M278 302l39 -36l-206 -280l-77 74zM372 395l-39 37l206 280l77 -75zM146 342v352h81v-352h-81zM78 627v67h144v-67h-144zM349 109l113 235h87l-115 -235h-85zM349 65v44l23 24h262v-68h-285zM519 -8v213h80v-213h-80z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="670" 
d="M278 302l39 -36l-206 -280l-77 74zM372 395l-39 37l206 280l77 -75zM146 342v352h81v-352h-81zM78 627v67h144v-67h-144zM357 37l138 139q20 20 26.5 32.5t6.5 26.5q0 21 -13.5 33.5t-36.5 12.5q-20 0 -36 -11.5t-33 -36.5l-54 48q22 36 55.5 52.5t74.5 16.5q57 0 90 -30
t33 -82q0 -31 -13 -56t-44 -55l-96 -93zM357 -8v45l67 22h191v-67h-258z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="670" 
d="M273 306l39 -33l-191 -287l-77 74zM404 392l-39 32l192 288l76 -75zM173 336q-37 0 -68 13t-53 36l52 52q10 -15 29 -23.5t39 -8.5q27 0 44 14t17 38q0 25 -18 39t-48 14q-9 0 -19.5 -1.5t-18.5 -3.5l36 44q21 6 32 8t18 2q42 0 70 -28.5t28 -75.5q0 -53 -38.5 -86
t-101.5 -33zM129 505v45l89 101l87 -1l-90 -101zM76 627v67h229v-44l-61 -23h-168zM325 109l113 235h87l-115 -235h-85zM325 65v44l23 24h261v-68h-284zM494 -8v213h80v-213h-80z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="489" 
d="M254 -209q-59 0 -105 25t-72 68.5t-26 100.5q0 54 23.5 95.5t66.5 68.5t99 36l-38 -41l8 111h78v-150h-26q-53 -1 -85.5 -32t-32.5 -82q0 -32 14.5 -57.5t40 -40t59.5 -14.5q49 0 83.5 24.5t55.5 67.5l66 -55q-29 -58 -83 -91.5t-126 -33.5zM250 354q-29 0 -47 19t-18 47
t18 46.5t47 18.5q28 0 46 -18.5t18 -46.5t-18 -47t-46 -19z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="696" 
d="M31 0l284 694h68l283 -694h-102l-233 587h35l-235 -587h-100zM166 157v82h365v-82h-365zM388 758l-160 131l71 70l130 -161z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="696" 
d="M31 0l284 694h68l283 -694h-102l-233 587h35l-235 -587h-100zM166 157v82h365v-82h-365zM288 765l-41 41l130 161l71 -71z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="696" 
d="M31 0l284 694h68l283 -694h-102l-233 587h35l-235 -587h-100zM166 157v82h365v-82h-365zM218 764l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="696" 
d="M31 0l284 694h68l283 -694h-102l-233 587h35l-235 -587h-100zM166 157v82h365v-82h-365zM420 767q-32 0 -54.5 12t-42.5 23t-45 11t-40 -9t-29 -27l-49 48q25 30 52 48t65 18q33 0 56 -11t43 -23t43 -12q25 0 40 9.5t28 27.5l49 -48q-26 -30 -52.5 -48.5t-63.5 -18.5z
" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="696" 
d="M31 0l284 694h68l283 -694h-102l-233 587h35l-235 -587h-100zM166 157v82h365v-82h-365zM248 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM448 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5
t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="696" 
d="M31 0l284 694h68l283 -694h-102l-233 587h35l-235 -587h-100zM166 157v82h365v-82h-365zM349 659q-35 0 -62.5 16t-43 42.5t-15.5 59.5q0 34 15.5 61t43 42.5t62.5 15.5q34 0 60.5 -15.5t42 -42.5t15.5 -60q0 -34 -15.5 -61t-42 -42.5t-60.5 -15.5zM349 724q21 0 36 15.5
t15 38.5t-15 38t-36 15q-24 0 -38.5 -15.5t-14.5 -37.5q0 -23 14.5 -38.5t38.5 -15.5z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="973" 
d="M31 0l284 694h255v-86h-196l-243 -608h-100zM170 245v82h361v-82h-361zM559 0v86h369v-86h-369zM495 0v694h94v-694h-94zM557 314v82h338v-82h-338zM559 608v86h363v-86h-363z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="688" 
d="M397 -10q-75 0 -139 27.5t-111.5 76.5t-73.5 114t-26 139q0 75 26 140t73.5 113.5t111 76t138.5 27.5q82 0 144 -28t110 -76l-65 -65q-33 37 -80.5 58t-108.5 21q-55 0 -101 -19.5t-80 -55.5t-52.5 -85t-18.5 -107t18.5 -107t52.5 -85t80 -55.5t101 -19.5q65 0 113 21.5
t81 58.5l65 -64q-48 -50 -112.5 -78t-145.5 -28zM361 -209q-35 0 -63 9.5t-54 26.5l41 49q12 -12 29.5 -19t40.5 -7q30 0 44 9t14 23t-14 24t-42 10h-44l31 97h68l-23 -72l-33 27q61 2 97.5 -23.5t36.5 -64.5t-35 -64t-94 -25z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" 
d="M82 0v694h94v-694h-94zM146 0v86h398v-86h-398zM146 314v82h365v-82h-365zM146 608v86h393v-86h-393zM355 758l-160 131l71 70l130 -161z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" 
d="M82 0v694h94v-694h-94zM146 0v86h398v-86h-398zM146 314v82h365v-82h-365zM146 608v86h393v-86h-393zM256 765l-41 41l130 161l71 -71z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" 
d="M82 0v694h94v-694h-94zM146 0v86h398v-86h-398zM146 314v82h365v-82h-365zM146 608v86h393v-86h-393zM185 764l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" 
d="M82 0v694h94v-694h-94zM146 0v86h398v-86h-398zM146 314v82h365v-82h-365zM146 608v86h393v-86h-393zM216 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM416 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5
t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="258" 
d="M82 0v694h94v-694h-94zM169 758l-160 131l71 70l130 -161z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="258" 
d="M82 0v694h94v-694h-94zM70 765l-41 41l130 161l71 -71z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="258" 
d="M82 0v694h94v-694h-94zM-1 764l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="258" 
d="M82 0v694h94v-694h-94zM30 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM230 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="755" 
d="M22 391h372v-79h-372v79zM159 0v86h193q77 0 135 33.5t90.5 92.5t32.5 136q0 76 -33 135t-91 92t-134 33h-192v86h194q76 0 140.5 -26t112 -73t74 -110t26.5 -138q0 -74 -26.5 -137.5t-73.5 -110.5t-111.5 -73t-139.5 -26h-197zM97 0v694h94v-694h-94z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="710" 
d="M82 0v694h66l28 -109v-585h-94zM562 0l-426 569l12 125l427 -568zM562 0l-28 103v591h94v-694h-66zM428 767q-32 0 -54.5 12t-42.5 23t-45 11t-40 -9t-29 -27l-49 48q25 30 52 48t65 18q33 0 56 -11t43 -23t43 -12q25 0 40 9.5t28 27.5l49 -48q-26 -30 -52.5 -48.5
t-63.5 -18.5z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="794" 
d="M398 -10q-74 0 -138 27.5t-112 77t-74.5 114.5t-26.5 139q0 75 26.5 139.5t74 113.5t111 76t137.5 27t137.5 -27t111.5 -76t75 -114t27 -140q0 -74 -27 -139t-74.5 -114t-111 -76.5t-136.5 -27.5zM396 80q75 0 131.5 34.5t89.5 95t33 138.5q0 58 -19 106.5t-53 84.5
t-80 55.5t-102 19.5q-74 0 -130.5 -34t-89 -94t-32.5 -138q0 -58 18.5 -107.5t52 -85t80 -55.5t101.5 -20zM437 758l-160 131l71 70l130 -161z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="794" 
d="M398 -10q-74 0 -138 27.5t-112 77t-74.5 114.5t-26.5 139q0 75 26.5 139.5t74 113.5t111 76t137.5 27t137.5 -27t111.5 -76t75 -114t27 -140q0 -74 -27 -139t-74.5 -114t-111 -76.5t-136.5 -27.5zM396 80q75 0 131.5 34.5t89.5 95t33 138.5q0 58 -19 106.5t-53 84.5
t-80 55.5t-102 19.5q-74 0 -130.5 -34t-89 -94t-32.5 -138q0 -58 18.5 -107.5t52 -85t80 -55.5t101.5 -20zM337 765l-41 41l130 161l71 -71z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="794" 
d="M398 -10q-74 0 -138 27.5t-112 77t-74.5 114.5t-26.5 139q0 75 26.5 139.5t74 113.5t111 76t137.5 27t137.5 -27t111.5 -76t75 -114t27 -140q0 -74 -27 -139t-74.5 -114t-111 -76.5t-136.5 -27.5zM396 80q75 0 131.5 34.5t89.5 95t33 138.5q0 58 -19 106.5t-53 84.5
t-80 55.5t-102 19.5q-74 0 -130.5 -34t-89 -94t-32.5 -138q0 -58 18.5 -107.5t52 -85t80 -55.5t101.5 -20zM267 764l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="794" 
d="M398 -10q-74 0 -138 27.5t-112 77t-74.5 114.5t-26.5 139q0 75 26.5 139.5t74 113.5t111 76t137.5 27t137.5 -27t111.5 -76t75 -114t27 -140q0 -74 -27 -139t-74.5 -114t-111 -76.5t-136.5 -27.5zM396 80q75 0 131.5 34.5t89.5 95t33 138.5q0 58 -19 106.5t-53 84.5
t-80 55.5t-102 19.5q-74 0 -130.5 -34t-89 -94t-32.5 -138q0 -58 18.5 -107.5t52 -85t80 -55.5t101.5 -20zM469 767q-32 0 -54.5 12t-42.5 23t-45 11t-40 -9t-29 -27l-49 48q25 30 52 48t65 18q33 0 56 -11t43 -23t43 -12q25 0 40 9.5t28 27.5l49 -48q-26 -30 -52.5 -48.5
t-63.5 -18.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="794" 
d="M398 -10q-74 0 -138 27.5t-112 77t-74.5 114.5t-26.5 139q0 75 26.5 139.5t74 113.5t111 76t137.5 27t137.5 -27t111.5 -76t75 -114t27 -140q0 -74 -27 -139t-74.5 -114t-111 -76.5t-136.5 -27.5zM396 80q75 0 131.5 34.5t89.5 95t33 138.5q0 58 -19 106.5t-53 84.5
t-80 55.5t-102 19.5q-74 0 -130.5 -34t-89 -94t-32.5 -138q0 -58 18.5 -107.5t52 -85t80 -55.5t101.5 -20zM297 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM497 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5
t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="542" 
d="M123 127l-59 59l356 356l59 -59zM412 134l-342 342l59 59l342 -342z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="794" 
d="M682 694l62 -62l-632 -632l-62 62zM398 -10q-74 0 -138 27.5t-112 77t-75 114.5t-27 139q0 75 27 139.5t74.5 113.5t111 76t137.5 27t137.5 -27t111.5 -76t74.5 -114t26.5 -140q0 -74 -26.5 -139t-74 -114t-111 -76.5t-136.5 -27.5zM396 80q74 0 131 34.5t89.5 95
t32.5 138.5q0 58 -18.5 106.5t-52.5 84.5t-80 55.5t-102 19.5q-74 0 -131 -34t-89.5 -94t-32.5 -138q0 -58 19 -107.5t52.5 -85t80 -55.5t101.5 -20z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="683" 
d="M342 -10q-77 0 -137.5 34.5t-95.5 95t-35 137.5v437h94v-435q0 -55 23 -95t62.5 -62t88.5 -22q51 0 89.5 22t61 62t22.5 94v436h95v-438q0 -77 -35 -137t-95 -94.5t-138 -34.5zM381 758l-160 131l71 70l130 -161z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="683" 
d="M342 -10q-77 0 -137.5 34.5t-95.5 95t-35 137.5v437h94v-435q0 -55 23 -95t62.5 -62t88.5 -22q51 0 89.5 22t61 62t22.5 94v436h95v-438q0 -77 -35 -137t-95 -94.5t-138 -34.5zM282 765l-41 41l130 161l71 -71z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="683" 
d="M342 -10q-77 0 -137.5 34.5t-95.5 95t-35 137.5v437h94v-435q0 -55 23 -95t62.5 -62t88.5 -22q51 0 89.5 22t61 62t22.5 94v436h95v-438q0 -77 -35 -137t-95 -94.5t-138 -34.5zM211 764l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="683" 
d="M342 -10q-77 0 -137.5 34.5t-95.5 95t-35 137.5v437h94v-435q0 -55 23 -95t62.5 -62t88.5 -22q51 0 89.5 22t61 62t22.5 94v436h95v-438q0 -77 -35 -137t-95 -94.5t-138 -34.5zM242 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5
t-41 -16.5zM442 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="665" 
d="M302 282l-273 412h109l220 -337h-49l221 337h107l-275 -412h-60zM286 0v344h94v-344h-94zM273 765l-41 41l130 161l71 -71z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="606" 
d="M145 143v83h189q39 0 69.5 15.5t48 44.5t17.5 69t-17.5 69.5t-48 45t-69.5 15.5h-189v82h195q64 0 114.5 -26t80 -73.5t29.5 -112.5q0 -64 -29.5 -111.5t-80 -74t-114.5 -26.5h-195zM82 0v694h94v-694h-94z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="573" 
d="M323 -10q-46 0 -78 12t-57 36l54 68q14 -14 34.5 -22.5t49.5 -8.5q34 0 60 15.5t40.5 44t14.5 65.5t-17 66t-46.5 46.5t-67.5 17.5h-60v79h35q32 0 56 15t37 42t13 61q0 32 -14 57t-39.5 40t-60.5 15q-53 0 -87 -31.5t-34 -83.5v-524h-90v524q0 56 27 101.5t74 72
t110 26.5q62 0 107.5 -26t70.5 -69t25 -96q0 -64 -35.5 -108.5t-95.5 -59.5l7 35q55 -5 96.5 -31.5t64.5 -70t23 -100.5q0 -45 -16 -83t-45 -66t-68.5 -43.5t-87.5 -15.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="582" 
d="M268 -10q-65 0 -118 32.5t-83.5 88.5t-30.5 126t30.5 126t83 89t118.5 33q54 0 96.5 -22.5t68.5 -62t29 -91.5v-143q-3 -51 -28.5 -91t-68 -62.5t-97.5 -22.5zM283 75q67 0 108 45.5t41 116.5q0 49 -18.5 85.5t-52.5 57t-79 20.5t-79.5 -21t-54 -57.5t-19.5 -83.5
q0 -48 19.5 -84.5t54.5 -57.5t80 -21zM426 0v128l17 116l-17 115v116h91v-475h-91zM333 539l-160 131l71 70l130 -161z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="582" 
d="M268 -10q-65 0 -118 32.5t-83.5 88.5t-30.5 126t30.5 126t83 89t118.5 33q54 0 96.5 -22.5t68.5 -62t29 -91.5v-143q-3 -51 -28.5 -91t-68 -62.5t-97.5 -22.5zM283 75q67 0 108 45.5t41 116.5q0 49 -18.5 85.5t-52.5 57t-79 20.5t-79.5 -21t-54 -57.5t-19.5 -83.5
q0 -48 19.5 -84.5t54.5 -57.5t80 -21zM426 0v128l17 116l-17 115v116h91v-475h-91zM234 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="582" 
d="M268 -10q-65 0 -118 32.5t-83.5 88.5t-30.5 126t30.5 126t83 89t118.5 33q54 0 96.5 -22.5t68.5 -62t29 -91.5v-143q-3 -51 -28.5 -91t-68 -62.5t-97.5 -22.5zM283 75q67 0 108 45.5t41 116.5q0 49 -18.5 85.5t-52.5 57t-79 20.5t-79.5 -21t-54 -57.5t-19.5 -83.5
q0 -48 19.5 -84.5t54.5 -57.5t80 -21zM426 0v128l17 116l-17 115v116h91v-475h-91zM163 545l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="582" 
d="M268 -10q-65 0 -118 32.5t-83.5 88.5t-30.5 126t30.5 126t83 89t118.5 33q54 0 96.5 -22.5t68.5 -62t29 -91.5v-143q-3 -51 -28.5 -91t-68 -62.5t-97.5 -22.5zM283 75q67 0 108 45.5t41 116.5q0 49 -18.5 85.5t-52.5 57t-79 20.5t-79.5 -21t-54 -57.5t-19.5 -83.5
q0 -48 19.5 -84.5t54.5 -57.5t80 -21zM426 0v128l17 116l-17 115v116h91v-475h-91zM366 548q-32 0 -54.5 12t-42.5 23t-45 11t-40 -9t-29 -27l-49 48q25 30 52 48t65 18q33 0 56 -11t43 -23t43 -12q25 0 40 9.5t28 27.5l49 -48q-26 -30 -52.5 -48.5t-63.5 -18.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="582" 
d="M268 -10q-65 0 -118 32.5t-83.5 88.5t-30.5 126t30.5 126t83 89t118.5 33q54 0 96.5 -22.5t68.5 -62t29 -91.5v-143q-3 -51 -28.5 -91t-68 -62.5t-97.5 -22.5zM283 75q67 0 108 45.5t41 116.5q0 49 -18.5 85.5t-52.5 57t-79 20.5t-79.5 -21t-54 -57.5t-19.5 -83.5
q0 -48 19.5 -84.5t54.5 -57.5t80 -21zM426 0v128l17 116l-17 115v116h91v-475h-91zM194 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM394 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5
q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="582" 
d="M268 -10q-65 0 -118 32.5t-83.5 88.5t-30.5 126t30.5 126t83 89t118.5 33q54 0 96.5 -22.5t68.5 -62t29 -91.5v-143q-3 -51 -28.5 -91t-68 -62.5t-97.5 -22.5zM283 75q67 0 108 45.5t41 116.5q0 49 -18.5 85.5t-52.5 57t-79 20.5t-79.5 -21t-54 -57.5t-19.5 -83.5
q0 -48 19.5 -84.5t54.5 -57.5t80 -21zM426 0v128l17 116l-17 115v116h91v-475h-91zM295 532q-35 0 -62.5 16t-43 42.5t-15.5 59.5q0 34 15.5 61t43 42.5t62.5 15.5q34 0 60.5 -15.5t42 -42.5t15.5 -60q0 -34 -15.5 -61t-42 -42.5t-60.5 -15.5zM295 597q21 0 36 15.5t15 38.5
t-15 38t-36 15q-24 0 -38.5 -15.5t-14.5 -37.5q0 -23 14.5 -38.5t38.5 -15.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="933" 
d="M683 -10q-71 0 -128 32.5t-90 88.5t-33 127q0 70 32.5 126t88 88.5t124.5 32.5q66 0 116.5 -30t79 -83t28.5 -120q0 -10 -1 -21.5t-4 -26.5h-401v75h353l-33 -29q0 48 -17 81.5t-48 52t-75 18.5q-46 0 -81 -20t-54 -56t-19 -85q0 -50 20 -87.5t57 -58t85 -20.5
q40 0 73.5 14t57.5 42l58 -59q-34 -40 -83.5 -61t-105.5 -21zM268 -10q-65 0 -118 32.5t-83.5 88.5t-30.5 126t30.5 126t83 89t118.5 33q54 0 96.5 -22.5t68.5 -62t29 -91.5v-143q-3 -51 -28.5 -91t-68 -62.5t-97.5 -22.5zM283 75q67 0 108 45.5t41 116.5q0 49 -18.5 85.5
t-52.5 57t-79 20.5t-79.5 -21t-54 -57.5t-19.5 -83.5q0 -48 19.5 -84.5t54.5 -57.5t80 -21zM426 0v128l17 116l-17 115v116h91v-475h-91z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="489" 
d="M283 -10q-70 0 -126.5 33t-89 89.5t-32.5 125.5q0 70 32.5 126t89 88.5t126.5 32.5q55 0 102 -20.5t81 -59.5l-60 -60q-22 26 -53.5 39.5t-69.5 13.5q-45 0 -80 -20.5t-55 -56.5t-20 -83t20 -83t55 -57t80 -21q38 0 69.5 13.5t54.5 39.5l59 -60q-33 -38 -80.5 -59
t-102.5 -21zM251 -209q-35 0 -63 9.5t-54 26.5l41 49q12 -12 29.5 -19t40.5 -7q30 0 44 9t14 23t-14 24t-42 10h-44l31 97h68l-23 -72l-33 27q61 2 97.5 -23.5t36.5 -64.5t-35 -64t-94 -25z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="536" 
d="M286 -10q-71 0 -128 32.5t-90 88.5t-33 127q0 70 32.5 126t88 88.5t124.5 32.5q66 0 116.5 -30t79 -83t28.5 -120q0 -10 -1 -21.5t-4 -26.5h-401v75h353l-33 -29q0 48 -17 81.5t-48 52t-75 18.5q-46 0 -81 -20t-54 -56t-19 -85q0 -50 20 -87.5t57 -58t85 -20.5
q40 0 73.5 14t57.5 42l58 -59q-34 -40 -83.5 -61t-105.5 -21zM319 539l-160 131l71 70l130 -161z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="536" 
d="M286 -10q-71 0 -128 32.5t-90 88.5t-33 127q0 70 32.5 126t88 88.5t124.5 32.5q66 0 116.5 -30t79 -83t28.5 -120q0 -10 -1 -21.5t-4 -26.5h-401v75h353l-33 -29q0 48 -17 81.5t-48 52t-75 18.5q-46 0 -81 -20t-54 -56t-19 -85q0 -50 20 -87.5t57 -58t85 -20.5
q40 0 73.5 14t57.5 42l58 -59q-34 -40 -83.5 -61t-105.5 -21zM219 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="536" 
d="M286 -10q-71 0 -128 32.5t-90 88.5t-33 127q0 70 32.5 126t88 88.5t124.5 32.5q66 0 116.5 -30t79 -83t28.5 -120q0 -10 -1 -21.5t-4 -26.5h-401v75h353l-33 -29q0 48 -17 81.5t-48 52t-75 18.5q-46 0 -81 -20t-54 -56t-19 -85q0 -50 20 -87.5t57 -58t85 -20.5
q40 0 73.5 14t57.5 42l58 -59q-34 -40 -83.5 -61t-105.5 -21zM149 545l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="536" 
d="M286 -10q-71 0 -128 32.5t-90 88.5t-33 127q0 70 32.5 126t88 88.5t124.5 32.5q66 0 116.5 -30t79 -83t28.5 -120q0 -10 -1 -21.5t-4 -26.5h-401v75h353l-33 -29q0 48 -17 81.5t-48 52t-75 18.5q-46 0 -81 -20t-54 -56t-19 -85q0 -50 20 -87.5t57 -58t85 -20.5
q40 0 73.5 14t57.5 42l58 -59q-34 -40 -83.5 -61t-105.5 -21zM179 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM379 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z
" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="222" 
d="M66 0v475h90v-475h-90zM151 539l-160 131l71 70l130 -161z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="222" 
d="M66 0v475h90v-475h-90zM51 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="222" 
d="M66 0v475h90v-475h-90zM-19 545l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="222" 
d="M66 0v475h90v-475h-90zM11 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM211 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="564" 
d="M283 -10q-68 0 -122.5 32.5t-87.5 87.5t-33 121q0 67 30 117.5t80 79.5t111 29q35 0 59.5 -8t41 -22.5t28.5 -35.5l-34 18l-209 285h110l204 -283q64 -91 64 -183q0 -66 -32 -120t-87 -86t-123 -32zM283 78q42 0 75.5 20t52.5 55t19 78t-19 77.5t-52.5 54.5t-75.5 20
q-41 0 -75 -20t-54 -54.5t-20 -77.5t20 -77.5t53.5 -55t75.5 -20.5zM114 496l-26 65l333 123l25 -65z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="548" 
d="M402 0v277q0 54 -34 89t-88 35q-36 0 -64 -16t-44 -44t-16 -64l-37 21q0 54 24 96t67 66.5t97 24.5t95.5 -27t65.5 -70.5t24 -92.5v-295h-90zM66 0v475h90v-475h-90zM347 548q-32 0 -54.5 12t-42.5 23t-45 11t-40 -9t-29 -27l-49 48q25 30 52 48t65 18q33 0 56 -11
t43 -23t43 -12q25 0 40 9.5t28 27.5l49 -48q-26 -30 -52.5 -48.5t-63.5 -18.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="566" 
d="M283 -10q-70 0 -126 33t-89 89.5t-33 126.5q0 69 33 124.5t89 88.5t126 33q69 0 125.5 -32.5t89.5 -88.5t33 -125q0 -70 -33 -126.5t-89.5 -89.5t-125.5 -33zM283 77q45 0 80 21t55 57.5t20 83.5q0 46 -20.5 82t-55 56.5t-79.5 20.5t-80 -20.5t-55 -56.5t-20 -82
q0 -47 20 -83.5t55 -57.5t80 -21zM323 539l-160 131l71 70l130 -161z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="566" 
d="M283 -10q-70 0 -126 33t-89 89.5t-33 126.5q0 69 33 124.5t89 88.5t126 33q69 0 125.5 -32.5t89.5 -88.5t33 -125q0 -70 -33 -126.5t-89.5 -89.5t-125.5 -33zM283 77q45 0 80 21t55 57.5t20 83.5q0 46 -20.5 82t-55 56.5t-79.5 20.5t-80 -20.5t-55 -56.5t-20 -82
q0 -47 20 -83.5t55 -57.5t80 -21zM224 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="566" 
d="M283 -10q-70 0 -126 33t-89 89.5t-33 126.5q0 69 33 124.5t89 88.5t126 33q69 0 125.5 -32.5t89.5 -88.5t33 -125q0 -70 -33 -126.5t-89.5 -89.5t-125.5 -33zM283 77q45 0 80 21t55 57.5t20 83.5q0 46 -20.5 82t-55 56.5t-79.5 20.5t-80 -20.5t-55 -56.5t-20 -82
q0 -47 20 -83.5t55 -57.5t80 -21zM153 545l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="566" 
d="M283 -10q-70 0 -126 33t-89 89.5t-33 126.5q0 69 33 124.5t89 88.5t126 33q69 0 125.5 -32.5t89.5 -88.5t33 -125q0 -70 -33 -126.5t-89.5 -89.5t-125.5 -33zM283 77q45 0 80 21t55 57.5t20 83.5q0 46 -20.5 82t-55 56.5t-79.5 20.5t-80 -20.5t-55 -56.5t-20 -82
q0 -47 20 -83.5t55 -57.5t80 -21zM356 548q-32 0 -54.5 12t-42.5 23t-45 11t-40 -9t-29 -27l-49 48q25 30 52 48t65 18q33 0 56 -11t43 -23t43 -12q25 0 40 9.5t28 27.5l49 -48q-26 -30 -52.5 -48.5t-63.5 -18.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="566" 
d="M283 -10q-70 0 -126 33t-89 89.5t-33 126.5q0 69 33 124.5t89 88.5t126 33q69 0 125.5 -32.5t89.5 -88.5t33 -125q0 -70 -33 -126.5t-89.5 -89.5t-125.5 -33zM283 77q45 0 80 21t55 57.5t20 83.5q0 46 -20.5 82t-55 56.5t-79.5 20.5t-80 -20.5t-55 -56.5t-20 -82
q0 -47 20 -83.5t55 -57.5t80 -21zM184 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM384 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="542" 
d="M59 294v81h424v-81h-424zM271 441q-24 0 -40 17t-16 41q0 25 16 41.5t40 16.5q25 0 41 -16.5t16 -41.5q0 -24 -16 -41t-41 -17zM271 113q-24 0 -40 16.5t-16 41.5q0 24 16 40.5t40 16.5q25 0 41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="566" 
d="M80 -25l-59 59l466 466l59 -59zM283 -10q-70 0 -126 33t-89 89.5t-33 126.5q0 69 33 124.5t89 88.5t126 33q69 0 125.5 -32.5t89.5 -88.5t33 -125q0 -70 -33 -126.5t-89.5 -89.5t-125.5 -33zM283 77q45 0 80 21t55 57.5t20 83.5q0 46 -20.5 82t-55 56.5t-79.5 20.5
t-80 -20.5t-55 -56.5t-20 -82q0 -47 20 -83.5t55 -57.5t80 -21z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="516" 
d="M258 -10q-59 0 -105.5 26.5t-72.5 73.5t-26 109v276h90v-272q0 -39 13.5 -67.5t39.5 -43.5t61 -15q53 0 83 33.5t30 92.5v272h90v-276q0 -62 -26 -109t-71.5 -73.5t-105.5 -26.5zM298 539l-160 131l71 70l130 -161z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="516" 
d="M258 -10q-59 0 -105.5 26.5t-72.5 73.5t-26 109v276h90v-272q0 -39 13.5 -67.5t39.5 -43.5t61 -15q53 0 83 33.5t30 92.5v272h90v-276q0 -62 -26 -109t-71.5 -73.5t-105.5 -26.5zM199 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="516" 
d="M258 -10q-59 0 -105.5 26.5t-72.5 73.5t-26 109v276h90v-272q0 -39 13.5 -67.5t39.5 -43.5t61 -15q53 0 83 33.5t30 92.5v272h90v-276q0 -62 -26 -109t-71.5 -73.5t-105.5 -26.5zM128 545l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="516" 
d="M258 -10q-59 0 -105.5 26.5t-72.5 73.5t-26 109v276h90v-272q0 -39 13.5 -67.5t39.5 -43.5t61 -15q53 0 83 33.5t30 92.5v272h90v-276q0 -62 -26 -109t-71.5 -73.5t-105.5 -26.5zM159 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5
q0 -25 -16 -41.5t-41 -16.5zM359 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="514" 
d="M222 -4l-206 479h100l153 -375h-32l161 375h100l-222 -479h-54zM86 -199l140 287l50 -92l-90 -195h-100zM197 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="582" 
d="M316 -10q-54 0 -97.5 22.5t-69.5 62.5t-29 91v143q3 52 29.5 91.5t70 62t96.5 22.5q65 0 117.5 -33t82.5 -89t30 -126t-30 -126t-82.5 -88.5t-117.5 -32.5zM66 -199v913h90v-364l-17 -115l17 -116v-318h-90zM301 75q45 0 79.5 21t54 57.5t19.5 84.5q0 47 -19.5 84
t-54 57.5t-78.5 20.5q-45 0 -79 -20.5t-53 -57.5t-19 -85q0 -47 18.5 -83.5t53 -57.5t78.5 -21z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="514" 
d="M222 -4l-206 479h100l153 -375h-32l161 375h100l-222 -479h-54zM86 -199l140 287l50 -92l-90 -195h-100zM157 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM357 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5
t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="696" 
d="M31 0l284 694h68l283 -694h-102l-233 587h35l-235 -587h-100zM166 157v82h365v-82h-365zM168 868h360v-74h-360v74z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="582" 
d="M268 -10q-65 0 -118 32.5t-83.5 88.5t-30.5 126t30.5 126t83 89t118.5 33q54 0 96.5 -22.5t68.5 -62t29 -91.5v-143q-3 -51 -28.5 -91t-68 -62.5t-97.5 -22.5zM283 75q67 0 108 45.5t41 116.5q0 49 -18.5 85.5t-52.5 57t-79 20.5t-79.5 -21t-54 -57.5t-19.5 -83.5
q0 -48 19.5 -84.5t54.5 -57.5t80 -21zM426 0v128l17 116l-17 115v116h91v-475h-91zM114 649h360v-74h-360v74z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="696" 
d="M31 0l284 694h68l283 -694h-102l-233 587h35l-235 -587h-100zM166 157v82h365v-82h-365zM519 921q0 -48 -22 -86.5t-60 -61t-87 -22.5q-50 0 -89 22.5t-61.5 61t-22.5 86.5h82q0 -40 25 -66t66 -26q25 0 45 12t31.5 33t11.5 47h81z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="582" 
d="M268 -10q-65 0 -118 32.5t-83.5 88.5t-30.5 126t30.5 126t83 89t118.5 33q54 0 96.5 -22.5t68.5 -62t29 -91.5v-143q-3 -51 -28.5 -91t-68 -62.5t-97.5 -22.5zM283 75q67 0 108 45.5t41 116.5q0 49 -18.5 85.5t-52.5 57t-79 20.5t-79.5 -21t-54 -57.5t-19.5 -83.5
q0 -48 19.5 -84.5t54.5 -57.5t80 -21zM426 0v128l17 116l-17 115v116h91v-475h-91zM464 702q0 -48 -22 -86.5t-60 -61t-87 -22.5q-50 0 -89 22.5t-61.5 61t-22.5 86.5h82q0 -40 25 -66t66 -26q25 0 45 12t31.5 33t11.5 47h81z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="696" 
d="M31 0l284 694h68l283 -694h-102l-233 587h35l-235 -587h-100zM166 157v82h365v-82h-365zM621 -209q-41 0 -72.5 20.5t-31.5 68.5q0 40 25.5 76t74.5 63l49 -19q-36 -23 -57.5 -49.5t-21.5 -57.5q0 -20 11.5 -31.5t32.5 -11.5q15 0 26.5 5t20.5 10l24 -50
q-14 -9 -33.5 -16.5t-47.5 -7.5z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="582" 
d="M268 -10q-65 0 -118 32.5t-83.5 88.5t-30.5 126t30.5 126t83 89t118.5 33q54 0 96.5 -22.5t68.5 -62t29 -91.5v-143q-3 -51 -28.5 -91t-68 -62.5t-97.5 -22.5zM283 75q67 0 108 45.5t41 116.5q0 49 -18.5 85.5t-52.5 57t-79 20.5t-79.5 -21t-54 -57.5t-19.5 -83.5
q0 -48 19.5 -84.5t54.5 -57.5t80 -21zM426 0v128l17 116l-17 115v116h91v-475h-91zM472 -209q-41 0 -72.5 20.5t-31.5 68.5q0 40 25.5 76t74.5 63l49 -19q-36 -23 -57.5 -49.5t-21.5 -57.5q0 -20 11.5 -31.5t32.5 -11.5q15 0 26.5 5t20.5 10l24 -50q-14 -9 -33.5 -16.5
t-47.5 -7.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="688" 
d="M397 -10q-75 0 -139 27.5t-111.5 76.5t-73.5 114t-26 139q0 75 26 140t73.5 113.5t111 76t138.5 27.5q82 0 144 -28t110 -76l-65 -65q-33 37 -80.5 58t-108.5 21q-55 0 -101 -19.5t-80 -55.5t-52.5 -85t-18.5 -107t18.5 -107t52.5 -85t80 -55.5t101 -19.5q65 0 113 21.5
t81 58.5l65 -64q-48 -50 -112.5 -78t-145.5 -28zM318 765l-41 41l130 161l71 -71z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="489" 
d="M283 -10q-70 0 -126.5 33t-89 89.5t-32.5 125.5q0 70 32.5 126t89 88.5t126.5 32.5q55 0 102 -20.5t81 -59.5l-60 -60q-22 26 -53.5 39.5t-69.5 13.5q-45 0 -80 -20.5t-55 -56.5t-20 -83t20 -83t55 -57t80 -21q38 0 69.5 13.5t54.5 39.5l59 -60q-33 -38 -80.5 -59
t-102.5 -21zM218 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="688" 
d="M397 -10q-75 0 -139 27.5t-111.5 76.5t-73.5 114t-26 139q0 75 26 140t73.5 113.5t111 76t138.5 27.5q82 0 144 -28t110 -76l-65 -65q-33 37 -80.5 58t-108.5 21q-55 0 -101 -19.5t-80 -55.5t-52.5 -85t-18.5 -107t18.5 -107t52.5 -85t80 -55.5t101 -19.5q65 0 113 21.5
t81 58.5l65 -64q-48 -50 -112.5 -78t-145.5 -28zM378 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="489" 
d="M283 -10q-70 0 -126.5 33t-89 89.5t-32.5 125.5q0 70 32.5 126t89 88.5t126.5 32.5q55 0 102 -20.5t81 -59.5l-60 -60q-22 26 -53.5 39.5t-69.5 13.5q-45 0 -80 -20.5t-55 -56.5t-20 -83t20 -83t55 -57t80 -21q38 0 69.5 13.5t54.5 39.5l59 -60q-33 -38 -80.5 -59
t-102.5 -21zM278 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="688" 
d="M397 -10q-75 0 -139 27.5t-111.5 76.5t-73.5 114t-26 139q0 75 26 140t73.5 113.5t111 76t138.5 27.5q82 0 144 -28t110 -76l-65 -65q-33 37 -80.5 58t-108.5 21q-55 0 -101 -19.5t-80 -55.5t-52.5 -85t-18.5 -107t18.5 -107t52.5 -85t80 -55.5t101 -19.5q65 0 113 21.5
t81 58.5l65 -64q-48 -50 -112.5 -78t-145.5 -28zM508 959l40 -41l-136 -154h-70l-136 154l41 41l160 -134l-60 1z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="489" 
d="M283 -10q-70 0 -126.5 33t-89 89.5t-32.5 125.5q0 70 32.5 126t89 88.5t126.5 32.5q55 0 102 -20.5t81 -59.5l-60 -60q-22 26 -53.5 39.5t-69.5 13.5q-45 0 -80 -20.5t-55 -56.5t-20 -83t20 -83t55 -57t80 -21q38 0 69.5 13.5t54.5 39.5l59 -60q-33 -38 -80.5 -59
t-102.5 -21zM408 740l40 -41l-136 -154h-70l-136 154l41 41l160 -134l-60 1z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="740" 
d="M144 0v86h193q77 0 135 33.5t90.5 92.5t32.5 136q0 76 -33 135t-91 92t-134 33h-192v86h194q76 0 140.5 -26t112 -73t74 -110t26.5 -138q0 -74 -26.5 -137.5t-73.5 -110.5t-111.5 -73t-139.5 -26h-197zM82 0v694h94v-694h-94zM469 959l41 -41l-136 -154h-70l-136 154
l41 41l160 -134l-61 1z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="766" 
d="M267 -10q-66 0 -118 32.5t-82.5 88.5t-30.5 126t30.5 126t82.5 89t118 33q53 0 96 -22.5t69.5 -62t29.5 -91.5v-143q-3 -51 -29 -91t-69 -62.5t-97 -22.5zM282 75q45 0 78.5 21t52.5 57.5t19 83.5q0 49 -19.5 85t-53 57t-78.5 21t-79 -21t-53.5 -57.5t-19.5 -83.5
q0 -48 19.5 -84.5t54 -57.5t79.5 -21zM517 0h-91v128l17 116l-17 115v355h91v-714zM614 435l-52 28l63 119l31 8q-5 -11 -12.5 -18t-18.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 28 19.5 48t47.5 20q26 0 46 -20t20 -48q0 -11 -4.5 -26.5t-16.5 -40.5z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="754" 
d="M0 309v77h417v-77h-417zM158 0v86h193q77 0 135 33.5t91 92.5t33 136q0 76 -33 135t-91.5 92t-134.5 33h-192v86h195q75 0 139.5 -26t112.5 -73t74.5 -110t26.5 -138q0 -74 -26.5 -137.5t-74 -110.5t-112 -73t-138.5 -26h-198zM96 0v694h94v-694h-94z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="597" 
d="M268 554v85h329v-85h-329zM267 -10q-66 0 -118 32.5t-82.5 88.5t-30.5 126t30.5 126t82.5 89t118 33q53 0 96 -22.5t69.5 -62t29.5 -91.5v-143q-3 -51 -29 -91t-69 -62.5t-97 -22.5zM282 75q45 0 78.5 21t52.5 57.5t19 83.5q0 49 -19.5 85t-53 57t-78.5 21t-79 -21
t-53.5 -57.5t-19.5 -83.5q0 -48 19.5 -84.5t54 -57.5t79.5 -21zM517 0h-91v128l17 116l-17 115v355h91v-714z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" 
d="M82 0v694h94v-694h-94zM146 0v86h398v-86h-398zM146 314v82h365v-82h-365zM146 608v86h393v-86h-393zM136 868h360v-74h-360v74z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="536" 
d="M286 -10q-71 0 -128 32.5t-90 88.5t-33 127q0 70 32.5 126t88 88.5t124.5 32.5q66 0 116.5 -30t79 -83t28.5 -120q0 -10 -1 -21.5t-4 -26.5h-401v75h353l-33 -29q0 48 -17 81.5t-48 52t-75 18.5q-46 0 -81 -20t-54 -56t-19 -85q0 -50 20 -87.5t57 -58t85 -20.5
q40 0 73.5 14t57.5 42l58 -59q-34 -40 -83.5 -61t-105.5 -21zM99 649h360v-74h-360v74z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" 
d="M82 0v694h94v-694h-94zM146 0v86h398v-86h-398zM146 314v82h365v-82h-365zM146 608v86h393v-86h-393zM316 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="536" 
d="M286 -10q-71 0 -128 32.5t-90 88.5t-33 127q0 70 32.5 126t88 88.5t124.5 32.5q66 0 116.5 -30t79 -83t28.5 -120q0 -10 -1 -21.5t-4 -26.5h-401v75h353l-33 -29q0 48 -17 81.5t-48 52t-75 18.5q-46 0 -81 -20t-54 -56t-19 -85q0 -50 20 -87.5t57 -58t85 -20.5
q40 0 73.5 14t57.5 42l58 -59q-34 -40 -83.5 -61t-105.5 -21zM279 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" 
d="M82 0v694h94v-694h-94zM146 0v86h398v-86h-398zM146 314v82h365v-82h-365zM146 608v86h393v-86h-393zM499 -209q-41 0 -72.5 20.5t-31.5 68.5q0 40 25.5 76t74.5 63l49 -19q-36 -23 -57.5 -49.5t-21.5 -57.5q0 -20 11.5 -31.5t32.5 -11.5q15 0 26.5 5t20.5 10l24 -50
q-14 -9 -33.5 -16.5t-47.5 -7.5z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="536" 
d="M286 -10q-71 0 -128 32.5t-90 88.5t-33 127q0 70 32.5 126t88 88.5t124.5 32.5q66 0 116.5 -30t79 -83t28.5 -120q0 -10 -1 -21.5t-4 -26.5h-401v75h353l-33 -29q0 48 -17 81.5t-48 52t-75 18.5q-46 0 -81 -20t-54 -56t-19 -85q0 -50 20 -87.5t57 -58t85 -20.5
q40 0 73.5 14t57.5 42l58 -59q-34 -40 -83.5 -61t-105.5 -21zM341 -199q-41 0 -72.5 20.5t-31.5 68.5q0 40 25.5 76t74.5 63l49 -19q-36 -23 -57.5 -49.5t-21.5 -57.5q0 -20 11.5 -31.5t32.5 -11.5q15 0 26.5 5t20.5 10l24 -50q-14 -9 -33.5 -16.5t-47.5 -7.5z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" 
d="M82 0v694h94v-694h-94zM146 0v86h398v-86h-398zM146 314v82h365v-82h-365zM146 608v86h393v-86h-393zM446 959l40 -41l-136 -154h-70l-136 154l41 41l160 -134l-60 1z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="536" 
d="M286 -10q-71 0 -128 32.5t-90 88.5t-33 127q0 70 32.5 126t88 88.5t124.5 32.5q66 0 116.5 -30t79 -83t28.5 -120q0 -10 -1 -21.5t-4 -26.5h-401v75h353l-33 -29q0 48 -17 81.5t-48 52t-75 18.5q-46 0 -81 -20t-54 -56t-19 -85q0 -50 20 -87.5t57 -58t85 -20.5
q40 0 73.5 14t57.5 42l58 -59q-34 -40 -83.5 -61t-105.5 -21zM409 740l41 -41l-136 -154h-70l-136 154l40 41l161 -134l-61 1z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="773" 
d="M396 -10q-73 0 -136.5 27.5t-111 76.5t-74.5 114t-27 140t27 139.5t75 113.5t112.5 76t140.5 27q83 0 153 -32t117 -92l-65 -65q-33 48 -87 73.5t-118 25.5q-76 0 -134.5 -34t-91 -94t-32.5 -138q0 -79 33 -139t89 -94t128 -34q73 0 126.5 28t82.5 82.5t29 133.5l57 -41
h-300v86h340v-14q0 -121 -42.5 -202.5t-118 -122.5t-172.5 -41zM568 921q0 -48 -22 -86.5t-60 -61t-87 -22.5q-50 0 -89 22.5t-61.5 61t-22.5 86.5h82q0 -40 25 -66t66 -26q25 0 45 12t31.5 33t11.5 47h81z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="579" 
d="M263 -209q-73 0 -129.5 27t-90.5 76l58 59q29 -37 69 -56.5t95 -19.5q73 0 115.5 38.5t42.5 103.5v118l16 107l-16 106v125h90v-456q0 -68 -31.5 -119t-88 -80t-130.5 -29zM263 8q-65 0 -116.5 31t-81.5 85.5t-30 122.5t30 121.5t81.5 85t116.5 31.5q56 0 99 -22
t68.5 -61.5t27.5 -92.5v-126q-3 -52 -28.5 -91.5t-68.5 -61.5t-98 -22zM281 93q44 0 77.5 19t51.5 53.5t18 80.5t-18.5 80.5t-51.5 54t-78 19.5t-79 -19.5t-53.5 -54t-19.5 -79.5t19.5 -80t54 -54.5t79.5 -19.5zM464 702q0 -48 -22 -86.5t-60 -61t-87 -22.5q-50 0 -89 22.5
t-61.5 61t-22.5 86.5h82q0 -40 25 -66t66 -26q25 0 45 12t31.5 33t11.5 47h81z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="773" 
d="M396 -10q-73 0 -136.5 27.5t-111 76.5t-74.5 114t-27 140t27 139.5t75 113.5t112.5 76t140.5 27q83 0 153 -32t117 -92l-65 -65q-33 48 -87 73.5t-118 25.5q-76 0 -134.5 -34t-91 -94t-32.5 -138q0 -79 33 -139t89 -94t128 -34q73 0 126.5 28t82.5 82.5t29 133.5l57 -41
h-300v86h340v-14q0 -121 -42.5 -202.5t-118 -122.5t-172.5 -41zM397 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="579" 
d="M263 -209q-73 0 -129.5 27t-90.5 76l58 59q29 -37 69 -56.5t95 -19.5q73 0 115.5 38.5t42.5 103.5v118l16 107l-16 106v125h90v-456q0 -68 -31.5 -119t-88 -80t-130.5 -29zM263 8q-65 0 -116.5 31t-81.5 85.5t-30 122.5t30 121.5t81.5 85t116.5 31.5q56 0 99 -22
t68.5 -61.5t27.5 -92.5v-126q-3 -52 -28.5 -91.5t-68.5 -61.5t-98 -22zM281 93q44 0 77.5 19t51.5 53.5t18 80.5t-18.5 80.5t-51.5 54t-78 19.5t-79 -19.5t-53.5 -54t-19.5 -79.5t19.5 -80t54 -54.5t79.5 -19.5zM294 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5
t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="773" 
d="M396 -10q-73 0 -136.5 27.5t-111 76.5t-74.5 114t-27 140t27 139.5t75 113.5t112.5 76t140.5 27q83 0 153 -32t117 -92l-65 -65q-33 48 -87 73.5t-118 25.5q-76 0 -134.5 -34t-91 -94t-32.5 -138q0 -79 33 -139t89 -94t128 -34q73 0 126.5 28t82.5 82.5t29 133.5l57 -41
h-300v86h340v-14q0 -121 -42.5 -202.5t-118 -122.5t-172.5 -41zM379 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t47 19.5q27 0 47 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="579" 
d="M263 -209q-73 0 -129.5 27t-90.5 76l58 59q29 -37 69 -56.5t95 -19.5q73 0 115.5 38.5t42.5 103.5v118l16 107l-16 106v125h90v-456q0 -68 -31.5 -119t-88 -80t-130.5 -29zM263 8q-65 0 -116.5 31t-81.5 85.5t-30 122.5t30 121.5t81.5 85t116.5 31.5q56 0 99 -22
t68.5 -61.5t27.5 -92.5v-126q-3 -52 -28.5 -91.5t-68.5 -61.5t-98 -22zM281 93q44 0 77.5 19t51.5 53.5t18 80.5t-18.5 80.5t-51.5 54t-78 19.5t-79 -19.5t-53.5 -54t-19.5 -79.5t19.5 -80t54 -54.5t79.5 -19.5zM360 753l-62 -119l-32 -8q5 11 13 18t18 7q21 0 38.5 -17
t17.5 -44t-19 -47.5t-48 -20.5q-26 0 -46 20t-20 48q0 12 4.5 27.5t17.5 39.5l66 124z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="700" 
d="M0 519v74h700v-74h-700zM79 0v694h94v-694h-94zM527 0v694h94v-694h-94zM143 316v86h407v-86h-407z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="548" 
d="M0 554v85h329v-85h-329zM402 0v277q0 54 -34 89t-88 35q-36 0 -64 -16t-44 -44t-16 -64l-37 21q0 54 24 96t67 66.5t97 24.5t95.5 -24t65.5 -67t24 -99v-295h-90zM66 0v714h90v-714h-90z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="258" 
d="M-27 868h313v-74h-313v74zM82 0v694h94v-694h-94z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="222" 
d="M-46 649h314v-74h-314v74zM66 0v475h90v-475h-90z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="258" 
d="M82 0v694h94v-694h-94zM131 -209q-41 0 -72.5 20.5t-31.5 68.5q0 40 25.5 76t74.5 63l49 -19q-36 -23 -57.5 -49.5t-21.5 -57.5q0 -20 11.5 -31.5t32.5 -11.5q15 0 26.5 5t20.5 10l24 -50q-14 -9 -33.5 -16.5t-47.5 -7.5z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="224" 
d="M67 0v475h90v-475h-90zM112 570q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM111 -209q-41 0 -72.5 20.5t-31.5 68.5q0 40 25.5 76t74.5 63l49 -19q-36 -23 -57.5 -49.5t-21.5 -57.5q0 -20 11.5 -31.5t32.5 -11.5
q15 0 26.5 5t20.5 10l24 -50q-14 -9 -33.5 -16.5t-47.5 -7.5z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="258" 
d="M82 0v694h94v-694h-94zM130 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="222" 
d="M66 0v475h90v-475h-90z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="606" 
d="M321 -10q-60 0 -105.5 23.5t-74.5 63.5l66 61q19 -28 47.5 -43t63.5 -15t62 15t42.5 44.5t15.5 72.5v482h94v-477q0 -69 -27.5 -120t-75 -79t-108.5 -28zM82 220v475h90v-475h-90z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="453" 
d="M67 0v475h90v-475h-90zM112 570q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM230 -209q-46 0 -77.5 15t-56.5 43l58 59q14 -17 30.5 -25t39.5 -8q30 0 52 18.5t22 55.5v526h90v-525q0 -52 -22 -87t-58 -53.5t-78 -18.5z
M344 570q-24 0 -40 16.5t-16 41.5q0 24 16 40.5t40 16.5q25 0 41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="666" 
d="M511 0l-350 362l343 332h122l-370 -356v52l380 -390h-125zM82 0v694h94v-694h-94zM297 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t47 19.5q27 0 47 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="498" 
d="M370 0l-222 243l220 232h109l-245 -256l4 52l250 -271h-116zM66 0v714h90v-714h-90zM245 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t47 19.5q27 0 47 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="552" 
d="M82 0v694h94v-694h-94zM146 0v86h372v-86h-372zM241 765l-41 41l130 161l71 -71z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="222" 
d="M66 0v714h90v-714h-90zM51 765l-41 41l130 161l71 -71z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="552" 
d="M82 0v694h94v-694h-94zM146 0v86h372v-86h-372zM282 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t47 19.5q27 0 47 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="222" 
d="M66 0v714h90v-714h-90zM93 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t47 19.5q27 0 47 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="552" 
d="M82 0v694h94v-694h-94zM146 0v86h372v-86h-372zM300 435l-52 28l63 119l31 8q-5 -11 -12.5 -18t-18.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 28 19.5 48t47.5 20q26 0 46 -20t20 -48q0 -11 -4.5 -26.5t-16.5 -40.5z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="399" 
d="M66 0v714h90v-714h-90zM247 436l-52 28l63 119l31 8q-5 -11 -12.5 -18t-18.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 28 19.5 48t47.5 20q26 0 46 -20t20 -48q0 -11 -4.5 -26.5t-16.5 -40.5z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="628" 
d="M66 322l274 158v-86l-274 -158v86zM158 0v694h94v-694h-94zM222 0v86h372v-86h-372z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="405" 
d="M66 322l274 158v-86l-274 -158v86zM158 0v714h90v-714h-90z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="710" 
d="M82 0v694h66l28 -109v-585h-94zM562 0l-426 569l12 125l427 -568zM562 0l-28 103v591h94v-694h-66zM296 765l-41 41l130 161l71 -71z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="548" 
d="M402 0v277q0 54 -34 89t-88 35q-36 0 -64 -16t-44 -44t-16 -64l-37 21q0 54 24 96t67 66.5t97 24.5t95.5 -27t65.5 -70.5t24 -92.5v-295h-90zM66 0v475h90v-475h-90zM215 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="710" 
d="M82 0v694h66l28 -109v-585h-94zM562 0l-426 569l12 125l427 -568zM562 0l-28 103v591h94v-694h-66zM337 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t47 19.5q27 0 47 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z
" />
    <glyph glyph-name="uni0146" unicode="&#x146;" horiz-adv-x="548" 
d="M402 0v277q0 54 -34 89t-88 35q-36 0 -64 -16t-44 -44t-16 -64l-37 21q0 54 24 96t67 66.5t97 24.5t95.5 -27t65.5 -70.5t24 -92.5v-295h-90zM66 0v475h90v-475h-90zM256 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5
t47 19.5q27 0 47 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="710" 
d="M82 0v694h66l28 -109v-585h-94zM562 0l-426 569l12 125l427 -568zM562 0l-28 103v591h94v-694h-66zM486 959l40 -41l-136 -154h-70l-136 154l41 41l160 -134l-60 1z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="548" 
d="M402 0v277q0 54 -34 89t-88 35q-36 0 -64 -16t-44 -44t-16 -64l-37 21q0 54 24 96t67 66.5t96 24.5q55 0 96.5 -27t65.5 -70.5t24 -92.5v-295h-90zM66 0v475h90v-475h-90zM404 740l41 -41l-136 -154h-70l-136 154l41 41l160 -134l-61 1z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="710" 
d="M82 0v694h66l28 -109v-585h-94zM562 0l-426 569l12 125l427 -568zM562 0l-28 103v591h94v-694h-66zM470 -209q-46 0 -77.5 15t-56.5 43l58 59q14 -17 30.5 -25t39.5 -8q30 0 52 18.5t22 55.5v526h90v-525q0 -52 -22 -87t-58 -53.5t-78 -18.5z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="548" 
d="M333 -209q-46 0 -77 15t-56 43l58 59q14 -17 30.5 -25t39.5 -8q30 0 52 18.5t22 55.5v239h90v-238q0 -52 -22 -87t-58 -53.5t-79 -18.5zM402 0v277q0 54 -34 89t-88 35q-36 0 -64 -16t-44 -44t-16 -64l-37 21q0 54 24 96t67 66.5t96 24.5q55 0 96.5 -27t65.5 -70.5
t24 -92.5v-295h-90zM66 0v475h90v-475h-90z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="794" 
d="M398 -10q-74 0 -138 27.5t-112 77t-74.5 114.5t-26.5 139q0 75 26.5 139.5t74 113.5t111 76t137.5 27t137.5 -27t111.5 -76t75 -114t27 -140q0 -74 -27 -139t-74.5 -114t-111 -76.5t-136.5 -27.5zM396 80q75 0 131.5 34.5t89.5 95t33 138.5q0 58 -19 106.5t-53 84.5
t-80 55.5t-102 19.5q-74 0 -130.5 -34t-89 -94t-32.5 -138q0 -58 18.5 -107.5t52 -85t80 -55.5t101.5 -20zM217 868h360v-74h-360v74z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="566" 
d="M283 -10q-70 0 -126 33t-89 89.5t-33 126.5q0 69 33 124.5t89 88.5t126 33q69 0 125.5 -32.5t89.5 -88.5t33 -125q0 -70 -33 -126.5t-89.5 -89.5t-125.5 -33zM283 77q45 0 80 21t55 57.5t20 83.5q0 46 -20.5 82t-55 56.5t-79.5 20.5t-80 -20.5t-55 -56.5t-20 -82
q0 -47 20 -83.5t55 -57.5t80 -21zM104 649h360v-74h-360v74z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="794" 
d="M398 -10q-74 0 -138 27.5t-112 77t-74.5 114.5t-26.5 139q0 75 26.5 139.5t74 113.5t111 76t137.5 27t137.5 -27t111.5 -76t75 -114t27 -140q0 -74 -27 -139t-74.5 -114t-111 -76.5t-136.5 -27.5zM396 80q75 0 131.5 34.5t89.5 95t33 138.5q0 58 -19 106.5t-53 84.5
t-80 55.5t-102 19.5q-74 0 -130.5 -34t-89 -94t-32.5 -138q0 -58 18.5 -107.5t52 -85t80 -55.5t101.5 -20zM295 765l-55 20l50 201l94 -35zM466 765l-54 20l50 201l93 -35z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="566" 
d="M283 -10q-70 0 -126 33t-89 89.5t-33 126.5q0 69 33 124.5t89 88.5t126 33q69 0 125.5 -32.5t89.5 -88.5t33 -125q0 -70 -33 -126.5t-89.5 -89.5t-125.5 -33zM283 77q45 0 80 21t55 57.5t20 83.5q0 46 -20.5 82t-55 56.5t-79.5 20.5t-80 -20.5t-55 -56.5t-20 -82
q0 -47 20 -83.5t55 -57.5t80 -21zM181 546l-54 20l50 201l93 -35zM352 546l-54 20l50 201l94 -35z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1157" 
d="M398 -10q-74 0 -138 27.5t-112 77t-74.5 114.5t-26.5 139q0 75 26.5 139.5t74 113.5t111 76t137.5 27q75 0 135 -27t103 -76t65.5 -114t22.5 -140q0 -74 -22.5 -139t-65 -114t-102.5 -76.5t-134 -27.5zM396 80q76 0 133 34.5t89 95t32 138.5q0 77 -32 137t-89.5 94.5
t-132.5 34.5q-74 0 -130.5 -34t-89 -94t-32.5 -138q0 -58 18.5 -107.5t52 -85t80 -55.5t101.5 -20zM650 0v694h94v-694h-94zM713 0v86h399v-86h-399zM713 314v82h366v-82h-366zM713 608v86h393v-86h-393z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="943" 
d="M283 -10q-70 0 -126 33t-89 89.5t-33 126.5q0 69 33 124.5t89 88.5t126 33q69 0 125.5 -32.5t89.5 -88.5t33 -125q0 -70 -33 -126.5t-89.5 -89.5t-125.5 -33zM283 77q45 0 80 21t55 57.5t20 83.5q0 46 -20.5 82t-55 56.5t-79.5 20.5t-80 -20.5t-55 -56.5t-20 -82
q0 -47 20 -83.5t55 -57.5t80 -21zM693 -10q-71 0 -128 32.5t-90 88.5t-33 127q0 70 32.5 126t88 88.5t124.5 32.5q66 0 116.5 -30t79 -83t28.5 -120q0 -10 -1 -21.5t-4 -26.5h-401v75h353l-33 -29q0 48 -17 81.5t-48 52t-75 18.5q-46 0 -81 -20t-54 -56t-19 -85
q0 -50 20 -87.5t57 -58t85 -20.5q40 0 73.5 14t57.5 42l58 -59q-34 -40 -83.5 -61t-105.5 -21z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="630" 
d="M145 302v79h188q61 0 94 31t33 84q0 50 -32.5 83t-93.5 33h-189v82h192q66 0 115 -25.5t75.5 -69.5t26.5 -101q0 -59 -26.5 -103t-75.5 -68.5t-115 -24.5h-192zM82 0v694h94v-694h-94zM482 0l-252 311l89 31l282 -342h-119zM235 765l-41 41l130 161l71 -71z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="419" 
d="M66 0v475h90v-475h-90zM156 271l-34 15q0 91 42 145t121 54q36 0 65 -12.5t54 -41.5l-59 -61q-15 16 -33 23t-42 7q-50 0 -82 -32t-32 -97zM152 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="630" 
d="M145 302v79h188q61 0 94 31t33 84q0 50 -32.5 83t-93.5 33h-189v82h192q66 0 115 -25.5t75.5 -69.5t26.5 -101q0 -59 -26.5 -103t-75.5 -68.5t-115 -24.5h-192zM82 0v694h94v-694h-94zM482 0l-252 311l89 31l282 -342h-119zM292 -338l-51 28l62 119l31 8
q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t47 19.5q27 0 47 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="419" 
d="M66 0v475h90v-475h-90zM156 271l-34 15q0 91 42 145t121 54q36 0 65 -12.5t54 -41.5l-59 -61q-15 16 -33 23t-42 7q-50 0 -82 -32t-32 -97zM93 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t47 19.5q27 0 47 -19.5
t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="630" 
d="M145 302v79h188q61 0 94 31t33 84q0 50 -32.5 83t-93.5 33h-189v82h192q66 0 115 -25.5t75.5 -69.5t26.5 -101q0 -59 -26.5 -103t-75.5 -68.5t-115 -24.5h-192zM82 0v694h94v-694h-94zM482 0l-252 311l89 31l282 -342h-119zM424 959l41 -41l-136 -154h-70l-136 154l41 41
l160 -134l-61 1z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="419" 
d="M66 0v475h90v-475h-90zM156 271l-34 15q0 91 42 145t121 54q36 0 65 -12.5t54 -41.5l-59 -61q-15 16 -33 23t-42 7q-50 0 -82 -32t-32 -97zM342 740l41 -41l-137 -154h-69l-137 154l41 41l160 -134l-60 1z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="555" 
d="M275 -10q-82 0 -140 30t-103 87l64 64q33 -46 76 -69.5t106 -23.5q62 0 99.5 27t37.5 74q0 39 -18 63t-48.5 39.5t-66.5 28t-72 27t-66 36t-48.5 56.5t-18.5 88q0 59 28.5 100.5t78 64t111.5 22.5q68 0 122 -26.5t88 -69.5l-64 -64q-31 36 -66.5 54t-81.5 18
q-56 0 -89 -24.5t-33 -68.5q0 -35 18.5 -56.5t48.5 -36.5t66.5 -27.5t72.5 -27.5t66 -38t48.5 -59.5t18.5 -91.5q0 -92 -63.5 -144t-171.5 -52zM236 765l-41 41l130 161l71 -71z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="427" 
d="M217 -10q-40 0 -75.5 10.5t-65.5 30t-52 46.5l58 58q26 -32 60 -47.5t76 -15.5t65 14.5t23 40.5t-18.5 40.5t-47.5 24t-61.5 19t-61.5 24.5t-47.5 41t-18.5 69t21 74.5t58.5 48.5t90.5 17q56 0 99.5 -19.5t71.5 -58.5l-58 -58q-20 26 -49.5 40t-66.5 14
q-39 0 -59.5 -13.5t-20.5 -37.5t18 -37t47.5 -22t61.5 -18.5t61 -25.5t47.5 -43t18.5 -71q0 -67 -47.5 -106t-127.5 -39zM161 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="555" 
d="M275 -10q-82 0 -140 30t-103 87l64 64q33 -46 76 -69.5t106 -23.5q62 0 99.5 27t37.5 74q0 39 -18 63t-48.5 39.5t-66.5 28t-72 27t-66 36t-48.5 56.5t-18.5 88q0 59 28.5 100.5t78 64t111.5 22.5q68 0 122 -26.5t88 -69.5l-64 -64q-31 36 -66.5 54t-81.5 18
q-56 0 -89 -24.5t-33 -68.5q0 -35 18.5 -56.5t48.5 -36.5t66.5 -27.5t72.5 -27.5t66 -38t48.5 -59.5t18.5 -91.5q0 -92 -63.5 -144t-171.5 -52zM262 -209q-35 0 -63 9.5t-54 26.5l41 49q12 -12 29.5 -19t40.5 -7q30 0 44 9t14 23t-14 24t-42 10h-44l31 97h68l-23 -72l-33 27
q61 2 97.5 -23.5t36.5 -64.5t-35 -64t-94 -25z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="427" 
d="M217 -10q-40 0 -75.5 10.5t-65.5 30t-52 46.5l58 58q26 -32 60 -47.5t76 -15.5t65 14.5t23 40.5t-18.5 40.5t-47.5 24t-61.5 19t-61.5 24.5t-47.5 41t-18.5 69t21 74.5t58.5 48.5t90.5 17q56 0 99.5 -19.5t71.5 -58.5l-58 -58q-20 26 -49.5 40t-66.5 14
q-39 0 -59.5 -13.5t-20.5 -37.5t18 -37t47.5 -22t61.5 -18.5t61 -25.5t47.5 -43t18.5 -71q0 -67 -47.5 -106t-127.5 -39zM205 -209q-35 0 -63 9.5t-54 26.5l41 49q12 -12 29.5 -19t40.5 -7q30 0 44 9t14 23t-14 24t-42 10h-44l31 97h68l-23 -72l-33 27q61 2 97.5 -23.5
t36.5 -64.5t-35 -64t-94 -25z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="555" 
d="M275 -10q-82 0 -140 30t-103 87l64 64q33 -46 76 -69.5t106 -23.5q62 0 99.5 27t37.5 74q0 39 -18 63t-48.5 39.5t-66.5 28t-72 27t-66 36t-48.5 56.5t-18.5 88q0 59 28.5 100.5t78 64t111.5 22.5q68 0 122 -26.5t88 -69.5l-64 -64q-31 36 -66.5 54t-81.5 18
q-56 0 -89 -24.5t-33 -68.5q0 -35 18.5 -56.5t48.5 -36.5t66.5 -27.5t72.5 -27.5t66 -38t48.5 -59.5t18.5 -91.5q0 -92 -63.5 -144t-171.5 -52zM425 959l41 -41l-136 -154h-70l-136 154l41 41l160 -134l-61 1z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="427" 
d="M217 -10q-40 0 -75.5 10.5t-65.5 30t-52 46.5l58 58q26 -32 60 -47.5t76 -15.5t65 14.5t23 40.5t-18.5 40.5t-47.5 24t-61.5 19t-61.5 24.5t-47.5 41t-18.5 69t21 74.5t58.5 48.5t90.5 17q56 0 99.5 -19.5t71.5 -58.5l-58 -58q-20 26 -49.5 40t-66.5 14
q-39 0 -59.5 -13.5t-20.5 -37.5t18 -37t47.5 -22t61.5 -18.5t61 -25.5t47.5 -43t18.5 -71q0 -67 -47.5 -106t-127.5 -39zM351 740l41 -41l-137 -154h-69l-137 154l41 41l160 -134l-60 1z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="625" 
d="M266 0v674h94v-674h-94zM30 608v86h565v-86h-565zM443 959l41 -41l-136 -154h-70l-136 154l40 41l161 -134l-61 1z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="461" 
d="M139 0v674h90v-674h-90zM22 393v82h324v-82h-324zM306 505l-52 28l63 119l31 8q-5 -11 -12.5 -18t-18.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 28 19.5 48t47.5 20q26 0 46 -20t20 -48q0 -11 -4.5 -26.5t-16.5 -40.5z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="683" 
d="M342 -10q-77 0 -137.5 34.5t-95.5 95t-35 137.5v437h94v-435q0 -55 23 -95t62.5 -62t88.5 -22q51 0 89.5 22t61 62t22.5 94v436h95v-438q0 -77 -35 -137t-95 -94.5t-138 -34.5zM162 868h360v-74h-360v74z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="516" 
d="M258 -10q-59 0 -105.5 26.5t-72.5 73.5t-26 109v276h90v-272q0 -39 13.5 -67.5t39.5 -43.5t61 -15q53 0 83 33.5t30 92.5v272h90v-276q0 -62 -26 -109t-71.5 -73.5t-105.5 -26.5zM79 649h360v-74h-360v74z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="683" 
d="M342 -10q-77 0 -137.5 34.5t-95.5 95t-35 137.5v437h94v-435q0 -55 23 -95t62.5 -62t88.5 -22q51 0 89.5 22t61 62t22.5 94v436h95v-438q0 -77 -35 -137t-95 -94.5t-138 -34.5zM512 921q0 -48 -22 -86.5t-60 -61t-87 -22.5q-50 0 -89 22.5t-61.5 61t-22.5 86.5h82
q0 -40 25 -66t66 -26q25 0 45 12t31.5 33t11.5 47h81z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="516" 
d="M258 -10q-59 0 -105.5 26.5t-72.5 73.5t-26 109v276h90v-272q0 -39 13.5 -67.5t39.5 -43.5t61 -15q53 0 83 33.5t30 92.5v272h90v-276q0 -62 -26 -109t-71.5 -73.5t-105.5 -26.5zM429 702q0 -48 -22 -86.5t-60 -61t-87 -22.5q-50 0 -89 22.5t-61.5 61t-22.5 86.5h82
q0 -40 25 -66t66 -26q25 0 45 12t31.5 33t11.5 47h81z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="683" 
d="M342 -10q-77 0 -137.5 34.5t-95.5 95t-35 137.5v437h94v-435q0 -55 23 -95t62.5 -62t88.5 -22q51 0 89.5 22t61 62t22.5 94v436h95v-438q0 -77 -35 -137t-95 -94.5t-138 -34.5zM343 751q-35 0 -62.5 16t-43 42.5t-15.5 59.5q0 34 15.5 61t43 42.5t62.5 15.5
q34 0 60.5 -15.5t42 -42.5t15.5 -60q0 -34 -15.5 -61t-42 -42.5t-60.5 -15.5zM343 816q21 0 36 15.5t15 38.5t-15 38t-36 15q-24 0 -38.5 -15.5t-14.5 -37.5q0 -23 14.5 -38.5t38.5 -15.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="516" 
d="M258 -10q-59 0 -105.5 26.5t-72.5 73.5t-26 109v276h90v-272q0 -39 13.5 -67.5t39.5 -43.5t61 -15q53 0 83 33.5t30 92.5v272h90v-276q0 -62 -26 -109t-71.5 -73.5t-105.5 -26.5zM259 532q-35 0 -62.5 16t-43 42.5t-15.5 59.5q0 34 15.5 61t43 42.5t62.5 15.5
q34 0 60.5 -15.5t42 -42.5t15.5 -60q0 -34 -15.5 -61t-42 -42.5t-60.5 -15.5zM259 597q21 0 36 15.5t15 38.5t-15 38t-36 15q-24 0 -38.5 -15.5t-14.5 -37.5q0 -23 14.5 -38.5t38.5 -15.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="683" 
d="M342 -10q-77 0 -137.5 34.5t-95.5 95t-35 137.5v437h94v-435q0 -55 23 -95t62.5 -62t88.5 -22q51 0 89.5 22t61 62t22.5 94v436h95v-438q0 -77 -35 -137t-95 -94.5t-138 -34.5zM239 765l-54 20l50 201l94 -35zM411 765l-55 20l50 201l94 -35z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="516" 
d="M258 -10q-60 0 -106 26.5t-72 73.5t-26 109v276h90v-272q0 -39 13.5 -67.5t39.5 -43.5t61 -15q53 0 83 33.5t30 92.5v272h90v-276q0 -62 -26 -109t-71.5 -73.5t-105.5 -26.5zM156 546l-54 20l50 201l93 -35zM327 546l-54 20l50 201l94 -35z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="683" 
d="M342 -10q-77 0 -137.5 34.5t-95.5 95t-35 137.5v437h94v-435q0 -55 23 -95t62.5 -62t88.5 -22q51 0 89.5 22t61 62t22.5 94v436h95v-438q0 -77 -35 -137t-95 -94.5t-138 -34.5zM403 -199q-41 0 -72.5 20.5t-31.5 68.5q0 40 25.5 76t74.5 63l49 -19q-36 -23 -57.5 -49.5
t-21.5 -57.5q0 -20 11.5 -31.5t32.5 -11.5q15 0 26.5 5t20.5 10l24 -50q-14 -9 -33.5 -16.5t-47.5 -7.5z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="516" 
d="M258 -10q-59 0 -105.5 26.5t-72.5 73.5t-26 109v276h90v-272q0 -39 13.5 -67.5t39.5 -43.5t61 -15q53 0 83 33.5t30 92.5v272h90v-276q0 -62 -26 -109t-71.5 -73.5t-105.5 -26.5zM305 -199q-41 0 -72.5 20.5t-31.5 68.5q0 40 25.5 76t74.5 63l49 -19q-36 -23 -57.5 -49.5
t-21.5 -57.5q0 -20 11.5 -31.5t32.5 -11.5q15 0 26.5 5t20.5 10l24 -50q-14 -9 -33.5 -16.5t-47.5 -7.5z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="981" 
d="M259 0l-227 694h94l180 -566h-27l177 566h68l177 -566h-26l180 566h93l-226 -694h-67l-178 565h26l-177 -565h-67zM360 764l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="750" 
d="M202 0l-188 475h94l136 -370h-30l135 370h52l135 -370h-30l136 370h94l-187 -475h-53l-137 359h33l-138 -359h-52zM245 545l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="665" 
d="M302 282l-273 412h109l220 -337h-49l221 337h107l-275 -412h-60zM286 0v344h94v-344h-94zM202 764l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="514" 
d="M222 -4l-206 479h100l153 -375h-32l161 375h100l-222 -479h-54zM86 -199l140 287l50 -92l-90 -195h-100zM127 545l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="665" 
d="M302 282l-273 412h109l220 -337h-49l221 337h107l-275 -412h-60zM286 0v344h94v-344h-94zM233 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM433 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5
t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="583" 
d="M39 65l392 565h110l-392 -565h-110zM39 0v65l80 21h415v-86h-495zM60 608v86h481v-64l-81 -22h-400zM231 765l-41 41l130 161l71 -71z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="447" 
d="M28 49l282 377h106l-282 -377h-106zM28 0v49l80 33h302v-82h-382zM45 393v82h371v-49l-83 -33h-288zM179 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="583" 
d="M39 65l392 565h110l-392 -565h-110zM39 0v65l80 21h415v-86h-495zM60 608v86h481v-64l-81 -22h-400zM291 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="447" 
d="M28 49l282 377h106l-282 -377h-106zM28 0v49l80 33h302v-82h-382zM45 393v82h371v-49l-83 -33h-288zM239 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="583" 
d="M39 65l392 565h110l-392 -565h-110zM39 0v65l80 21h415v-86h-495zM60 608v86h481v-64l-81 -22h-400zM421 959l41 -41l-136 -154h-70l-136 154l40 41l161 -134l-61 1z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="447" 
d="M28 49l282 377h106l-282 -377h-106zM28 0v49l80 33h302v-82h-382zM45 393v82h371v-49l-83 -33h-288zM368 740l41 -41l-136 -154h-70l-136 154l41 41l160 -134l-61 1z" />
    <glyph glyph-name="uni01CD" unicode="&#x1cd;" horiz-adv-x="696" 
d="M31 0l284 694h68l283 -694h-102l-233 587h35l-235 -587h-100zM166 157v82h365v-82h-365zM478 959l41 -41l-136 -154h-70l-136 154l40 41l161 -134l-61 1z" />
    <glyph glyph-name="uni01CE" unicode="&#x1ce;" horiz-adv-x="582" 
d="M268 -10q-65 0 -118 32.5t-83.5 88.5t-30.5 126t30.5 126t83 89t118.5 33q54 0 96.5 -22.5t68.5 -62t29 -91.5v-143q-3 -51 -28.5 -91t-68 -62.5t-97.5 -22.5zM283 75q67 0 108 45.5t41 116.5q0 49 -18.5 85.5t-52.5 57t-79 20.5t-79.5 -21t-54 -57.5t-19.5 -83.5
q0 -48 19.5 -84.5t54.5 -57.5t80 -21zM426 0v128l17 116l-17 115v116h91v-475h-91zM424 740l40 -41l-136 -154h-70l-136 154l41 41l160 -134l-60 1z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="555" 
d="M275 -10q-82 0 -140 30t-103 87l64 64q33 -46 76 -69.5t106 -23.5q62 0 99.5 27t37.5 74q0 39 -18 63t-48.5 39.5t-66.5 28t-72 27t-66 36t-48.5 56.5t-18.5 88q0 59 28.5 100.5t78 64t111.5 22.5q68 0 122 -26.5t88 -69.5l-64 -64q-31 36 -66.5 54t-81.5 18
q-56 0 -89 -24.5t-33 -68.5q0 -35 18.5 -56.5t48.5 -36.5t66.5 -27.5t72.5 -27.5t66 -38t48.5 -59.5t18.5 -91.5q0 -92 -63.5 -144t-171.5 -52zM260 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t47 19.5q27 0 47 -19.5
t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="427" 
d="M217 -10q-40 0 -75.5 10.5t-65.5 30t-52 46.5l58 58q26 -32 60 -47.5t76 -15.5t65 14.5t23 40.5t-18.5 40.5t-47.5 24t-61.5 19t-61.5 24.5t-47.5 41t-18.5 69t21 74.5t58.5 48.5t90.5 17q56 0 99.5 -19.5t71.5 -58.5l-58 -58q-20 26 -49.5 40t-66.5 14
q-39 0 -59.5 -13.5t-20.5 -37.5t18 -37t47.5 -22t61.5 -18.5t61 -25.5t47.5 -43t18.5 -71q0 -67 -47.5 -106t-127.5 -39zM203 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t47 19.5q27 0 47 -19.5t20 -48.5
q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="625" 
d="M266 0v674h94v-674h-94zM30 608v86h565v-86h-565zM295 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t47 19.5q27 0 47 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="368" 
d="M139 0v674h90v-674h-90zM22 393v82h324v-82h-324zM166 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t47 19.5q27 0 47 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="230" 
d="M6 -209q-46 0 -77.5 15t-56.5 43l58 59q14 -17 30.5 -25t39.5 -8q30 0 52 18.5t22 55.5v526h90v-525q0 -52 -22 -87t-58 -53.5t-78 -18.5z" />
    <glyph glyph-name="apostrophe" unicode="&#x2bc;" horiz-adv-x="256" 
d="M47 493l78 119l35 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 28 19 48t48 20q28 0 47 -19t19 -47q0 -13 -5.5 -29.5t-21.5 -39.5l-82 -124z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="422" 
d="M81 545l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="422" 
d="M342 740l40 -41l-136 -154h-70l-136 154l41 41l160 -134l-60 1z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="422" 
d="M382 702q0 -48 -22 -86.5t-60 -61t-87 -22.5q-50 0 -89 22.5t-61.5 61t-22.5 86.5h82q0 -40 25 -66t66 -26q25 0 45 12t31.5 33t11.5 47h81z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="294" 
d="M147 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="319" 
d="M161 532q-35 0 -62.5 16t-43 42.5t-15.5 59.5q0 34 15.5 61t43 42.5t62.5 15.5q34 0 60.5 -15.5t42 -42.5t15.5 -60q0 -34 -15.5 -61t-42 -42.5t-60.5 -15.5zM161 597q21 0 36 15.5t15 38.5t-15 38t-36 15q-24 0 -38.5 -15.5t-14.5 -37.5q0 -23 14.5 -38.5t38.5 -15.5z
" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="264" 
d="M143 -209q-41 0 -72.5 20.5t-31.5 68.5q0 40 25.5 76t74.5 63l49 -19q-36 -23 -57.5 -49.5t-21.5 -57.5q0 -20 11.5 -31.5t32.5 -11.5q15 0 26.5 5t20.5 10l24 -50q-14 -9 -33.5 -16.5t-47.5 -7.5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="456" 
d="M300 548q-32 0 -54.5 12t-42.5 23t-45 11t-40 -9t-29 -27l-49 48q25 30 52 48t65 18q33 0 56 -11t43 -23t43 -12q25 0 40 9.5t28 27.5l49 -48q-26 -30 -52.5 -48.5t-63.5 -18.5z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="395" 
d="M95 531l-55 20l50 201l94 -35zM266 531l-55 20l50 201l94 -35z" />
    <glyph glyph-name="gravecomb" unicode="&#x300;" horiz-adv-x="0" 
d="M200 539l-160 131l71 70l130 -161z" />
    <glyph glyph-name="acutecomb" unicode="&#x301;" horiz-adv-x="0" 
d="M81 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="uni0302" unicode="&#x302;" horiz-adv-x="0" 
d="M81 545l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="tildecomb" unicode="&#x303;" horiz-adv-x="0" 
d="M300 548q-32 0 -54.5 12t-42.5 23t-45 11t-40 -9t-29 -27l-49 48q25 30 52 48t65 18q33 0 56 -11t43 -23t43 -12q25 0 40 9.5t28 27.5l49 -48q-26 -30 -52.5 -48.5t-63.5 -18.5z" />
    <glyph glyph-name="uni0304" unicode="&#x304;" horiz-adv-x="0" 
d="M40 649h360v-74h-360v74z" />
    <glyph glyph-name="uni0306" unicode="&#x306;" horiz-adv-x="0" 
d="M382 702q0 -48 -22 -86.5t-60 -61t-87 -22.5q-50 0 -89 22.5t-61.5 61t-22.5 86.5h82q0 -40 25 -66t66 -26q25 0 45 12t31.5 33t11.5 47h81z" />
    <glyph glyph-name="uni0307" unicode="&#x307;" horiz-adv-x="0" 
d="M147 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="uni0308" unicode="&#x308;" horiz-adv-x="0" 
d="M117 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM317 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="uni030A" unicode="&#x30a;" horiz-adv-x="0" 
d="M161 532q-35 0 -62.5 16t-43 42.5t-15.5 59.5q0 34 15.5 61t43 42.5t62.5 15.5q34 0 60.5 -15.5t42 -42.5t15.5 -60q0 -34 -15.5 -61t-42 -42.5t-60.5 -15.5zM161 597q21 0 36 15.5t15 38.5t-15 38t-36 15q-24 0 -38.5 -15.5t-14.5 -37.5q0 -23 14.5 -38.5t38.5 -15.5z
" />
    <glyph glyph-name="uni030B" unicode="&#x30b;" horiz-adv-x="0" 
d="M95 531l-55 20l50 201l94 -35zM266 531l-55 20l50 201l94 -35z" />
    <glyph glyph-name="uni030C" unicode="&#x30c;" horiz-adv-x="0" 
d="M342 740l40 -41l-136 -154h-70l-136 154l41 41l160 -134l-60 1z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" horiz-adv-x="0" 
d="M180 753l-62 -119l-32 -8q5 11 13 18t18 7q21 0 38.5 -17t17.5 -44t-19 -47.5t-48 -20.5q-26 0 -46 20t-20 48q0 12 4.5 27.5t17.5 39.5l66 124z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="0" 
d="M92 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t47 19.5q27 0 47 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="uni0327" unicode="&#x327;" horiz-adv-x="0" 
d="M129 -209q-35 0 -63 9.5t-54 26.5l41 49q12 -12 29.5 -19t40.5 -7q30 0 44 9t14 23t-14 24t-42 10h-44l31 97h68l-23 -72l-33 27q61 2 97.5 -23.5t36.5 -64.5t-35 -64t-94 -25z" />
    <glyph glyph-name="uni0328" unicode="&#x328;" horiz-adv-x="0" 
d="M143 -209q-41 0 -72.5 20.5t-31.5 68.5q0 40 25.5 76t74.5 63l49 -19q-36 -23 -57.5 -49.5t-21.5 -57.5q0 -20 11.5 -31.5t32.5 -11.5q15 0 26.5 5t20.5 10l24 -50q-14 -9 -33.5 -16.5t-47.5 -7.5z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="981" 
d="M259 0l-227 694h94l180 -566h-27l177 566h68l177 -566h-26l180 566h93l-226 -694h-67l-178 565h26l-177 -565h-67zM530 758l-160 131l71 70l130 -161z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="750" 
d="M202 0l-188 475h94l136 -370h-30l135 370h52l135 -370h-30l136 370h94l-187 -475h-53l-137 359h33l-138 -359h-52zM415 539l-160 131l71 70l130 -161z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="981" 
d="M259 0l-227 694h94l180 -566h-27l177 566h68l177 -566h-26l180 566h93l-226 -694h-67l-178 565h26l-177 -565h-67zM431 765l-41 41l130 161l71 -71z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="750" 
d="M202 0l-188 475h94l136 -370h-30l135 370h52l135 -370h-30l136 370h94l-187 -475h-53l-137 359h33l-138 -359h-52zM315 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="981" 
d="M259 0l-227 694h94l180 -566h-27l177 566h68l177 -566h-26l180 566h93l-226 -694h-67l-178 565h26l-177 -565h-67zM391 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM591 774q-25 0 -41 16.5t-16 41.5q0 24 16 40.5
t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="750" 
d="M202 0l-188 475h94l136 -370h-30l135 370h52l135 -370h-30l136 370h94l-187 -475h-53l-137 359h33l-138 -359h-52zM275 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM475 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5
t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="uni1E9E" unicode="&#x1e9e;" horiz-adv-x="716" 
d="M346 407l201 208l88 -25l-183 -188l-106 -52v57zM423 -9q-60 0 -97.5 13.5t-61.5 37.5l6 113q23 -35 63 -55.5t89 -20.5q46 0 79.5 17.5t53 49.5t19.5 74q0 43 -19.5 74.5t-54.5 48.5t-83 17q-18 0 -36 -2.5t-35 -7.5l40 54q19 9 41 14t44 5q56 0 100.5 -26t71 -73
t26.5 -109q0 -66 -31.5 -116.5t-86.5 -79t-128 -28.5zM374 715q51 0 98.5 -9.5t84 -21.5t57.5 -21.5l21 -9.5v-64l-18 4.5t-47.5 11.5t-64.5 13.5t-69 11t-62 4.5q-61 0 -106.5 -20.5t-70.5 -56t-25 -78.5v-479h-90v486q0 71 39.5 122t106 79t146.5 28z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="665" 
d="M302 282l-273 412h109l220 -337h-49l221 337h107l-275 -412h-60zM286 0v344h94v-344h-94zM373 758l-160 131l71 70l130 -161z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="514" 
d="M222 -4l-206 479h100l153 -375h-32l161 375h100l-222 -479h-54zM86 -199l140 287l50 -92l-90 -195h-100zM297 539l-160 131l71 70l130 -161z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="549" 
d="M66 216v77h417v-77h-417z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="852" 
d="M66 217v74h714v-74h-714z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="248" 
d="M191 695l-62 -119l-31 -8q5 11 12.5 18t17.5 7q21 0 38.5 -17t17.5 -44t-19.5 -47.5t-46.5 -20.5t-47 20t-20 48q0 12 5 27.5t17 39.5l67 124z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="249" 
d="M58 473l62 119l32 8q-5 -11 -12.5 -18t-18.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 28 19.5 48t47.5 20q26 0 46 -20t20 -48q0 -11 -4.5 -26.5t-17.5 -40.5l-66 -124z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="275" 
d="M71 -110l62 119l32 8q-5 -11 -12.5 -18t-18.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t48 19.5q26 0 46 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5l-66 -125z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="432" 
d="M191 695l-62 -119l-31 -8q5 11 12.5 18t17.5 7q21 0 38.5 -17t17.5 -44t-19.5 -47.5t-46.5 -20.5t-47 20t-20 48q0 12 5 27.5t17 39.5l67 124zM375 695l-62 -119l-31 -8q5 11 12.5 18t17.5 7q21 0 38.5 -17t17.5 -44t-19.5 -47.5t-46.5 -20.5t-47 20t-20 48q0 12 5 27.5
t17 39.5l67 124z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="433" 
d="M242 473l62 119l32 8q-5 -11 -12.5 -18t-18.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 28 19.5 48t47.5 20q26 0 46 -20t20 -48q0 -11 -4.5 -26.5t-17.5 -40.5l-66 -124zM58 473l62 119l32 8q-5 -11 -12.5 -18t-18.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 28 19.5 48t47.5 20
q26 0 46 -20t20 -48q0 -11 -4.5 -26.5t-17.5 -40.5l-66 -124z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="459" 
d="M255 -110l62 119l32 8q-5 -11 -12.5 -18t-18.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 28 19.5 48t47.5 20q26 0 46 -20t20 -48q0 -11 -4.5 -26.5t-17.5 -40.5l-66 -124zM71 -110l62 119l32 8q-5 -11 -12.5 -18t-18.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 28 19.5 48t47.5 20
q26 0 46 -20t20 -48q0 -11 -4.5 -26.5t-17.5 -40.5l-66 -124z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="330" 
d="M165 267q-29 0 -51 13t-34.5 35.5t-12.5 49.5t12.5 49t34.5 35.5t51 13.5t50.5 -13.5t34 -35.5t12.5 -49t-12.5 -49.5t-34 -35.5t-50.5 -13z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="750" 
d="M375 -10q-28 0 -46.5 19.5t-18.5 46.5q0 28 18.5 46.5t46.5 18.5t46.5 -18.5t18.5 -46.5q0 -27 -18.5 -46.5t-46.5 -19.5zM605 -10q-28 0 -46.5 19.5t-18.5 46.5q0 28 18.5 46.5t46.5 18.5t46.5 -18.5t18.5 -46.5q0 -27 -18.5 -46.5t-46.5 -19.5zM145 -10
q-28 0 -46.5 19.5t-18.5 46.5q0 28 18.5 46.5t46.5 18.5t46.5 -18.5t18.5 -46.5q0 -27 -18.5 -46.5t-46.5 -19.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="274" 
d="M146 82l-109 158l109 159h92l-113 -159l113 -158h-92z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="274" 
d="M36 82l114 158l-114 159h92l109 -159l-109 -158h-92z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="660" 
d="M278 302l39 -36l-206 -280l-77 74zM372 395l-39 37l206 280l77 -75z" />
    <glyph glyph-name="uni2074" unicode="&#x2074;" horiz-adv-x="350" 
d="M33 459l113 235h87l-115 -235h-85zM33 415v44l23 24h262v-68h-285zM203 342v213h80v-213h-80z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="725" 
d="M428 -10q-75 0 -136.5 27.5t-106.5 76.5t-69 114t-24 139q0 75 24 140t68.5 113.5t106.5 76t137 27.5q78 0 135.5 -28t105.5 -76l-65 -65q-33 39 -77 60t-103 21q-72 0 -125.5 -34.5t-82.5 -95t-29 -139.5q0 -78 29.5 -139t84 -95.5t128.5 -34.5q59 0 103 21.5t77 60.5
l65 -64q-48 -50 -108 -78t-138 -28zM25 398v74h476l-24 -74h-452zM25 223v75h435l-24 -75h-411z" />
    <glyph glyph-name="uni2120" unicode="&#x2120;" horiz-adv-x="663" 
d="M146 314q-41 0 -70.5 15t-51.5 44l32 32q17 -23 38.5 -35t53.5 -12q31 0 50 13.5t19 37.5q0 19 -9.5 31t-24.5 19.5t-33.5 14t-36.5 13.5t-33 18.5t-24 28.5t-9 44q0 29 14.5 50t39 32t55.5 11q35 0 61.5 -13t43.5 -35l-32 -32q-15 18 -33 27.5t-41 9.5
q-28 0 -44.5 -12.5t-16.5 -34.5q0 -17 9.5 -28.5t24 -19t33 -13t36.5 -13.5t33 -19.5t24.5 -29.5t9.5 -46q0 -46 -32 -72t-86 -26zM299 319v347h33l149 -241h-23l148 241h33v-347h-48v265l11 -3l-116 -191h-34l-116 191l11 3v-265h-48z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="693" 
d="M142 319v337h47v-337h-47zM24 624v42h283v-42h-283zM330 319v347h33l148 -241h-23l148 241h33v-347h-47v265l11 -3l-116 -191h-34l-116 191l10 3v-265h-47z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="658" 
d="M135 295v86h486v-86h-486zM399 585l-247 -247l247 -247h-120l-247 247l247 247h120z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="658" 
d="M286 532h86v-486h-86v486zM576 268l-247 247l-247 -247v120l247 247l247 -247v-120z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="658" 
d="M523 381v-86h-486v86h486zM259 91l247 247l-247 247h120l247 -247l-247 -247h-120z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="658" 
d="M372 144h-86v486h86v-486zM82 408l247 -247l247 247v-120l-247 -247l-247 247v120z" />
    <glyph glyph-name="uni2196" unicode="&#x2196;" horiz-adv-x="658" 
d="M125 482l60 60l391 -390l-60 -61zM516 501h-349v-350l-85 84v350h350z" />
    <glyph glyph-name="uni2197" unicode="&#x2197;" horiz-adv-x="658" 
d="M473 542l60 -60l-390 -391l-61 61zM492 151v350h-350l84 84h350v-350z" />
    <glyph glyph-name="uni2198" unicode="&#x2198;" horiz-adv-x="658" 
d="M533 194l-60 -60l-391 391l61 60zM142 176h350v349l84 -84v-350h-350z" />
    <glyph glyph-name="uni2199" unicode="&#x2199;" horiz-adv-x="658" 
d="M185 134l-60 60l391 391l60 -60zM167 525v-349h349l-84 -85h-350v350z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="542" 
d="M59 294v81h424v-81h-424z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="390" 
d="M14 -40l278 773h84l-277 -773h-85z" />
    <glyph glyph-name="uni2713" unicode="&#x2713;" horiz-adv-x="778" 
d="M260 78l-238 235l58 58l228 -229h-42l431 473l59 -58l-445 -479h-51z" />
    <glyph glyph-name="T_T" horiz-adv-x="1104" 
d="M266 0v674h94v-674h-94zM30 608v86h565v-86h-565zM744 0v674h94v-674h-94zM509 608v86h565v-86h-565z" />
    <glyph glyph-name="T_h" horiz-adv-x="1094" 
d="M266 0v674h94v-674h-94zM30 608v86h614v-86h-614zM948 0v277q0 54 -33.5 89t-87.5 35q-37 0 -65 -16t-44 -44t-16 -64l-37 21q0 54 24.5 96t67 66.5t96.5 24.5t96 -24t65.5 -67t23.5 -99v-295h-90zM612 0v694h90v-694h-90z" />
    <glyph glyph-name="T_l" horiz-adv-x="768" 
d="M266 0v674h94v-674h-94zM30 608v86h625v-86h-625zM612 0v694h90v-694h-90z" />
    <glyph glyph-name="ae.ss01" horiz-adv-x="907" 
d="M686 -10q-74 0 -131.5 33t-90 89.5t-32.5 126.5q0 69 33 124.5t89.5 88.5t127.5 33q69 0 118.5 -29.5t80.5 -83.5l-292 -292l-53 53l269 268l1 -65q-18 31 -47 48.5t-76 17.5q-48 0 -84.5 -21t-57 -58t-20.5 -84q0 -48 20.5 -85.5t57.5 -58t86 -20.5q43 0 76.5 13.5
t57.5 39.5l57 -58q-34 -40 -82.5 -60t-107.5 -20zM268 -10q-65 0 -118 32.5t-83.5 88.5t-30.5 126t30.5 126t83 89t118.5 33q54 0 96.5 -22.5t68.5 -62t29 -91.5v-143q-3 -51 -28.5 -91t-68 -62.5t-97.5 -22.5zM283 75q67 0 108 45.5t41 116.5q0 49 -18.5 85.5t-52.5 57
t-79 20.5t-79.5 -21t-54 -57.5t-19.5 -83.5q0 -48 19.5 -84.5t54.5 -57.5t80 -21zM426 0v128l17 116l-17 115v116h91v-475h-91z" />
    <glyph glyph-name="e.ss01" horiz-adv-x="510" 
d="M289 -10q-74 0 -131.5 33t-90 89.5t-32.5 126.5q0 69 33 124.5t89.5 88.5t127.5 33q69 0 118.5 -29.5t80.5 -83.5l-292 -292l-53 53l269 268l1 -65q-18 31 -47 48.5t-76 17.5q-48 0 -84.5 -21t-57 -58t-20.5 -84q0 -48 20.5 -85.5t57.5 -58t86 -20.5q43 0 76.5 13.5
t57.5 39.5l57 -58q-34 -40 -82.5 -60t-107.5 -20z" />
    <glyph glyph-name="eacute.ss01" horiz-adv-x="510" 
d="M289 -10q-74 0 -131.5 33t-90 89.5t-32.5 126.5q0 69 33 124.5t89.5 88.5t127.5 33q69 0 118.5 -29.5t80.5 -83.5l-292 -292l-53 53l269 268l1 -65q-18 31 -47 48.5t-76 17.5q-48 0 -84.5 -21t-57 -58t-20.5 -84q0 -48 20.5 -85.5t57.5 -58t86 -20.5q43 0 76.5 13.5
t57.5 39.5l57 -58q-34 -40 -82.5 -60t-107.5 -20zM219 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="ecaron.ss01" horiz-adv-x="510" 
d="M289 -10q-74 0 -131.5 33t-90 89.5t-32.5 126.5q0 69 33 124.5t89.5 88.5t127.5 33q69 0 118.5 -29.5t80.5 -83.5l-292 -292l-53 53l269 268l1 -65q-18 31 -47 48.5t-76 17.5q-48 0 -84.5 -21t-57 -58t-20.5 -84q0 -48 20.5 -85.5t57.5 -58t86 -20.5q43 0 76.5 13.5
t57.5 39.5l57 -58q-34 -40 -82.5 -60t-107.5 -20zM409 740l41 -41l-136 -154h-70l-136 154l40 41l161 -134l-61 1z" />
    <glyph glyph-name="ecircumflex.ss01" horiz-adv-x="510" 
d="M289 -10q-74 0 -131.5 33t-90 89.5t-32.5 126.5q0 69 33 124.5t89.5 88.5t127.5 33q69 0 118.5 -29.5t80.5 -83.5l-292 -292l-53 53l269 268l1 -65q-18 31 -47 48.5t-76 17.5q-48 0 -84.5 -21t-57 -58t-20.5 -84q0 -48 20.5 -85.5t57.5 -58t86 -20.5q43 0 76.5 13.5
t57.5 39.5l57 -58q-34 -40 -82.5 -60t-107.5 -20zM149 545l-41 40l136 155h70l136 -155l-40 -40l-161 134l61 -1z" />
    <glyph glyph-name="edieresis.ss01" horiz-adv-x="510" 
d="M289 -10q-74 0 -131.5 33t-90 89.5t-32.5 126.5q0 69 33 124.5t89.5 88.5t127.5 33q69 0 118.5 -29.5t80.5 -83.5l-292 -292l-53 53l269 268l1 -65q-18 31 -47 48.5t-76 17.5q-48 0 -84.5 -21t-57 -58t-20.5 -84q0 -48 20.5 -85.5t57.5 -58t86 -20.5q43 0 76.5 13.5
t57.5 39.5l57 -58q-34 -40 -82.5 -60t-107.5 -20zM179 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5zM379 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="edotaccent.ss01" horiz-adv-x="510" 
d="M289 -10q-74 0 -131.5 33t-90 89.5t-32.5 126.5q0 69 33 124.5t89.5 88.5t127.5 33q69 0 118.5 -29.5t80.5 -83.5l-292 -292l-53 53l269 268l1 -65q-18 31 -47 48.5t-76 17.5q-48 0 -84.5 -21t-57 -58t-20.5 -84q0 -48 20.5 -85.5t57.5 -58t86 -20.5q43 0 76.5 13.5
t57.5 39.5l57 -58q-34 -40 -82.5 -60t-107.5 -20zM279 555q-25 0 -41 16.5t-16 41.5q0 24 16 40.5t41 16.5t41 -16.5t16 -40.5q0 -25 -16 -41.5t-41 -16.5z" />
    <glyph glyph-name="egrave.ss01" horiz-adv-x="510" 
d="M289 -10q-74 0 -131.5 33t-90 89.5t-32.5 126.5q0 69 33 124.5t89.5 88.5t127.5 33q69 0 118.5 -29.5t80.5 -83.5l-292 -292l-53 53l269 268l1 -65q-18 31 -47 48.5t-76 17.5q-48 0 -84.5 -21t-57 -58t-20.5 -84q0 -48 20.5 -85.5t57.5 -58t86 -20.5q43 0 76.5 13.5
t57.5 39.5l57 -58q-34 -40 -82.5 -60t-107.5 -20zM319 539l-160 131l71 70l130 -161z" />
    <glyph glyph-name="emacron.ss01" horiz-adv-x="510" 
d="M289 -10q-74 0 -131.5 33t-90 89.5t-32.5 126.5q0 69 33 124.5t89.5 88.5t127.5 33q69 0 118.5 -29.5t80.5 -83.5l-292 -292l-53 53l269 268l1 -65q-18 31 -47 48.5t-76 17.5q-48 0 -84.5 -21t-57 -58t-20.5 -84q0 -48 20.5 -85.5t57.5 -58t86 -20.5q43 0 76.5 13.5
t57.5 39.5l57 -58q-34 -40 -82.5 -60t-107.5 -20zM99 649h360v-74h-360v74z" />
    <glyph glyph-name="eogonek.ss01" horiz-adv-x="510" 
d="M289 -10q-74 0 -131.5 33t-90 89.5t-32.5 126.5q0 69 33 124.5t89.5 88.5t127.5 33q69 0 118.5 -29.5t80.5 -83.5l-292 -292l-53 53l269 268l1 -65q-18 31 -47 48.5t-76 17.5q-48 0 -84.5 -21t-57 -58t-20.5 -84q0 -48 20.5 -85.5t57.5 -58t86 -20.5q43 0 76.5 13.5
t57.5 39.5l57 -58q-34 -40 -82.5 -60t-107.5 -20zM350 -199q-41 0 -72.5 20.5t-31.5 68.5q0 40 25.5 76t74.5 63l49 -19q-36 -23 -57.5 -49.5t-21.5 -57.5q0 -20 11.5 -31.5t32.5 -11.5q15 0 26.5 5t20.5 10l24 -50q-14 -9 -33.5 -16.5t-47.5 -7.5z" />
    <glyph glyph-name="g.ss01" horiz-adv-x="505" 
d="M240 -209q-61 0 -110.5 23t-78.5 63t-29 94q0 40 12 67t36 49l71 -48q-13 -12 -19.5 -27.5t-6.5 -37.5q0 -44 34 -70t91 -26q60 0 94 28.5t34 79.5q0 33 -16 59t-44 41.5t-64 15.5q-54 0 -97.5 24.5t-69 68t-25.5 97.5q0 56 26 99t70 68.5t99 25.5t99 -24.5t69.5 -66
t25.5 -94.5q0 -51 -25 -92t-67.5 -64.5t-96.5 -23.5l-6 49q63 0 111 -24t76 -66.5t28 -97.5q0 -56 -27.5 -99t-76.5 -67t-117 -24zM247 186q32 0 56 14t38 38t14 55t-14 55t-38 37.5t-56 13.5t-56.5 -13.5t-38 -37.5t-13.5 -55t13.5 -55t38.5 -38t56 -14zM381 369l-55 56
l111 112l55 -56z" />
    <glyph glyph-name="k.ss01" horiz-adv-x="488" 
d="M403 -14l-255 257l250 247l59 -58l-222 -216l4 58l229 -228zM66 0v714h90v-714h-90z" />
    <glyph glyph-name="uni0137.ss01" horiz-adv-x="488" 
d="M403 -14l-255 257l250 247l59 -58l-222 -216l4 58l229 -228zM66 0v714h90v-714h-90zM245 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t47 19.5q27 0 47 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="m.ss01" horiz-adv-x="836" 
d="M54 0v280q0 62 26.5 108t72.5 71.5t106 25.5q62 0 106.5 -29t65.5 -80h-26q21 51 66 80t107 29q60 0 106 -25.5t72 -71.5t26 -108v-280h-90v269q0 62 -30.5 96t-83.5 34q-55 0 -85 -33.5t-30 -96.5v-269h-90v269q0 63 -30 96.5t-84 33.5t-84 -33.5t-30 -96.5v-269h-91z
" />
    <glyph glyph-name="n.ss01" horiz-adv-x="525" 
d="M54 0v273q0 63 26.5 110.5t73 74.5t109.5 27t109.5 -26.5t72.5 -74.5t26 -111v-273h-90v266q0 59 -30.5 96t-87.5 37t-88 -37t-31 -96v-266h-90z" />
    <glyph glyph-name="nacute.ss01" horiz-adv-x="525" 
d="M54 0v273q0 63 26.5 110.5t73 74.5t109.5 27t109.5 -26.5t72.5 -74.5t26 -111v-273h-90v266q0 59 -30.5 96t-87.5 37t-88 -37t-31 -96v-266h-90zM204 546l-41 41l130 161l71 -71z" />
    <glyph glyph-name="ncaron.ss01" horiz-adv-x="525" 
d="M54 0v273q0 63 26.5 110.5t73 74.5t109.5 27t109.5 -26.5t72.5 -74.5t26 -111v-273h-90v266q0 59 -30.5 96t-87.5 37t-88 -37t-31 -96v-266h-90zM393 740l41 -41l-136 -154h-70l-136 154l41 41l160 -134l-61 1z" />
    <glyph glyph-name="uni0146.ss01" horiz-adv-x="525" 
d="M54 0v273q0 63 26.5 110.5t73 74.5t109.5 27t109.5 -26.5t72.5 -74.5t26 -111v-273h-90v266q0 59 -30.5 96t-87.5 37t-88 -37t-31 -96v-266h-90zM245 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 29 19 48.5t47 19.5
q27 0 47 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="ntilde.ss01" horiz-adv-x="525" 
d="M54 0v273q0 63 26.5 110.5t73 74.5t109.5 27t109.5 -26.5t72.5 -74.5t26 -111v-273h-90v266q0 59 -30.5 96t-87.5 37t-88 -37t-31 -96v-266h-90zM336 548q-32 0 -54.5 12t-42.5 23t-45 11t-40 -9t-29 -27l-49 48q25 30 52 48t65 18q33 0 56 -11t43 -23t43 -12
q25 0 40 9.5t28 27.5l49 -48q-26 -30 -52.5 -48.5t-63.5 -18.5z" />
    <glyph glyph-name="eng.ss01" horiz-adv-x="525" 
d="M313 -209q-46 0 -77.5 15t-56.5 43l58 59q14 -17 30.5 -25t39.5 -8q30 0 52 18.5t22 55.5v239h90v-238q0 -52 -22 -87t-58 -53.5t-78 -18.5zM54 0v273q0 63 26.5 110.5t73 74.5t109.5 27t109.5 -26.5t72.5 -74.5t26 -111v-273h-90v266q0 59 -30.5 96t-87.5 37t-88 -37
t-31 -96v-266h-90z" />
    <glyph glyph-name="oe.ss01" horiz-adv-x="917" 
d="M283 -10q-70 0 -126 33t-89 89.5t-33 126.5q0 69 33 124.5t89 88.5t126 33q69 0 125.5 -32.5t89.5 -88.5t33 -125q0 -70 -33 -126.5t-89.5 -89.5t-125.5 -33zM283 77q45 0 80 21t55 57.5t20 83.5q0 46 -20.5 82t-55 56.5t-79.5 20.5t-80 -20.5t-55 -56.5t-20 -82
q0 -47 20 -83.5t55 -57.5t80 -21zM696 -10q-74 0 -131.5 33t-90 89.5t-32.5 126.5q0 69 33 124.5t89.5 88.5t127.5 33q69 0 118.5 -29.5t80.5 -83.5l-292 -292l-53 53l269 268l1 -65q-18 31 -47 48.5t-76 17.5q-48 0 -84.5 -21t-57 -58t-20.5 -84q0 -48 20.5 -85.5t57.5 -58
t86 -20.5q43 0 76.5 13.5t57.5 39.5l57 -58q-34 -40 -82.5 -60t-107.5 -20z" />
    <glyph glyph-name="t.ss01" horiz-adv-x="450" 
d="M295 -10q-43 0 -78.5 18.5t-56.5 54t-21 87.5v524h90v-522q0 -39 20.5 -58.5t51.5 -19.5q22 0 39.5 8t31.5 23l55 -55q-25 -30 -55.5 -45t-76.5 -15zM22 391v84h384v-84h-384z" />
    <glyph glyph-name="tcaron.ss01" horiz-adv-x="450" 
d="M295 -10q-43 0 -78.5 18.5t-56.5 54t-21 87.5v524h90v-522q0 -39 20.5 -58.5t51.5 -19.5q22 0 39.5 8t31.5 23l55 -55q-25 -30 -55.5 -45t-76.5 -15zM22 391v84h384v-84h-384zM322 505l-52 28l63 119l31 8q-5 -11 -12.5 -18t-18.5 -7q-21 0 -38.5 17.5t-17.5 43.5
q0 28 19.5 48t47.5 20q26 0 46 -20t20 -48q0 -11 -4.5 -26.5t-16.5 -40.5z" />
    <glyph glyph-name="uni021B.ss01" horiz-adv-x="450" 
d="M295 -10q-43 0 -78.5 18.5t-56.5 54t-21 87.5v524h90v-522q0 -39 20.5 -58.5t51.5 -19.5q22 0 39.5 8t31.5 23l55 -55q-25 -30 -55.5 -45t-76.5 -15zM22 391v84h384v-84h-384zM242 -338l-51 28l62 119l31 8q-5 -11 -12.5 -18t-17.5 -7q-21 0 -38.5 17.5t-17.5 43.5
q0 29 19 48.5t47 19.5q27 0 47 -19.5t20 -48.5q0 -11 -4.5 -26.5t-17.5 -39.5z" />
    <glyph glyph-name="f_t" horiz-adv-x="646" 
d="M134 0v546q0 53 22.5 92.5t63.5 62.5t95 23l-1 -84q-43 0 -66.5 -25t-23.5 -69v-546h-90zM17 393v82h607v-82h-607zM417 0v674h90v-674h-90z" />
    <glyph glyph-name="r_f" horiz-adv-x="692" 
d="M66 0v475h90v-475h-90zM426 0v546q0 53 23.5 92.5t64 62.5t93.5 23q41 0 71 -14t56 -41l-59 -58q-14 14 -29.5 21.5t-38.5 7.5q-43 0 -66.5 -25t-23.5 -69v-546h-91zM156 268l-34 16q0 90 45 140.5t141 50.5h360v-82h-376q-66 0 -101 -29.5t-35 -95.5z" />
    <glyph glyph-name="r_t" horiz-adv-x="658" 
d="M66 0v475h90v-475h-90zM156 268l-34 16q0 90 45 140.5t141 50.5h328v-82h-344q-66 0 -101 -29.5t-35 -95.5zM429 0v674h91v-674h-91z" />
    <glyph glyph-name="t_f" horiz-adv-x="687" 
d="M139 0v674h90v-674h-90zM22 393v82h641v-82h-641zM422 0v546q0 53 23 92.5t64 62.5t94 23q41 0 71 -14t55 -41l-58 -58q-14 14 -30 21.5t-38 7.5q-44 0 -67.5 -25t-23.5 -69v-546h-90z" />
    <glyph glyph-name="one.ss01" horiz-adv-x="355" 
d="M183 0v628l28 66h64v-694h-92zM80 458l-53 52l184 184l6 -98z" />
    <glyph glyph-name="zero.tf" 
d="M296 -10q-75 0 -136.5 44t-98.5 125t-37 189q0 109 36.5 189t98 123.5t136.5 43.5t136.5 -43.5t98 -123.5t36.5 -190q0 -109 -36.5 -189.5t-97.5 -124t-136 -43.5zM295 79q52 0 91.5 30.5t62 90.5t22.5 148t-22.5 147.5t-62 90t-91.5 30.5t-91.5 -30.5t-62 -90
t-22.5 -147.5t22.5 -148t62.5 -90.5t91 -30.5z" />
    <glyph glyph-name="one.tf" 
d="M269 0v694h92v-694h-92zM81 610v84h266v-84h-266zM81 0v84h438v-84h-438z" />
    <glyph glyph-name="two.tf" 
d="M51 57l265 270q40 40 63 69.5t33 54.5t10 52q0 53 -35.5 83t-92.5 30q-53 0 -95.5 -24.5t-75.5 -79.5l-67 55q42 68 102 102.5t139 34.5q66 0 114.5 -25t75.5 -70t27 -105q0 -44 -11 -78t-38 -69t-76 -84l-215 -213zM51 0v57l79 27h401v-84h-480z" />
    <glyph glyph-name="three.tf" 
d="M273 -10q-71 0 -128 25.5t-97 74.5l64 64q25 -35 67 -55.5t92 -20.5q48 0 84 17.5t56 49.5t20 74q0 43 -20 74.5t-57 48.5t-88 17q-18 0 -36.5 -2.5t-35.5 -7.5l39 54q19 9 41.5 14t46.5 5q58 0 104.5 -26t73.5 -73t27 -109q0 -66 -32 -116.5t-89 -79t-132 -28.5z
M194 349v57l211 231l113 1l-219 -237zM82 610v84h436v-56l-82 -28h-354z" />
    <glyph glyph-name="four.tf" 
d="M31 239l251 455h106l-256 -455h-101zM31 181v58l40 26h489v-84h-529zM368 0v459h92v-459h-92z" />
    <glyph glyph-name="five.tf" 
d="M272 -10q-70 0 -128 25t-98 75l63 64q25 -35 66.5 -55.5t94.5 -20.5q49 0 86 18t58.5 50.5t21.5 77.5q0 46 -21 78t-56 48.5t-77 16.5t-77 -10t-66 -34l1 60q18 21 40.5 34t52 20t66.5 7q74 0 125.5 -30t79 -81t27.5 -114q0 -66 -33.5 -118t-92 -81.5t-133.5 -29.5z
M139 323l-51 51l32 320h86l-36 -329zM140 610l-20 84h375v-84h-355z" />
    <glyph glyph-name="six.tf" 
d="M295 -10q-69 0 -125 32t-88 86t-32 120q0 93 64 183l209 283h109l-212 -285l-34 -18q12 21 29 35.5t42.5 22.5t59.5 8q62 0 112.5 -29t81 -79.5t30.5 -117.5q0 -66 -33 -121t-89 -87.5t-124 -32.5zM295 78q43 0 77.5 20.5t54.5 55t20 77.5t-20 77.5t-54.5 54.5t-77.5 20
t-77.5 -20t-54 -54.5t-19.5 -77.5t19.5 -78t54 -55t77.5 -20z" />
    <glyph glyph-name="seven.tf" 
d="M174 0l270 639l93 -3l-265 -636h-98zM51 610v84h486v-58l-41 -26h-445z" />
    <glyph glyph-name="eight.tf" 
d="M294 -10q-70 0 -124 26t-84 72t-30 104q0 47 19 86t54.5 66.5t84.5 39.5l3 -27q-41 11 -69.5 35.5t-43.5 58t-15 71.5q0 54 26.5 95t73.5 64.5t105 23.5q60 0 106.5 -23.5t73.5 -64.5t27 -95q0 -38 -15 -71.5t-43.5 -58t-69.5 -35.5l3 27q49 -12 84 -39.5t54 -66.5
t19 -86q0 -58 -30 -104t-83.5 -72t-125.5 -26zM294 75q44 0 77.5 16.5t52.5 45.5t19 67q0 37 -19 66t-52.5 45.5t-77.5 16.5q-43 0 -76 -16.5t-52 -45.5t-19 -66q0 -38 19 -67t52 -45.5t76 -16.5zM294 405q52 0 85.5 31t33.5 80q0 48 -33.5 78.5t-85.5 30.5
q-51 0 -84.5 -30.5t-33.5 -78.5q0 -49 33.5 -80t84.5 -31z" />
    <glyph glyph-name="nine.tf" 
d="M158 0l213 285l34 18q-12 -20 -29.5 -35t-42.5 -23t-60 -8q-61 0 -112 29t-81.5 80.5t-30.5 116.5q0 67 33 121.5t89 87t124 32.5q70 0 125.5 -32t88 -85.5t32.5 -120.5q0 -92 -65 -183l-208 -283h-110zM295 311q43 0 77.5 20.5t54 55t19.5 76.5q0 44 -19.5 78.5
t-54 54.5t-77.5 20t-77 -20t-54.5 -54.5t-20.5 -78.5q0 -43 20.5 -77t55 -54.5t76.5 -20.5z" />
    <glyph glyph-name="ampersand.ss01" horiz-adv-x="637" 
d="M300 -10q-75 0 -130 26t-84.5 72t-29.5 104q0 71 41.5 122.5t114.5 70.5l2 -27q-39 9 -67 33t-43.5 58t-15.5 73q0 53 26.5 94.5t73.5 65t109 23.5q73 0 121 -30t74 -72l-64 -57q-23 32 -53 52.5t-79 20.5q-59 0 -90 -30.5t-31 -76.5q0 -29 14 -52.5t38 -37.5t54 -14v-78
q-38 0 -68.5 -16t-48 -44.5t-17.5 -66.5q0 -37 19 -66t53.5 -45.5t80.5 -16.5q47 0 81 16.5t53 45.5t19 66q0 34 -12.5 59.5t-34.5 41.5t-49 21l36 59q47 -13 80.5 -40t52 -66t18.5 -86q0 -58 -31 -104t-86 -72t-127 -26zM357 325v83h253v-83h-253z" />
    <glyph glyph-name="uni030C.alt" horiz-adv-x="0" 
d="M135 216l-52 28l63 119l31 8q-5 -11 -12.5 -18t-18.5 -7q-21 0 -38.5 17.5t-17.5 43.5q0 28 19.5 48t47.5 20q26 0 46 -20t20 -48q0 -11 -4.5 -26.5t-16.5 -40.5z" />
    <hkern u1="&#x20;" g2="T_l" k="16" />
    <hkern u1="&#x20;" g2="T_h" k="16" />
    <hkern u1="&#x20;" g2="T_T" k="16" />
    <hkern u1="&#x20;" u2="&#x1ef2;" k="28" />
    <hkern u1="&#x20;" u2="&#x21a;" k="16" />
    <hkern u1="&#x20;" u2="&#x1cd;" k="28" />
    <hkern u1="&#x20;" u2="&#x178;" k="28" />
    <hkern u1="&#x20;" u2="&#x176;" k="28" />
    <hkern u1="&#x20;" u2="&#x164;" k="16" />
    <hkern u1="&#x20;" u2="&#x104;" k="28" />
    <hkern u1="&#x20;" u2="&#x102;" k="28" />
    <hkern u1="&#x20;" u2="&#x100;" k="28" />
    <hkern u1="&#x20;" u2="&#xdd;" k="28" />
    <hkern u1="&#x20;" u2="&#xc6;" k="28" />
    <hkern u1="&#x20;" u2="&#xc5;" k="28" />
    <hkern u1="&#x20;" u2="&#xc4;" k="28" />
    <hkern u1="&#x20;" u2="&#xc3;" k="28" />
    <hkern u1="&#x20;" u2="&#xc2;" k="28" />
    <hkern u1="&#x20;" u2="&#xc1;" k="28" />
    <hkern u1="&#x20;" u2="&#xc0;" k="28" />
    <hkern u1="&#x20;" u2="Y" k="28" />
    <hkern u1="&#x20;" u2="T" k="16" />
    <hkern u1="&#x20;" u2="J" k="28" />
    <hkern u1="&#x20;" u2="A" k="28" />
    <hkern u1="&#x22;" u2="x" k="3" />
    <hkern u1="&#x22;" u2="w" k="3" />
    <hkern u1="&#x22;" u2="s" k="43" />
    <hkern u1="&#x22;" u2="S" k="28" />
    <hkern u1="&#x22;" u2="J" k="116" />
    <hkern u1="&#x22;" u2="&#x39;" k="3" />
    <hkern u1="&#x22;" u2="&#x38;" k="27" />
    <hkern u1="&#x22;" u2="&#x37;" k="-23" />
    <hkern u1="&#x22;" u2="&#x36;" k="55" />
    <hkern u1="&#x22;" u2="&#x34;" k="75" />
    <hkern u1="&#x22;" u2="&#x30;" k="34" />
    <hkern u1="&#x24;" u2="&#x37;" k="18" />
    <hkern u1="&#x26;" g2="o_u_t_f_i_t_t_e_r" k="20" />
    <hkern u1="&#x26;" g2="o_u_t_f_i_t_t_e_d" k="20" />
    <hkern u1="&#x26;" g2="o_u_t_f_i_t" k="20" />
    <hkern u1="&#x26;" g2="f_t" k="46" />
    <hkern u1="&#x26;" g2="f_j" k="46" />
    <hkern u1="&#x26;" g2="f_f" k="46" />
    <hkern u1="&#x26;" g2="t.ss01" k="46" />
    <hkern u1="&#x26;" g2="oe.ss01" k="16" />
    <hkern u1="&#x26;" g2="eogonek.ss01" k="16" />
    <hkern u1="&#x26;" g2="emacron.ss01" k="16" />
    <hkern u1="&#x26;" g2="egrave.ss01" k="16" />
    <hkern u1="&#x26;" g2="edotaccent.ss01" k="16" />
    <hkern u1="&#x26;" g2="edieresis.ss01" k="16" />
    <hkern u1="&#x26;" g2="ecircumflex.ss01" k="16" />
    <hkern u1="&#x26;" g2="ecaron.ss01" k="16" />
    <hkern u1="&#x26;" g2="eacute.ss01" k="16" />
    <hkern u1="&#x26;" g2="e.ss01" k="16" />
    <hkern u1="&#x26;" g2="ae.ss01" k="16" />
    <hkern u1="&#x26;" g2="T_l" k="94" />
    <hkern u1="&#x26;" g2="T_h" k="94" />
    <hkern u1="&#x26;" g2="T_T" k="94" />
    <hkern u1="&#x26;" g2="fl" k="46" />
    <hkern u1="&#x26;" g2="fi" k="46" />
    <hkern u1="&#x26;" u2="&#x1ef3;" k="60" />
    <hkern u1="&#x26;" u2="&#x1ef2;" k="86" />
    <hkern u1="&#x26;" u2="&#x21b;" k="46" />
    <hkern u1="&#x26;" u2="&#x21a;" k="94" />
    <hkern u1="&#x26;" u2="&#x1ce;" k="16" />
    <hkern u1="&#x26;" u2="&#x1cd;" k="88" />
    <hkern u1="&#x26;" u2="&#x178;" k="86" />
    <hkern u1="&#x26;" u2="&#x177;" k="60" />
    <hkern u1="&#x26;" u2="&#x176;" k="86" />
    <hkern u1="&#x26;" u2="&#x173;" k="20" />
    <hkern u1="&#x26;" u2="&#x172;" k="42" />
    <hkern u1="&#x26;" u2="&#x171;" k="20" />
    <hkern u1="&#x26;" u2="&#x170;" k="42" />
    <hkern u1="&#x26;" u2="&#x16f;" k="20" />
    <hkern u1="&#x26;" u2="&#x16e;" k="42" />
    <hkern u1="&#x26;" u2="&#x16d;" k="20" />
    <hkern u1="&#x26;" u2="&#x16c;" k="42" />
    <hkern u1="&#x26;" u2="&#x16b;" k="20" />
    <hkern u1="&#x26;" u2="&#x16a;" k="42" />
    <hkern u1="&#x26;" u2="&#x165;" k="46" />
    <hkern u1="&#x26;" u2="&#x164;" k="94" />
    <hkern u1="&#x26;" u2="&#x158;" k="28" />
    <hkern u1="&#x26;" u2="&#x156;" k="28" />
    <hkern u1="&#x26;" u2="&#x154;" k="28" />
    <hkern u1="&#x26;" u2="&#x153;" k="16" />
    <hkern u1="&#x26;" u2="&#x152;" k="72" />
    <hkern u1="&#x26;" u2="&#x151;" k="16" />
    <hkern u1="&#x26;" u2="&#x150;" k="72" />
    <hkern u1="&#x26;" u2="&#x14d;" k="16" />
    <hkern u1="&#x26;" u2="&#x14c;" k="72" />
    <hkern u1="&#x26;" u2="&#x14a;" k="28" />
    <hkern u1="&#x26;" u2="&#x147;" k="28" />
    <hkern u1="&#x26;" u2="&#x145;" k="28" />
    <hkern u1="&#x26;" u2="&#x143;" k="28" />
    <hkern u1="&#x26;" u2="&#x141;" k="28" />
    <hkern u1="&#x26;" u2="&#x13d;" k="28" />
    <hkern u1="&#x26;" u2="&#x13b;" k="28" />
    <hkern u1="&#x26;" u2="&#x139;" k="28" />
    <hkern u1="&#x26;" u2="&#x136;" k="28" />
    <hkern u1="&#x26;" u2="&#x130;" k="28" />
    <hkern u1="&#x26;" u2="&#x12e;" k="28" />
    <hkern u1="&#x26;" u2="&#x12a;" k="28" />
    <hkern u1="&#x26;" u2="&#x123;" k="16" />
    <hkern u1="&#x26;" u2="&#x122;" k="72" />
    <hkern u1="&#x26;" u2="&#x121;" k="16" />
    <hkern u1="&#x26;" u2="&#x120;" k="72" />
    <hkern u1="&#x26;" u2="&#x11f;" k="16" />
    <hkern u1="&#x26;" u2="&#x11e;" k="72" />
    <hkern u1="&#x26;" u2="&#x11b;" k="16" />
    <hkern u1="&#x26;" u2="&#x11a;" k="28" />
    <hkern u1="&#x26;" u2="&#x119;" k="16" />
    <hkern u1="&#x26;" u2="&#x118;" k="28" />
    <hkern u1="&#x26;" u2="&#x117;" k="16" />
    <hkern u1="&#x26;" u2="&#x116;" k="28" />
    <hkern u1="&#x26;" u2="&#x113;" k="16" />
    <hkern u1="&#x26;" u2="&#x112;" k="28" />
    <hkern u1="&#x26;" u2="&#x111;" k="16" />
    <hkern u1="&#x26;" u2="&#x110;" k="28" />
    <hkern u1="&#x26;" u2="&#x10f;" k="16" />
    <hkern u1="&#x26;" u2="&#x10e;" k="28" />
    <hkern u1="&#x26;" u2="&#x10d;" k="16" />
    <hkern u1="&#x26;" u2="&#x10c;" k="72" />
    <hkern u1="&#x26;" u2="&#x10b;" k="16" />
    <hkern u1="&#x26;" u2="&#x10a;" k="72" />
    <hkern u1="&#x26;" u2="&#x107;" k="16" />
    <hkern u1="&#x26;" u2="&#x106;" k="72" />
    <hkern u1="&#x26;" u2="&#x105;" k="16" />
    <hkern u1="&#x26;" u2="&#x104;" k="88" />
    <hkern u1="&#x26;" u2="&#x103;" k="16" />
    <hkern u1="&#x26;" u2="&#x102;" k="88" />
    <hkern u1="&#x26;" u2="&#x101;" k="16" />
    <hkern u1="&#x26;" u2="&#x100;" k="88" />
    <hkern u1="&#x26;" u2="&#xff;" k="60" />
    <hkern u1="&#x26;" u2="&#xfd;" k="60" />
    <hkern u1="&#x26;" u2="&#xfc;" k="20" />
    <hkern u1="&#x26;" u2="&#xfb;" k="20" />
    <hkern u1="&#x26;" u2="&#xfa;" k="20" />
    <hkern u1="&#x26;" u2="&#xf9;" k="20" />
    <hkern u1="&#x26;" u2="&#xf8;" k="16" />
    <hkern u1="&#x26;" u2="&#xf6;" k="16" />
    <hkern u1="&#x26;" u2="&#xf5;" k="16" />
    <hkern u1="&#x26;" u2="&#xf4;" k="16" />
    <hkern u1="&#x26;" u2="&#xf3;" k="16" />
    <hkern u1="&#x26;" u2="&#xf2;" k="16" />
    <hkern u1="&#x26;" u2="&#xeb;" k="16" />
    <hkern u1="&#x26;" u2="&#xea;" k="16" />
    <hkern u1="&#x26;" u2="&#xe9;" k="16" />
    <hkern u1="&#x26;" u2="&#xe8;" k="16" />
    <hkern u1="&#x26;" u2="&#xe7;" k="16" />
    <hkern u1="&#x26;" u2="&#xe6;" k="16" />
    <hkern u1="&#x26;" u2="&#xe5;" k="16" />
    <hkern u1="&#x26;" u2="&#xe4;" k="16" />
    <hkern u1="&#x26;" u2="&#xe3;" k="16" />
    <hkern u1="&#x26;" u2="&#xe2;" k="16" />
    <hkern u1="&#x26;" u2="&#xe1;" k="16" />
    <hkern u1="&#x26;" u2="&#xe0;" k="16" />
    <hkern u1="&#x26;" u2="&#xde;" k="28" />
    <hkern u1="&#x26;" u2="&#xdd;" k="86" />
    <hkern u1="&#x26;" u2="&#xdc;" k="42" />
    <hkern u1="&#x26;" u2="&#xdb;" k="42" />
    <hkern u1="&#x26;" u2="&#xda;" k="42" />
    <hkern u1="&#x26;" u2="&#xd9;" k="42" />
    <hkern u1="&#x26;" u2="&#xd8;" k="72" />
    <hkern u1="&#x26;" u2="&#xd6;" k="72" />
    <hkern u1="&#x26;" u2="&#xd5;" k="72" />
    <hkern u1="&#x26;" u2="&#xd4;" k="72" />
    <hkern u1="&#x26;" u2="&#xd3;" k="72" />
    <hkern u1="&#x26;" u2="&#xd2;" k="72" />
    <hkern u1="&#x26;" u2="&#xd1;" k="28" />
    <hkern u1="&#x26;" u2="&#xd0;" k="28" />
    <hkern u1="&#x26;" u2="&#xcf;" k="28" />
    <hkern u1="&#x26;" u2="&#xce;" k="28" />
    <hkern u1="&#x26;" u2="&#xcd;" k="28" />
    <hkern u1="&#x26;" u2="&#xcc;" k="28" />
    <hkern u1="&#x26;" u2="&#xcb;" k="28" />
    <hkern u1="&#x26;" u2="&#xca;" k="28" />
    <hkern u1="&#x26;" u2="&#xc9;" k="28" />
    <hkern u1="&#x26;" u2="&#xc8;" k="28" />
    <hkern u1="&#x26;" u2="&#xc7;" k="72" />
    <hkern u1="&#x26;" u2="&#xc6;" k="88" />
    <hkern u1="&#x26;" u2="&#xc5;" k="88" />
    <hkern u1="&#x26;" u2="&#xc4;" k="88" />
    <hkern u1="&#x26;" u2="&#xc3;" k="88" />
    <hkern u1="&#x26;" u2="&#xc2;" k="88" />
    <hkern u1="&#x26;" u2="&#xc1;" k="88" />
    <hkern u1="&#x26;" u2="&#xc0;" k="88" />
    <hkern u1="&#x26;" u2="&#x7c;" k="28" />
    <hkern u1="&#x26;" u2="y" k="60" />
    <hkern u1="&#x26;" u2="w" k="39" />
    <hkern u1="&#x26;" u2="v" k="60" />
    <hkern u1="&#x26;" u2="u" k="20" />
    <hkern u1="&#x26;" u2="t" k="46" />
    <hkern u1="&#x26;" u2="s" k="16" />
    <hkern u1="&#x26;" u2="q" k="16" />
    <hkern u1="&#x26;" u2="o" k="16" />
    <hkern u1="&#x26;" u2="g" k="16" />
    <hkern u1="&#x26;" u2="f" k="46" />
    <hkern u1="&#x26;" u2="e" k="16" />
    <hkern u1="&#x26;" u2="d" k="16" />
    <hkern u1="&#x26;" u2="c" k="16" />
    <hkern u1="&#x26;" u2="a" k="16" />
    <hkern u1="&#x26;" u2="Z" k="52" />
    <hkern u1="&#x26;" u2="Y" k="86" />
    <hkern u1="&#x26;" u2="X" k="68" />
    <hkern u1="&#x26;" u2="W" k="64" />
    <hkern u1="&#x26;" u2="V" k="73" />
    <hkern u1="&#x26;" u2="U" k="42" />
    <hkern u1="&#x26;" u2="T" k="94" />
    <hkern u1="&#x26;" u2="S" k="26" />
    <hkern u1="&#x26;" u2="R" k="28" />
    <hkern u1="&#x26;" u2="Q" k="72" />
    <hkern u1="&#x26;" u2="P" k="28" />
    <hkern u1="&#x26;" u2="O" k="72" />
    <hkern u1="&#x26;" u2="N" k="28" />
    <hkern u1="&#x26;" u2="M" k="28" />
    <hkern u1="&#x26;" u2="L" k="28" />
    <hkern u1="&#x26;" u2="K" k="28" />
    <hkern u1="&#x26;" u2="I" k="28" />
    <hkern u1="&#x26;" u2="H" k="28" />
    <hkern u1="&#x26;" u2="G" k="72" />
    <hkern u1="&#x26;" u2="F" k="28" />
    <hkern u1="&#x26;" u2="E" k="28" />
    <hkern u1="&#x26;" u2="D" k="28" />
    <hkern u1="&#x26;" u2="C" k="72" />
    <hkern u1="&#x26;" u2="B" k="28" />
    <hkern u1="&#x26;" u2="A" k="88" />
    <hkern u1="&#x26;" u2="&#x40;" k="16" />
    <hkern u1="&#x26;" u2="&#x20;" k="38" />
    <hkern u1="&#x27;" u2="x" k="3" />
    <hkern u1="&#x27;" u2="w" k="3" />
    <hkern u1="&#x27;" u2="s" k="43" />
    <hkern u1="&#x27;" u2="S" k="28" />
    <hkern u1="&#x27;" u2="J" k="116" />
    <hkern u1="&#x27;" u2="&#x39;" k="3" />
    <hkern u1="&#x27;" u2="&#x38;" k="27" />
    <hkern u1="&#x27;" u2="&#x37;" k="-23" />
    <hkern u1="&#x27;" u2="&#x36;" k="55" />
    <hkern u1="&#x27;" u2="&#x34;" k="75" />
    <hkern u1="&#x27;" u2="&#x30;" k="34" />
    <hkern u1="&#x28;" u2="&#x1ef3;" k="36" />
    <hkern u1="&#x28;" u2="&#x177;" k="36" />
    <hkern u1="&#x28;" u2="&#x152;" k="12" />
    <hkern u1="&#x28;" u2="&#x150;" k="12" />
    <hkern u1="&#x28;" u2="&#x14c;" k="12" />
    <hkern u1="&#x28;" u2="&#x122;" k="12" />
    <hkern u1="&#x28;" u2="&#x120;" k="12" />
    <hkern u1="&#x28;" u2="&#x11e;" k="12" />
    <hkern u1="&#x28;" u2="&#x10c;" k="12" />
    <hkern u1="&#x28;" u2="&#x10a;" k="12" />
    <hkern u1="&#x28;" u2="&#x106;" k="12" />
    <hkern u1="&#x28;" u2="&#xff;" k="36" />
    <hkern u1="&#x28;" u2="&#xfd;" k="36" />
    <hkern u1="&#x28;" u2="&#xd8;" k="12" />
    <hkern u1="&#x28;" u2="&#xd6;" k="12" />
    <hkern u1="&#x28;" u2="&#xd5;" k="12" />
    <hkern u1="&#x28;" u2="&#xd4;" k="12" />
    <hkern u1="&#x28;" u2="&#xd3;" k="12" />
    <hkern u1="&#x28;" u2="&#xd2;" k="12" />
    <hkern u1="&#x28;" u2="&#xc7;" k="12" />
    <hkern u1="&#x28;" u2="y" k="36" />
    <hkern u1="&#x28;" u2="w" k="28" />
    <hkern u1="&#x28;" u2="v" k="36" />
    <hkern u1="&#x28;" u2="j" k="-98" />
    <hkern u1="&#x28;" u2="Q" k="12" />
    <hkern u1="&#x28;" u2="O" k="12" />
    <hkern u1="&#x28;" u2="J" k="16" />
    <hkern u1="&#x28;" u2="G" k="12" />
    <hkern u1="&#x28;" u2="C" k="12" />
    <hkern u1="&#x2c;" u2="w" k="33" />
    <hkern u1="&#x2c;" u2="j" k="-36" />
    <hkern u1="&#x2c;" u2="W" k="56" />
    <hkern u1="&#x2c;" u2="V" k="79" />
    <hkern u1="&#x2c;" u2="&#x39;" k="52" />
    <hkern u1="&#x2c;" u2="&#x37;" k="61" />
    <hkern u1="&#x2c;" u2="&#x36;" k="28" />
    <hkern u1="&#x2c;" u2="&#x34;" k="32" />
    <hkern u1="&#x2c;" u2="&#x31;" k="82" />
    <hkern u1="&#x2c;" u2="&#x30;" k="27" />
    <hkern u1="&#x2c;" u2="&#x2a;" k="68" />
    <hkern u1="&#x2d;" g2="f_t" k="19" />
    <hkern u1="&#x2d;" g2="f_j" k="19" />
    <hkern u1="&#x2d;" g2="f_f" k="19" />
    <hkern u1="&#x2d;" g2="t.ss01" k="19" />
    <hkern u1="&#x2d;" g2="oe.ss01" k="-16" />
    <hkern u1="&#x2d;" g2="eogonek.ss01" k="-16" />
    <hkern u1="&#x2d;" g2="emacron.ss01" k="-16" />
    <hkern u1="&#x2d;" g2="egrave.ss01" k="-16" />
    <hkern u1="&#x2d;" g2="edotaccent.ss01" k="-16" />
    <hkern u1="&#x2d;" g2="edieresis.ss01" k="-16" />
    <hkern u1="&#x2d;" g2="ecircumflex.ss01" k="-16" />
    <hkern u1="&#x2d;" g2="ecaron.ss01" k="-16" />
    <hkern u1="&#x2d;" g2="eacute.ss01" k="-16" />
    <hkern u1="&#x2d;" g2="e.ss01" k="-16" />
    <hkern u1="&#x2d;" g2="ae.ss01" k="-16" />
    <hkern u1="&#x2d;" g2="T_l" k="78" />
    <hkern u1="&#x2d;" g2="T_h" k="78" />
    <hkern u1="&#x2d;" g2="T_T" k="78" />
    <hkern u1="&#x2d;" g2="fl" k="19" />
    <hkern u1="&#x2d;" g2="fi" k="19" />
    <hkern u1="&#x2d;" u2="&#x1ef3;" k="26" />
    <hkern u1="&#x2d;" u2="&#x1ef2;" k="85" />
    <hkern u1="&#x2d;" u2="&#x21b;" k="19" />
    <hkern u1="&#x2d;" u2="&#x21a;" k="78" />
    <hkern u1="&#x2d;" u2="&#x1ce;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x1cd;" k="40" />
    <hkern u1="&#x2d;" u2="&#x178;" k="85" />
    <hkern u1="&#x2d;" u2="&#x177;" k="26" />
    <hkern u1="&#x2d;" u2="&#x176;" k="85" />
    <hkern u1="&#x2d;" u2="&#x165;" k="19" />
    <hkern u1="&#x2d;" u2="&#x164;" k="78" />
    <hkern u1="&#x2d;" u2="&#x153;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x152;" k="-13" />
    <hkern u1="&#x2d;" u2="&#x151;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x150;" k="-13" />
    <hkern u1="&#x2d;" u2="&#x14d;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x14c;" k="-13" />
    <hkern u1="&#x2d;" u2="&#x123;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x122;" k="-13" />
    <hkern u1="&#x2d;" u2="&#x121;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x120;" k="-13" />
    <hkern u1="&#x2d;" u2="&#x11f;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x11e;" k="-13" />
    <hkern u1="&#x2d;" u2="&#x11b;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x119;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x117;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x113;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x111;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x10f;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x10d;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x10c;" k="-13" />
    <hkern u1="&#x2d;" u2="&#x10b;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x10a;" k="-13" />
    <hkern u1="&#x2d;" u2="&#x107;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x106;" k="-13" />
    <hkern u1="&#x2d;" u2="&#x105;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x104;" k="40" />
    <hkern u1="&#x2d;" u2="&#x103;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x102;" k="40" />
    <hkern u1="&#x2d;" u2="&#x101;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x100;" k="40" />
    <hkern u1="&#x2d;" u2="&#xff;" k="26" />
    <hkern u1="&#x2d;" u2="&#xfd;" k="26" />
    <hkern u1="&#x2d;" u2="&#xf8;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xf6;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xf5;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xf4;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xf3;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xf2;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xeb;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xea;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xe9;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xe8;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xe7;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xe6;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xe5;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xe4;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xe3;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xe2;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xe1;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xe0;" k="-16" />
    <hkern u1="&#x2d;" u2="&#xdd;" k="85" />
    <hkern u1="&#x2d;" u2="&#xd8;" k="-13" />
    <hkern u1="&#x2d;" u2="&#xd6;" k="-13" />
    <hkern u1="&#x2d;" u2="&#xd5;" k="-13" />
    <hkern u1="&#x2d;" u2="&#xd4;" k="-13" />
    <hkern u1="&#x2d;" u2="&#xd3;" k="-13" />
    <hkern u1="&#x2d;" u2="&#xd2;" k="-13" />
    <hkern u1="&#x2d;" u2="&#xc7;" k="-13" />
    <hkern u1="&#x2d;" u2="&#xc6;" k="40" />
    <hkern u1="&#x2d;" u2="&#xc5;" k="40" />
    <hkern u1="&#x2d;" u2="&#xc4;" k="40" />
    <hkern u1="&#x2d;" u2="&#xc3;" k="40" />
    <hkern u1="&#x2d;" u2="&#xc2;" k="40" />
    <hkern u1="&#x2d;" u2="&#xc1;" k="40" />
    <hkern u1="&#x2d;" u2="&#xc0;" k="40" />
    <hkern u1="&#x2d;" u2="z" k="23" />
    <hkern u1="&#x2d;" u2="y" k="26" />
    <hkern u1="&#x2d;" u2="x" k="27" />
    <hkern u1="&#x2d;" u2="v" k="26" />
    <hkern u1="&#x2d;" u2="t" k="19" />
    <hkern u1="&#x2d;" u2="q" k="-16" />
    <hkern u1="&#x2d;" u2="o" k="-16" />
    <hkern u1="&#x2d;" u2="g" k="-16" />
    <hkern u1="&#x2d;" u2="f" k="19" />
    <hkern u1="&#x2d;" u2="e" k="-16" />
    <hkern u1="&#x2d;" u2="d" k="-16" />
    <hkern u1="&#x2d;" u2="c" k="-16" />
    <hkern u1="&#x2d;" u2="a" k="-16" />
    <hkern u1="&#x2d;" u2="Z" k="31" />
    <hkern u1="&#x2d;" u2="Y" k="85" />
    <hkern u1="&#x2d;" u2="X" k="61" />
    <hkern u1="&#x2d;" u2="W" k="43" />
    <hkern u1="&#x2d;" u2="V" k="68" />
    <hkern u1="&#x2d;" u2="T" k="78" />
    <hkern u1="&#x2d;" u2="S" k="30" />
    <hkern u1="&#x2d;" u2="Q" k="-13" />
    <hkern u1="&#x2d;" u2="O" k="-13" />
    <hkern u1="&#x2d;" u2="J" k="57" />
    <hkern u1="&#x2d;" u2="G" k="-13" />
    <hkern u1="&#x2d;" u2="C" k="-13" />
    <hkern u1="&#x2d;" u2="A" k="40" />
    <hkern u1="&#x2d;" u2="&#x40;" k="-16" />
    <hkern u1="&#x2d;" u2="&#x37;" k="77" />
    <hkern u1="&#x2d;" u2="&#x33;" k="56" />
    <hkern u1="&#x2d;" u2="&#x32;" k="28" />
    <hkern u1="&#x2d;" u2="&#x31;" k="54" />
    <hkern u1="&#x2e;" u2="w" k="33" />
    <hkern u1="&#x2e;" u2="j" k="-36" />
    <hkern u1="&#x2e;" u2="W" k="56" />
    <hkern u1="&#x2e;" u2="V" k="79" />
    <hkern u1="&#x2e;" u2="&#x39;" k="52" />
    <hkern u1="&#x2e;" u2="&#x37;" k="61" />
    <hkern u1="&#x2e;" u2="&#x36;" k="28" />
    <hkern u1="&#x2e;" u2="&#x34;" k="32" />
    <hkern u1="&#x2e;" u2="&#x31;" k="82" />
    <hkern u1="&#x2e;" u2="&#x30;" k="27" />
    <hkern u1="&#x2e;" u2="&#x2a;" k="68" />
    <hkern u1="&#x2f;" g2="r_t" k="42" />
    <hkern u1="&#x2f;" g2="r_f" k="42" />
    <hkern u1="&#x2f;" g2="o_u_t_f_i_t_t_e_r" k="24" />
    <hkern u1="&#x2f;" g2="o_u_t_f_i_t_t_e_d" k="24" />
    <hkern u1="&#x2f;" g2="o_u_t_f_i_t" k="24" />
    <hkern u1="&#x2f;" g2="f_t" k="18" />
    <hkern u1="&#x2f;" g2="f_j" k="18" />
    <hkern u1="&#x2f;" g2="f_f" k="18" />
    <hkern u1="&#x2f;" g2="t.ss01" k="18" />
    <hkern u1="&#x2f;" g2="oe.ss01" k="52" />
    <hkern u1="&#x2f;" g2="eng.ss01" k="42" />
    <hkern u1="&#x2f;" g2="ntilde.ss01" k="42" />
    <hkern u1="&#x2f;" g2="uni0146.ss01" k="42" />
    <hkern u1="&#x2f;" g2="ncaron.ss01" k="42" />
    <hkern u1="&#x2f;" g2="nacute.ss01" k="42" />
    <hkern u1="&#x2f;" g2="n.ss01" k="42" />
    <hkern u1="&#x2f;" g2="m.ss01" k="42" />
    <hkern u1="&#x2f;" g2="eogonek.ss01" k="52" />
    <hkern u1="&#x2f;" g2="emacron.ss01" k="52" />
    <hkern u1="&#x2f;" g2="egrave.ss01" k="52" />
    <hkern u1="&#x2f;" g2="edotaccent.ss01" k="52" />
    <hkern u1="&#x2f;" g2="edieresis.ss01" k="52" />
    <hkern u1="&#x2f;" g2="ecircumflex.ss01" k="52" />
    <hkern u1="&#x2f;" g2="ecaron.ss01" k="52" />
    <hkern u1="&#x2f;" g2="eacute.ss01" k="52" />
    <hkern u1="&#x2f;" g2="e.ss01" k="52" />
    <hkern u1="&#x2f;" g2="ae.ss01" k="52" />
    <hkern u1="&#x2f;" g2="fl" k="18" />
    <hkern u1="&#x2f;" g2="fi" k="18" />
    <hkern u1="&#x2f;" u2="&#x1ef3;" k="30" />
    <hkern u1="&#x2f;" u2="&#x21b;" k="18" />
    <hkern u1="&#x2f;" u2="&#x1ce;" k="52" />
    <hkern u1="&#x2f;" u2="&#x1cd;" k="58" />
    <hkern u1="&#x2f;" u2="&#x177;" k="30" />
    <hkern u1="&#x2f;" u2="&#x173;" k="24" />
    <hkern u1="&#x2f;" u2="&#x171;" k="24" />
    <hkern u1="&#x2f;" u2="&#x16f;" k="24" />
    <hkern u1="&#x2f;" u2="&#x16d;" k="24" />
    <hkern u1="&#x2f;" u2="&#x16b;" k="24" />
    <hkern u1="&#x2f;" u2="&#x165;" k="18" />
    <hkern u1="&#x2f;" u2="&#x159;" k="42" />
    <hkern u1="&#x2f;" u2="&#x158;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x157;" k="42" />
    <hkern u1="&#x2f;" u2="&#x156;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x155;" k="42" />
    <hkern u1="&#x2f;" u2="&#x154;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x153;" k="52" />
    <hkern u1="&#x2f;" u2="&#x152;" k="27" />
    <hkern u1="&#x2f;" u2="&#x151;" k="52" />
    <hkern u1="&#x2f;" u2="&#x150;" k="27" />
    <hkern u1="&#x2f;" u2="&#x14d;" k="52" />
    <hkern u1="&#x2f;" u2="&#x14c;" k="27" />
    <hkern u1="&#x2f;" u2="&#x14b;" k="42" />
    <hkern u1="&#x2f;" u2="&#x14a;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x148;" k="42" />
    <hkern u1="&#x2f;" u2="&#x147;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x146;" k="42" />
    <hkern u1="&#x2f;" u2="&#x145;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x144;" k="42" />
    <hkern u1="&#x2f;" u2="&#x143;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x141;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x13d;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x13b;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x139;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x136;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x130;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x12e;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x12a;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x123;" k="52" />
    <hkern u1="&#x2f;" u2="&#x122;" k="27" />
    <hkern u1="&#x2f;" u2="&#x121;" k="52" />
    <hkern u1="&#x2f;" u2="&#x120;" k="27" />
    <hkern u1="&#x2f;" u2="&#x11f;" k="52" />
    <hkern u1="&#x2f;" u2="&#x11e;" k="27" />
    <hkern u1="&#x2f;" u2="&#x11b;" k="52" />
    <hkern u1="&#x2f;" u2="&#x11a;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x119;" k="52" />
    <hkern u1="&#x2f;" u2="&#x118;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x117;" k="52" />
    <hkern u1="&#x2f;" u2="&#x116;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x113;" k="52" />
    <hkern u1="&#x2f;" u2="&#x112;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x111;" k="52" />
    <hkern u1="&#x2f;" u2="&#x110;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x10f;" k="52" />
    <hkern u1="&#x2f;" u2="&#x10e;" k="-14" />
    <hkern u1="&#x2f;" u2="&#x10d;" k="52" />
    <hkern u1="&#x2f;" u2="&#x10c;" k="27" />
    <hkern u1="&#x2f;" u2="&#x10b;" k="52" />
    <hkern u1="&#x2f;" u2="&#x10a;" k="27" />
    <hkern u1="&#x2f;" u2="&#x107;" k="52" />
    <hkern u1="&#x2f;" u2="&#x106;" k="27" />
    <hkern u1="&#x2f;" u2="&#x105;" k="52" />
    <hkern u1="&#x2f;" u2="&#x104;" k="58" />
    <hkern u1="&#x2f;" u2="&#x103;" k="52" />
    <hkern u1="&#x2f;" u2="&#x102;" k="58" />
    <hkern u1="&#x2f;" u2="&#x101;" k="52" />
    <hkern u1="&#x2f;" u2="&#x100;" k="58" />
    <hkern u1="&#x2f;" u2="&#xff;" k="30" />
    <hkern u1="&#x2f;" u2="&#xfe;" k="42" />
    <hkern u1="&#x2f;" u2="&#xfd;" k="30" />
    <hkern u1="&#x2f;" u2="&#xfc;" k="24" />
    <hkern u1="&#x2f;" u2="&#xfb;" k="24" />
    <hkern u1="&#x2f;" u2="&#xfa;" k="24" />
    <hkern u1="&#x2f;" u2="&#xf9;" k="24" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="52" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="52" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="52" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="52" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="52" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="52" />
    <hkern u1="&#x2f;" u2="&#xf1;" k="42" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="52" />
    <hkern u1="&#x2f;" u2="&#xea;" k="52" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="52" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="52" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="52" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="52" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="52" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="52" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="52" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="52" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="52" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="52" />
    <hkern u1="&#x2f;" u2="&#xde;" k="-14" />
    <hkern u1="&#x2f;" u2="&#xd8;" k="27" />
    <hkern u1="&#x2f;" u2="&#xd6;" k="27" />
    <hkern u1="&#x2f;" u2="&#xd5;" k="27" />
    <hkern u1="&#x2f;" u2="&#xd4;" k="27" />
    <hkern u1="&#x2f;" u2="&#xd3;" k="27" />
    <hkern u1="&#x2f;" u2="&#xd2;" k="27" />
    <hkern u1="&#x2f;" u2="&#xd1;" k="-14" />
    <hkern u1="&#x2f;" u2="&#xd0;" k="-14" />
    <hkern u1="&#x2f;" u2="&#xcf;" k="-14" />
    <hkern u1="&#x2f;" u2="&#xce;" k="-14" />
    <hkern u1="&#x2f;" u2="&#xcd;" k="-14" />
    <hkern u1="&#x2f;" u2="&#xcc;" k="-14" />
    <hkern u1="&#x2f;" u2="&#xcb;" k="-14" />
    <hkern u1="&#x2f;" u2="&#xca;" k="-14" />
    <hkern u1="&#x2f;" u2="&#xc9;" k="-14" />
    <hkern u1="&#x2f;" u2="&#xc8;" k="-14" />
    <hkern u1="&#x2f;" u2="&#xc7;" k="27" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="58" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="58" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="58" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="58" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="58" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="58" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="58" />
    <hkern u1="&#x2f;" u2="&#x7c;" k="-14" />
    <hkern u1="&#x2f;" u2="z" k="28" />
    <hkern u1="&#x2f;" u2="y" k="30" />
    <hkern u1="&#x2f;" u2="w" k="30" />
    <hkern u1="&#x2f;" u2="v" k="30" />
    <hkern u1="&#x2f;" u2="u" k="24" />
    <hkern u1="&#x2f;" u2="t" k="18" />
    <hkern u1="&#x2f;" u2="s" k="43" />
    <hkern u1="&#x2f;" u2="r" k="42" />
    <hkern u1="&#x2f;" u2="q" k="52" />
    <hkern u1="&#x2f;" u2="p" k="42" />
    <hkern u1="&#x2f;" u2="o" k="52" />
    <hkern u1="&#x2f;" u2="n" k="42" />
    <hkern u1="&#x2f;" u2="m" k="42" />
    <hkern u1="&#x2f;" u2="g" k="52" />
    <hkern u1="&#x2f;" u2="f" k="18" />
    <hkern u1="&#x2f;" u2="e" k="52" />
    <hkern u1="&#x2f;" u2="d" k="52" />
    <hkern u1="&#x2f;" u2="c" k="52" />
    <hkern u1="&#x2f;" u2="a" k="52" />
    <hkern u1="&#x2f;" u2="R" k="-14" />
    <hkern u1="&#x2f;" u2="Q" k="27" />
    <hkern u1="&#x2f;" u2="P" k="-14" />
    <hkern u1="&#x2f;" u2="O" k="27" />
    <hkern u1="&#x2f;" u2="N" k="-14" />
    <hkern u1="&#x2f;" u2="M" k="-14" />
    <hkern u1="&#x2f;" u2="L" k="-14" />
    <hkern u1="&#x2f;" u2="K" k="-14" />
    <hkern u1="&#x2f;" u2="J" k="78" />
    <hkern u1="&#x2f;" u2="I" k="-14" />
    <hkern u1="&#x2f;" u2="H" k="-14" />
    <hkern u1="&#x2f;" u2="G" k="27" />
    <hkern u1="&#x2f;" u2="F" k="-14" />
    <hkern u1="&#x2f;" u2="E" k="-14" />
    <hkern u1="&#x2f;" u2="D" k="-14" />
    <hkern u1="&#x2f;" u2="C" k="27" />
    <hkern u1="&#x2f;" u2="B" k="-14" />
    <hkern u1="&#x2f;" u2="A" k="58" />
    <hkern u1="&#x2f;" u2="&#x40;" k="52" />
    <hkern u1="&#x30;" u2="&#x37;" k="38" />
    <hkern u1="&#x30;" u2="&#x32;" k="2" />
    <hkern u1="&#x30;" u2="&#x2e;" k="27" />
    <hkern u1="&#x30;" u2="&#x2c;" k="27" />
    <hkern u1="&#x30;" u2="&#x27;" k="34" />
    <hkern u1="&#x30;" u2="&#x22;" k="34" />
    <hkern u1="&#x31;" u2="&#x37;" k="7" />
    <hkern u1="&#x32;" u2="&#x37;" k="26" />
    <hkern u1="&#x32;" u2="&#x2d;" k="32" />
    <hkern u1="&#x33;" u2="&#x37;" k="20" />
    <hkern u1="&#x33;" u2="&#x2e;" k="28" />
    <hkern u1="&#x33;" u2="&#x2c;" k="28" />
    <hkern u1="&#x34;" u2="&#x39;" k="25" />
    <hkern u1="&#x34;" u2="&#x37;" k="49" />
    <hkern u1="&#x34;" u2="&#x35;" k="14" />
    <hkern u1="&#x34;" u2="&#x34;" k="20" />
    <hkern u1="&#x34;" u2="&#x32;" k="13" />
    <hkern u1="&#x34;" u2="&#x31;" k="39" />
    <hkern u1="&#x34;" u2="&#x2e;" k="41" />
    <hkern u1="&#x34;" u2="&#x2c;" k="41" />
    <hkern u1="&#x34;" u2="&#x27;" k="62" />
    <hkern u1="&#x34;" u2="&#x22;" k="62" />
    <hkern u1="&#x35;" u2="&#x37;" k="31" />
    <hkern u1="&#x35;" u2="&#x2e;" k="28" />
    <hkern u1="&#x35;" u2="&#x2c;" k="28" />
    <hkern u1="&#x36;" u2="&#x39;" k="20" />
    <hkern u1="&#x36;" u2="&#x37;" k="33" />
    <hkern u1="&#x36;" u2="&#x33;" k="12" />
    <hkern u1="&#x36;" u2="&#x31;" k="25" />
    <hkern u1="&#x36;" u2="&#x27;" k="55" />
    <hkern u1="&#x36;" u2="&#x22;" k="55" />
    <hkern u1="&#x37;" u2="&#xa2;" k="18" />
    <hkern u1="&#x37;" u2="&#x39;" k="18" />
    <hkern u1="&#x37;" u2="&#x38;" k="22" />
    <hkern u1="&#x37;" u2="&#x37;" k="15" />
    <hkern u1="&#x37;" u2="&#x36;" k="35" />
    <hkern u1="&#x37;" u2="&#x34;" k="39" />
    <hkern u1="&#x37;" u2="&#x33;" k="21" />
    <hkern u1="&#x37;" u2="&#x32;" k="10" />
    <hkern u1="&#x37;" u2="&#x30;" k="17" />
    <hkern u1="&#x37;" u2="&#x2e;" k="68" />
    <hkern u1="&#x37;" u2="&#x2d;" k="53" />
    <hkern u1="&#x37;" u2="&#x2c;" k="68" />
    <hkern u1="&#x37;" u2="&#x27;" k="-23" />
    <hkern u1="&#x37;" u2="&#x22;" k="-23" />
    <hkern u1="&#x38;" u2="&#x39;" k="12" />
    <hkern u1="&#x38;" u2="&#x37;" k="29" />
    <hkern u1="&#x38;" u2="&#x33;" k="19" />
    <hkern u1="&#x38;" u2="&#x32;" k="9" />
    <hkern u1="&#x38;" u2="&#x31;" k="18" />
    <hkern u1="&#x38;" u2="&#x27;" k="27" />
    <hkern u1="&#x38;" u2="&#x22;" k="27" />
    <hkern u1="&#x39;" u2="&#x38;" k="17" />
    <hkern u1="&#x39;" u2="&#x37;" k="33" />
    <hkern u1="&#x39;" u2="&#x36;" k="29" />
    <hkern u1="&#x39;" u2="&#x34;" k="19" />
    <hkern u1="&#x39;" u2="&#x33;" k="9" />
    <hkern u1="&#x39;" u2="&#x32;" k="10" />
    <hkern u1="&#x39;" u2="&#x31;" k="9" />
    <hkern u1="&#x39;" u2="&#x2e;" k="71" />
    <hkern u1="&#x39;" u2="&#x2c;" k="71" />
    <hkern u1="&#x39;" u2="&#x27;" k="3" />
    <hkern u1="&#x39;" u2="&#x22;" k="3" />
    <hkern u1="&#x40;" u2="z" k="16" />
    <hkern u1="&#x40;" u2="x" k="15" />
    <hkern u1="&#x40;" u2="w" k="14" />
    <hkern u1="&#x40;" u2="&#x2d;" k="-16" />
    <hkern u1="A" g2="ampersand.ss01" k="28" />
    <hkern u1="A" g2="g.ss01" k="18" />
    <hkern u1="A" u2="y" k="52" />
    <hkern u1="A" u2="x" k="46" />
    <hkern u1="A" u2="w" k="57" />
    <hkern u1="A" u2="j" k="8" />
    <hkern u1="A" u2="Z" k="18" />
    <hkern u1="A" u2="X" k="38" />
    <hkern u1="A" u2="V" k="95" />
    <hkern u1="A" u2="S" k="27" />
    <hkern u1="A" u2="J" k="18" />
    <hkern u1="A" u2="&#x3f;" k="58" />
    <hkern u1="A" u2="&#x2d;" k="40" />
    <hkern u1="A" u2="&#x2a;" k="124" />
    <hkern u1="A" u2="&#x20;" k="28" />
    <hkern u1="B" g2="g.ss01" k="8" />
    <hkern u1="B" g2="T_l" k="21" />
    <hkern u1="B" g2="T_h" k="21" />
    <hkern u1="B" g2="T_T" k="21" />
    <hkern u1="B" u2="&#x1ef2;" k="34" />
    <hkern u1="B" u2="&#x21a;" k="21" />
    <hkern u1="B" u2="&#x178;" k="34" />
    <hkern u1="B" u2="&#x176;" k="34" />
    <hkern u1="B" u2="&#x164;" k="21" />
    <hkern u1="B" u2="&#xdd;" k="34" />
    <hkern u1="B" u2="Y" k="34" />
    <hkern u1="B" u2="X" k="24" />
    <hkern u1="B" u2="W" k="9" />
    <hkern u1="B" u2="V" k="19" />
    <hkern u1="B" u2="T" k="21" />
    <hkern u1="B" u2="S" k="14" />
    <hkern u1="B" u2="J" k="38" />
    <hkern u1="C" g2="g.ss01" k="24" />
    <hkern u1="C" u2="&#x7d;" k="-23" />
    <hkern u1="C" u2="x" k="16" />
    <hkern u1="C" u2="w" k="38" />
    <hkern u1="C" u2="Y" k="28" />
    <hkern u1="C" u2="S" k="12" />
    <hkern u1="C" u2="J" k="48" />
    <hkern u1="C" u2="&#x2d;" k="20" />
    <hkern u1="E" u2="&#x7d;" k="-26" />
    <hkern u1="E" u2="]" k="-26" />
    <hkern u1="E" u2="Y" k="14" />
    <hkern u1="E" u2="V" k="14" />
    <hkern u1="E" u2="S" k="18" />
    <hkern u1="E" u2="J" k="35" />
    <hkern u1="E" u2="&#x2d;" k="34" />
    <hkern u1="E" u2="&#x29;" k="-25" />
    <hkern u1="F" g2="oe.ss01" k="19" />
    <hkern u1="F" g2="g.ss01" k="24" />
    <hkern u1="F" g2="eogonek.ss01" k="19" />
    <hkern u1="F" g2="emacron.ss01" k="19" />
    <hkern u1="F" g2="egrave.ss01" k="19" />
    <hkern u1="F" g2="edotaccent.ss01" k="19" />
    <hkern u1="F" g2="edieresis.ss01" k="19" />
    <hkern u1="F" g2="ecircumflex.ss01" k="19" />
    <hkern u1="F" g2="ecaron.ss01" k="19" />
    <hkern u1="F" g2="eacute.ss01" k="19" />
    <hkern u1="F" g2="e.ss01" k="19" />
    <hkern u1="F" g2="ae.ss01" k="19" />
    <hkern u1="F" g2="T_l" k="22" />
    <hkern u1="F" g2="T_h" k="22" />
    <hkern u1="F" g2="T_T" k="22" />
    <hkern u1="F" u2="&#x201d;" k="-16" />
    <hkern u1="F" u2="&#x2019;" k="-16" />
    <hkern u1="F" u2="&#x1ef3;" k="12" />
    <hkern u1="F" u2="&#x2bc;" k="-16" />
    <hkern u1="F" u2="&#x21a;" k="22" />
    <hkern u1="F" u2="&#x1ce;" k="19" />
    <hkern u1="F" u2="&#x1cd;" k="61" />
    <hkern u1="F" u2="&#x177;" k="12" />
    <hkern u1="F" u2="&#x164;" k="22" />
    <hkern u1="F" u2="&#x153;" k="19" />
    <hkern u1="F" u2="&#x152;" k="12" />
    <hkern u1="F" u2="&#x151;" k="19" />
    <hkern u1="F" u2="&#x150;" k="12" />
    <hkern u1="F" u2="&#x14d;" k="19" />
    <hkern u1="F" u2="&#x14c;" k="12" />
    <hkern u1="F" u2="&#x123;" k="19" />
    <hkern u1="F" u2="&#x122;" k="12" />
    <hkern u1="F" u2="&#x121;" k="19" />
    <hkern u1="F" u2="&#x120;" k="12" />
    <hkern u1="F" u2="&#x11f;" k="19" />
    <hkern u1="F" u2="&#x11e;" k="12" />
    <hkern u1="F" u2="&#x11b;" k="19" />
    <hkern u1="F" u2="&#x119;" k="19" />
    <hkern u1="F" u2="&#x117;" k="19" />
    <hkern u1="F" u2="&#x113;" k="19" />
    <hkern u1="F" u2="&#x111;" k="19" />
    <hkern u1="F" u2="&#x10f;" k="19" />
    <hkern u1="F" u2="&#x10d;" k="19" />
    <hkern u1="F" u2="&#x10c;" k="12" />
    <hkern u1="F" u2="&#x10b;" k="19" />
    <hkern u1="F" u2="&#x10a;" k="12" />
    <hkern u1="F" u2="&#x107;" k="19" />
    <hkern u1="F" u2="&#x106;" k="12" />
    <hkern u1="F" u2="&#x105;" k="19" />
    <hkern u1="F" u2="&#x104;" k="61" />
    <hkern u1="F" u2="&#x103;" k="19" />
    <hkern u1="F" u2="&#x102;" k="61" />
    <hkern u1="F" u2="&#x101;" k="19" />
    <hkern u1="F" u2="&#x100;" k="61" />
    <hkern u1="F" u2="&#xff;" k="12" />
    <hkern u1="F" u2="&#xfd;" k="12" />
    <hkern u1="F" u2="&#xf8;" k="19" />
    <hkern u1="F" u2="&#xf6;" k="19" />
    <hkern u1="F" u2="&#xf5;" k="19" />
    <hkern u1="F" u2="&#xf4;" k="19" />
    <hkern u1="F" u2="&#xf3;" k="19" />
    <hkern u1="F" u2="&#xf2;" k="19" />
    <hkern u1="F" u2="&#xeb;" k="19" />
    <hkern u1="F" u2="&#xea;" k="19" />
    <hkern u1="F" u2="&#xe9;" k="19" />
    <hkern u1="F" u2="&#xe8;" k="19" />
    <hkern u1="F" u2="&#xe7;" k="19" />
    <hkern u1="F" u2="&#xe6;" k="19" />
    <hkern u1="F" u2="&#xe5;" k="19" />
    <hkern u1="F" u2="&#xe4;" k="19" />
    <hkern u1="F" u2="&#xe3;" k="19" />
    <hkern u1="F" u2="&#xe2;" k="19" />
    <hkern u1="F" u2="&#xe1;" k="19" />
    <hkern u1="F" u2="&#xe0;" k="19" />
    <hkern u1="F" u2="&#xd8;" k="12" />
    <hkern u1="F" u2="&#xd6;" k="12" />
    <hkern u1="F" u2="&#xd5;" k="12" />
    <hkern u1="F" u2="&#xd4;" k="12" />
    <hkern u1="F" u2="&#xd3;" k="12" />
    <hkern u1="F" u2="&#xd2;" k="12" />
    <hkern u1="F" u2="&#xc7;" k="12" />
    <hkern u1="F" u2="&#xc6;" k="61" />
    <hkern u1="F" u2="&#xc5;" k="61" />
    <hkern u1="F" u2="&#xc4;" k="61" />
    <hkern u1="F" u2="&#xc3;" k="61" />
    <hkern u1="F" u2="&#xc2;" k="61" />
    <hkern u1="F" u2="&#xc1;" k="61" />
    <hkern u1="F" u2="&#xc0;" k="61" />
    <hkern u1="F" u2="&#x7d;" k="-26" />
    <hkern u1="F" u2="z" k="18" />
    <hkern u1="F" u2="y" k="12" />
    <hkern u1="F" u2="x" k="22" />
    <hkern u1="F" u2="w" k="6" />
    <hkern u1="F" u2="v" k="12" />
    <hkern u1="F" u2="s" k="18" />
    <hkern u1="F" u2="q" k="19" />
    <hkern u1="F" u2="o" k="19" />
    <hkern u1="F" u2="g" k="19" />
    <hkern u1="F" u2="e" k="19" />
    <hkern u1="F" u2="d" k="19" />
    <hkern u1="F" u2="c" k="19" />
    <hkern u1="F" u2="a" k="19" />
    <hkern u1="F" u2="]" k="-26" />
    <hkern u1="F" u2="Z" k="10" />
    <hkern u1="F" u2="X" k="24" />
    <hkern u1="F" u2="T" k="22" />
    <hkern u1="F" u2="S" k="20" />
    <hkern u1="F" u2="Q" k="12" />
    <hkern u1="F" u2="O" k="12" />
    <hkern u1="F" u2="J" k="147" />
    <hkern u1="F" u2="G" k="12" />
    <hkern u1="F" u2="C" k="12" />
    <hkern u1="F" u2="A" k="61" />
    <hkern u1="F" u2="&#x40;" k="19" />
    <hkern u1="F" u2="&#x2f;" k="42" />
    <hkern u1="F" u2="&#x2e;" k="55" />
    <hkern u1="F" u2="&#x2d;" k="23" />
    <hkern u1="F" u2="&#x2c;" k="55" />
    <hkern u1="F" u2="&#x29;" k="-28" />
    <hkern u1="F" u2="&#x26;" k="12" />
    <hkern u1="F" u2="&#x20;" k="10" />
    <hkern u1="G" g2="T_l" k="30" />
    <hkern u1="G" g2="T_h" k="30" />
    <hkern u1="G" g2="T_T" k="30" />
    <hkern u1="G" u2="&#x201d;" k="15" />
    <hkern u1="G" u2="&#x2019;" k="15" />
    <hkern u1="G" u2="&#x1ef2;" k="45" />
    <hkern u1="G" u2="&#x2bc;" k="15" />
    <hkern u1="G" u2="&#x21a;" k="30" />
    <hkern u1="G" u2="&#x1cd;" k="33" />
    <hkern u1="G" u2="&#x178;" k="45" />
    <hkern u1="G" u2="&#x176;" k="45" />
    <hkern u1="G" u2="&#x164;" k="30" />
    <hkern u1="G" u2="&#x152;" k="-6" />
    <hkern u1="G" u2="&#x150;" k="-6" />
    <hkern u1="G" u2="&#x14c;" k="-6" />
    <hkern u1="G" u2="&#x122;" k="-6" />
    <hkern u1="G" u2="&#x120;" k="-6" />
    <hkern u1="G" u2="&#x11e;" k="-6" />
    <hkern u1="G" u2="&#x10c;" k="-6" />
    <hkern u1="G" u2="&#x10a;" k="-6" />
    <hkern u1="G" u2="&#x106;" k="-6" />
    <hkern u1="G" u2="&#x104;" k="33" />
    <hkern u1="G" u2="&#x102;" k="33" />
    <hkern u1="G" u2="&#x100;" k="33" />
    <hkern u1="G" u2="&#xdd;" k="45" />
    <hkern u1="G" u2="&#xd8;" k="-6" />
    <hkern u1="G" u2="&#xd6;" k="-6" />
    <hkern u1="G" u2="&#xd5;" k="-6" />
    <hkern u1="G" u2="&#xd4;" k="-6" />
    <hkern u1="G" u2="&#xd3;" k="-6" />
    <hkern u1="G" u2="&#xd2;" k="-6" />
    <hkern u1="G" u2="&#xc7;" k="-6" />
    <hkern u1="G" u2="&#xc6;" k="33" />
    <hkern u1="G" u2="&#xc5;" k="33" />
    <hkern u1="G" u2="&#xc4;" k="33" />
    <hkern u1="G" u2="&#xc3;" k="33" />
    <hkern u1="G" u2="&#xc2;" k="33" />
    <hkern u1="G" u2="&#xc1;" k="33" />
    <hkern u1="G" u2="&#xc0;" k="33" />
    <hkern u1="G" u2="&#x7d;" k="-11" />
    <hkern u1="G" u2="Y" k="45" />
    <hkern u1="G" u2="X" k="27" />
    <hkern u1="G" u2="W" k="19" />
    <hkern u1="G" u2="V" k="28" />
    <hkern u1="G" u2="T" k="30" />
    <hkern u1="G" u2="S" k="14" />
    <hkern u1="G" u2="Q" k="-6" />
    <hkern u1="G" u2="O" k="-6" />
    <hkern u1="G" u2="J" k="38" />
    <hkern u1="G" u2="G" k="-6" />
    <hkern u1="G" u2="C" k="-6" />
    <hkern u1="G" u2="A" k="33" />
    <hkern u1="G" u2="&#x2e;" k="42" />
    <hkern u1="G" u2="&#x2c;" k="42" />
    <hkern u1="G" u2="&#x27;" k="30" />
    <hkern u1="G" u2="&#x22;" k="30" />
    <hkern u1="H" u2="x" k="12" />
    <hkern u1="H" u2="S" k="14" />
    <hkern u1="H" u2="&#x2f;" k="-14" />
    <hkern u1="I" u2="x" k="12" />
    <hkern u1="I" u2="S" k="14" />
    <hkern u1="I" u2="&#x2f;" k="-14" />
    <hkern u1="J" g2="g.ss01" k="8" />
    <hkern u1="J" u2="X" k="18" />
    <hkern u1="J" u2="S" k="8" />
    <hkern u1="J" u2="J" k="48" />
    <hkern u1="J" u2="&#x2f;" k="17" />
    <hkern u1="K" g2="ampersand.ss01" k="36" />
    <hkern u1="K" g2="o_u_t_f_i_t_t_e_r" k="48" />
    <hkern u1="K" g2="o_u_t_f_i_t_t_e_d" k="48" />
    <hkern u1="K" g2="o_u_t_f_i_t" k="48" />
    <hkern u1="K" g2="f_t" k="52" />
    <hkern u1="K" g2="f_j" k="52" />
    <hkern u1="K" g2="f_f" k="52" />
    <hkern u1="K" g2="t.ss01" k="52" />
    <hkern u1="K" g2="oe.ss01" k="51" />
    <hkern u1="K" g2="eogonek.ss01" k="51" />
    <hkern u1="K" g2="emacron.ss01" k="51" />
    <hkern u1="K" g2="egrave.ss01" k="51" />
    <hkern u1="K" g2="edotaccent.ss01" k="51" />
    <hkern u1="K" g2="edieresis.ss01" k="51" />
    <hkern u1="K" g2="ecircumflex.ss01" k="51" />
    <hkern u1="K" g2="ecaron.ss01" k="51" />
    <hkern u1="K" g2="eacute.ss01" k="51" />
    <hkern u1="K" g2="e.ss01" k="51" />
    <hkern u1="K" g2="ae.ss01" k="51" />
    <hkern u1="K" g2="T_l" k="64" />
    <hkern u1="K" g2="T_h" k="64" />
    <hkern u1="K" g2="T_T" k="64" />
    <hkern u1="K" g2="fl" k="52" />
    <hkern u1="K" g2="fi" k="52" />
    <hkern u1="K" u2="&#x201d;" k="24" />
    <hkern u1="K" u2="&#x2019;" k="24" />
    <hkern u1="K" u2="&#x1ef3;" k="88" />
    <hkern u1="K" u2="&#x1ef2;" k="55" />
    <hkern u1="K" u2="&#x2bc;" k="24" />
    <hkern u1="K" u2="&#x21b;" k="52" />
    <hkern u1="K" u2="&#x21a;" k="64" />
    <hkern u1="K" u2="&#x1ce;" k="51" />
    <hkern u1="K" u2="&#x1cd;" k="38" />
    <hkern u1="K" u2="&#x178;" k="55" />
    <hkern u1="K" u2="&#x177;" k="88" />
    <hkern u1="K" u2="&#x176;" k="55" />
    <hkern u1="K" u2="&#x173;" k="48" />
    <hkern u1="K" u2="&#x172;" k="32" />
    <hkern u1="K" u2="&#x171;" k="48" />
    <hkern u1="K" u2="&#x170;" k="32" />
    <hkern u1="K" u2="&#x16f;" k="48" />
    <hkern u1="K" u2="&#x16e;" k="32" />
    <hkern u1="K" u2="&#x16d;" k="48" />
    <hkern u1="K" u2="&#x16c;" k="32" />
    <hkern u1="K" u2="&#x16b;" k="48" />
    <hkern u1="K" u2="&#x16a;" k="32" />
    <hkern u1="K" u2="&#x165;" k="52" />
    <hkern u1="K" u2="&#x164;" k="64" />
    <hkern u1="K" u2="&#x158;" k="18" />
    <hkern u1="K" u2="&#x156;" k="18" />
    <hkern u1="K" u2="&#x154;" k="18" />
    <hkern u1="K" u2="&#x153;" k="51" />
    <hkern u1="K" u2="&#x152;" k="66" />
    <hkern u1="K" u2="&#x151;" k="51" />
    <hkern u1="K" u2="&#x150;" k="66" />
    <hkern u1="K" u2="&#x14d;" k="51" />
    <hkern u1="K" u2="&#x14c;" k="66" />
    <hkern u1="K" u2="&#x14a;" k="18" />
    <hkern u1="K" u2="&#x147;" k="18" />
    <hkern u1="K" u2="&#x145;" k="18" />
    <hkern u1="K" u2="&#x143;" k="18" />
    <hkern u1="K" u2="&#x141;" k="18" />
    <hkern u1="K" u2="&#x13d;" k="18" />
    <hkern u1="K" u2="&#x13b;" k="18" />
    <hkern u1="K" u2="&#x139;" k="18" />
    <hkern u1="K" u2="&#x136;" k="18" />
    <hkern u1="K" u2="&#x130;" k="18" />
    <hkern u1="K" u2="&#x12e;" k="18" />
    <hkern u1="K" u2="&#x12a;" k="18" />
    <hkern u1="K" u2="&#x123;" k="51" />
    <hkern u1="K" u2="&#x122;" k="66" />
    <hkern u1="K" u2="&#x121;" k="51" />
    <hkern u1="K" u2="&#x120;" k="66" />
    <hkern u1="K" u2="&#x11f;" k="51" />
    <hkern u1="K" u2="&#x11e;" k="66" />
    <hkern u1="K" u2="&#x11b;" k="51" />
    <hkern u1="K" u2="&#x11a;" k="18" />
    <hkern u1="K" u2="&#x119;" k="51" />
    <hkern u1="K" u2="&#x118;" k="18" />
    <hkern u1="K" u2="&#x117;" k="51" />
    <hkern u1="K" u2="&#x116;" k="18" />
    <hkern u1="K" u2="&#x113;" k="51" />
    <hkern u1="K" u2="&#x112;" k="18" />
    <hkern u1="K" u2="&#x111;" k="51" />
    <hkern u1="K" u2="&#x110;" k="18" />
    <hkern u1="K" u2="&#x10f;" k="51" />
    <hkern u1="K" u2="&#x10e;" k="18" />
    <hkern u1="K" u2="&#x10d;" k="51" />
    <hkern u1="K" u2="&#x10c;" k="66" />
    <hkern u1="K" u2="&#x10b;" k="51" />
    <hkern u1="K" u2="&#x10a;" k="66" />
    <hkern u1="K" u2="&#x107;" k="51" />
    <hkern u1="K" u2="&#x106;" k="66" />
    <hkern u1="K" u2="&#x105;" k="51" />
    <hkern u1="K" u2="&#x104;" k="38" />
    <hkern u1="K" u2="&#x103;" k="51" />
    <hkern u1="K" u2="&#x102;" k="38" />
    <hkern u1="K" u2="&#x101;" k="51" />
    <hkern u1="K" u2="&#x100;" k="38" />
    <hkern u1="K" u2="&#xff;" k="88" />
    <hkern u1="K" u2="&#xfd;" k="88" />
    <hkern u1="K" u2="&#xfc;" k="48" />
    <hkern u1="K" u2="&#xfb;" k="48" />
    <hkern u1="K" u2="&#xfa;" k="48" />
    <hkern u1="K" u2="&#xf9;" k="48" />
    <hkern u1="K" u2="&#xf8;" k="51" />
    <hkern u1="K" u2="&#xf6;" k="51" />
    <hkern u1="K" u2="&#xf5;" k="51" />
    <hkern u1="K" u2="&#xf4;" k="51" />
    <hkern u1="K" u2="&#xf3;" k="51" />
    <hkern u1="K" u2="&#xf2;" k="51" />
    <hkern u1="K" u2="&#xeb;" k="51" />
    <hkern u1="K" u2="&#xea;" k="51" />
    <hkern u1="K" u2="&#xe9;" k="51" />
    <hkern u1="K" u2="&#xe8;" k="51" />
    <hkern u1="K" u2="&#xe7;" k="51" />
    <hkern u1="K" u2="&#xe6;" k="51" />
    <hkern u1="K" u2="&#xe5;" k="51" />
    <hkern u1="K" u2="&#xe4;" k="51" />
    <hkern u1="K" u2="&#xe3;" k="51" />
    <hkern u1="K" u2="&#xe2;" k="51" />
    <hkern u1="K" u2="&#xe1;" k="51" />
    <hkern u1="K" u2="&#xe0;" k="51" />
    <hkern u1="K" u2="&#xde;" k="18" />
    <hkern u1="K" u2="&#xdd;" k="55" />
    <hkern u1="K" u2="&#xdc;" k="32" />
    <hkern u1="K" u2="&#xdb;" k="32" />
    <hkern u1="K" u2="&#xda;" k="32" />
    <hkern u1="K" u2="&#xd9;" k="32" />
    <hkern u1="K" u2="&#xd8;" k="66" />
    <hkern u1="K" u2="&#xd6;" k="66" />
    <hkern u1="K" u2="&#xd5;" k="66" />
    <hkern u1="K" u2="&#xd4;" k="66" />
    <hkern u1="K" u2="&#xd3;" k="66" />
    <hkern u1="K" u2="&#xd2;" k="66" />
    <hkern u1="K" u2="&#xd1;" k="18" />
    <hkern u1="K" u2="&#xd0;" k="18" />
    <hkern u1="K" u2="&#xcf;" k="18" />
    <hkern u1="K" u2="&#xce;" k="18" />
    <hkern u1="K" u2="&#xcd;" k="18" />
    <hkern u1="K" u2="&#xcc;" k="18" />
    <hkern u1="K" u2="&#xcb;" k="18" />
    <hkern u1="K" u2="&#xca;" k="18" />
    <hkern u1="K" u2="&#xc9;" k="18" />
    <hkern u1="K" u2="&#xc8;" k="18" />
    <hkern u1="K" u2="&#xc7;" k="66" />
    <hkern u1="K" u2="&#xc6;" k="38" />
    <hkern u1="K" u2="&#xc5;" k="38" />
    <hkern u1="K" u2="&#xc4;" k="38" />
    <hkern u1="K" u2="&#xc3;" k="38" />
    <hkern u1="K" u2="&#xc2;" k="38" />
    <hkern u1="K" u2="&#xc1;" k="38" />
    <hkern u1="K" u2="&#xc0;" k="38" />
    <hkern u1="K" u2="&#x7c;" k="18" />
    <hkern u1="K" u2="y" k="88" />
    <hkern u1="K" u2="x" k="36" />
    <hkern u1="K" u2="w" k="59" />
    <hkern u1="K" u2="v" k="88" />
    <hkern u1="K" u2="u" k="48" />
    <hkern u1="K" u2="t" k="52" />
    <hkern u1="K" u2="s" k="34" />
    <hkern u1="K" u2="q" k="51" />
    <hkern u1="K" u2="o" k="51" />
    <hkern u1="K" u2="g" k="51" />
    <hkern u1="K" u2="f" k="52" />
    <hkern u1="K" u2="e" k="51" />
    <hkern u1="K" u2="d" k="51" />
    <hkern u1="K" u2="c" k="51" />
    <hkern u1="K" u2="a" k="51" />
    <hkern u1="K" u2="Z" k="38" />
    <hkern u1="K" u2="Y" k="55" />
    <hkern u1="K" u2="X" k="64" />
    <hkern u1="K" u2="W" k="38" />
    <hkern u1="K" u2="V" k="56" />
    <hkern u1="K" u2="U" k="32" />
    <hkern u1="K" u2="T" k="64" />
    <hkern u1="K" u2="S" k="50" />
    <hkern u1="K" u2="R" k="18" />
    <hkern u1="K" u2="Q" k="66" />
    <hkern u1="K" u2="P" k="18" />
    <hkern u1="K" u2="O" k="66" />
    <hkern u1="K" u2="N" k="18" />
    <hkern u1="K" u2="M" k="18" />
    <hkern u1="K" u2="L" k="18" />
    <hkern u1="K" u2="K" k="18" />
    <hkern u1="K" u2="J" k="30" />
    <hkern u1="K" u2="I" k="18" />
    <hkern u1="K" u2="H" k="18" />
    <hkern u1="K" u2="G" k="66" />
    <hkern u1="K" u2="F" k="18" />
    <hkern u1="K" u2="E" k="18" />
    <hkern u1="K" u2="D" k="18" />
    <hkern u1="K" u2="C" k="66" />
    <hkern u1="K" u2="B" k="18" />
    <hkern u1="K" u2="A" k="38" />
    <hkern u1="K" u2="&#x40;" k="51" />
    <hkern u1="K" u2="&#x3b;" k="12" />
    <hkern u1="K" u2="&#x3a;" k="12" />
    <hkern u1="K" u2="&#x2d;" k="76" />
    <hkern u1="K" u2="&#x2a;" k="36" />
    <hkern u1="K" u2="&#x20;" k="28" />
    <hkern u1="L" g2="f_t" k="39" />
    <hkern u1="L" g2="f_j" k="39" />
    <hkern u1="L" g2="f_f" k="39" />
    <hkern u1="L" g2="t.ss01" k="39" />
    <hkern u1="L" g2="oe.ss01" k="20" />
    <hkern u1="L" g2="eogonek.ss01" k="20" />
    <hkern u1="L" g2="emacron.ss01" k="20" />
    <hkern u1="L" g2="egrave.ss01" k="20" />
    <hkern u1="L" g2="edotaccent.ss01" k="20" />
    <hkern u1="L" g2="edieresis.ss01" k="20" />
    <hkern u1="L" g2="ecircumflex.ss01" k="20" />
    <hkern u1="L" g2="ecaron.ss01" k="20" />
    <hkern u1="L" g2="eacute.ss01" k="20" />
    <hkern u1="L" g2="e.ss01" k="20" />
    <hkern u1="L" g2="ae.ss01" k="20" />
    <hkern u1="L" g2="T_l" k="91" />
    <hkern u1="L" g2="T_h" k="91" />
    <hkern u1="L" g2="T_T" k="91" />
    <hkern u1="L" g2="fl" k="39" />
    <hkern u1="L" g2="fi" k="39" />
    <hkern u1="L" u2="&#x201d;" k="90" />
    <hkern u1="L" u2="&#x2019;" k="90" />
    <hkern u1="L" u2="&#x1ef3;" k="29" />
    <hkern u1="L" u2="&#x1ef2;" k="101" />
    <hkern u1="L" u2="&#x2bc;" k="90" />
    <hkern u1="L" u2="&#x21b;" k="39" />
    <hkern u1="L" u2="&#x21a;" k="91" />
    <hkern u1="L" u2="&#x1ce;" k="20" />
    <hkern u1="L" u2="&#x1cd;" k="25" />
    <hkern u1="L" u2="&#x178;" k="101" />
    <hkern u1="L" u2="&#x177;" k="29" />
    <hkern u1="L" u2="&#x176;" k="101" />
    <hkern u1="L" u2="&#x172;" k="24" />
    <hkern u1="L" u2="&#x170;" k="24" />
    <hkern u1="L" u2="&#x16e;" k="24" />
    <hkern u1="L" u2="&#x16c;" k="24" />
    <hkern u1="L" u2="&#x16a;" k="24" />
    <hkern u1="L" u2="&#x165;" k="39" />
    <hkern u1="L" u2="&#x164;" k="91" />
    <hkern u1="L" u2="&#x153;" k="20" />
    <hkern u1="L" u2="&#x152;" k="60" />
    <hkern u1="L" u2="&#x151;" k="20" />
    <hkern u1="L" u2="&#x150;" k="60" />
    <hkern u1="L" u2="&#x14d;" k="20" />
    <hkern u1="L" u2="&#x14c;" k="60" />
    <hkern u1="L" u2="&#x123;" k="20" />
    <hkern u1="L" u2="&#x122;" k="60" />
    <hkern u1="L" u2="&#x121;" k="20" />
    <hkern u1="L" u2="&#x120;" k="60" />
    <hkern u1="L" u2="&#x11f;" k="20" />
    <hkern u1="L" u2="&#x11e;" k="60" />
    <hkern u1="L" u2="&#x11b;" k="20" />
    <hkern u1="L" u2="&#x119;" k="20" />
    <hkern u1="L" u2="&#x117;" k="20" />
    <hkern u1="L" u2="&#x113;" k="20" />
    <hkern u1="L" u2="&#x111;" k="20" />
    <hkern u1="L" u2="&#x10f;" k="20" />
    <hkern u1="L" u2="&#x10d;" k="20" />
    <hkern u1="L" u2="&#x10c;" k="60" />
    <hkern u1="L" u2="&#x10b;" k="20" />
    <hkern u1="L" u2="&#x10a;" k="60" />
    <hkern u1="L" u2="&#x107;" k="20" />
    <hkern u1="L" u2="&#x106;" k="60" />
    <hkern u1="L" u2="&#x105;" k="20" />
    <hkern u1="L" u2="&#x104;" k="25" />
    <hkern u1="L" u2="&#x103;" k="20" />
    <hkern u1="L" u2="&#x102;" k="25" />
    <hkern u1="L" u2="&#x101;" k="20" />
    <hkern u1="L" u2="&#x100;" k="25" />
    <hkern u1="L" u2="&#xff;" k="29" />
    <hkern u1="L" u2="&#xfd;" k="29" />
    <hkern u1="L" u2="&#xf8;" k="20" />
    <hkern u1="L" u2="&#xf6;" k="20" />
    <hkern u1="L" u2="&#xf5;" k="20" />
    <hkern u1="L" u2="&#xf4;" k="20" />
    <hkern u1="L" u2="&#xf3;" k="20" />
    <hkern u1="L" u2="&#xf2;" k="20" />
    <hkern u1="L" u2="&#xeb;" k="20" />
    <hkern u1="L" u2="&#xea;" k="20" />
    <hkern u1="L" u2="&#xe9;" k="20" />
    <hkern u1="L" u2="&#xe8;" k="20" />
    <hkern u1="L" u2="&#xe7;" k="20" />
    <hkern u1="L" u2="&#xe6;" k="20" />
    <hkern u1="L" u2="&#xe5;" k="20" />
    <hkern u1="L" u2="&#xe4;" k="20" />
    <hkern u1="L" u2="&#xe3;" k="20" />
    <hkern u1="L" u2="&#xe2;" k="20" />
    <hkern u1="L" u2="&#xe1;" k="20" />
    <hkern u1="L" u2="&#xe0;" k="20" />
    <hkern u1="L" u2="&#xdd;" k="101" />
    <hkern u1="L" u2="&#xdc;" k="24" />
    <hkern u1="L" u2="&#xdb;" k="24" />
    <hkern u1="L" u2="&#xda;" k="24" />
    <hkern u1="L" u2="&#xd9;" k="24" />
    <hkern u1="L" u2="&#xd8;" k="60" />
    <hkern u1="L" u2="&#xd6;" k="60" />
    <hkern u1="L" u2="&#xd5;" k="60" />
    <hkern u1="L" u2="&#xd4;" k="60" />
    <hkern u1="L" u2="&#xd3;" k="60" />
    <hkern u1="L" u2="&#xd2;" k="60" />
    <hkern u1="L" u2="&#xc7;" k="60" />
    <hkern u1="L" u2="&#xc6;" k="25" />
    <hkern u1="L" u2="&#xc5;" k="25" />
    <hkern u1="L" u2="&#xc4;" k="25" />
    <hkern u1="L" u2="&#xc3;" k="25" />
    <hkern u1="L" u2="&#xc2;" k="25" />
    <hkern u1="L" u2="&#xc1;" k="25" />
    <hkern u1="L" u2="&#xc0;" k="25" />
    <hkern u1="L" u2="y" k="29" />
    <hkern u1="L" u2="x" k="17" />
    <hkern u1="L" u2="w" k="23" />
    <hkern u1="L" u2="v" k="29" />
    <hkern u1="L" u2="t" k="39" />
    <hkern u1="L" u2="q" k="20" />
    <hkern u1="L" u2="o" k="20" />
    <hkern u1="L" u2="g" k="20" />
    <hkern u1="L" u2="f" k="39" />
    <hkern u1="L" u2="e" k="20" />
    <hkern u1="L" u2="d" k="20" />
    <hkern u1="L" u2="c" k="20" />
    <hkern u1="L" u2="a" k="20" />
    <hkern u1="L" u2="Y" k="101" />
    <hkern u1="L" u2="W" k="68" />
    <hkern u1="L" u2="V" k="77" />
    <hkern u1="L" u2="U" k="24" />
    <hkern u1="L" u2="T" k="91" />
    <hkern u1="L" u2="S" k="32" />
    <hkern u1="L" u2="Q" k="60" />
    <hkern u1="L" u2="O" k="60" />
    <hkern u1="L" u2="J" k="15" />
    <hkern u1="L" u2="G" k="60" />
    <hkern u1="L" u2="C" k="60" />
    <hkern u1="L" u2="A" k="25" />
    <hkern u1="L" u2="&#x40;" k="20" />
    <hkern u1="L" u2="&#x3f;" k="30" />
    <hkern u1="L" u2="&#x2d;" k="43" />
    <hkern u1="L" u2="&#x2a;" k="84" />
    <hkern u1="L" u2="&#x27;" k="88" />
    <hkern u1="L" u2="&#x22;" k="88" />
    <hkern u1="L" u2="&#x20;" k="8" />
    <hkern u1="M" u2="x" k="12" />
    <hkern u1="M" u2="S" k="14" />
    <hkern u1="M" u2="&#x2f;" k="-14" />
    <hkern u1="N" u2="x" k="12" />
    <hkern u1="N" u2="S" k="14" />
    <hkern u1="N" u2="&#x2f;" k="-14" />
    <hkern u1="O" u2="x" k="11" />
    <hkern u1="O" u2="j" k="-24" />
    <hkern u1="O" u2="]" k="12" />
    <hkern u1="O" u2="Z" k="14" />
    <hkern u1="O" u2="X" k="41" />
    <hkern u1="O" u2="W" k="32" />
    <hkern u1="O" u2="V" k="39" />
    <hkern u1="O" u2="S" k="19" />
    <hkern u1="O" u2="J" k="62" />
    <hkern u1="O" u2="&#x2f;" k="27" />
    <hkern u1="O" u2="&#x2d;" k="-13" />
    <hkern u1="O" u2="&#x29;" k="12" />
    <hkern u1="P" g2="oe.ss01" k="27" />
    <hkern u1="P" g2="g.ss01" k="36" />
    <hkern u1="P" g2="eogonek.ss01" k="27" />
    <hkern u1="P" g2="emacron.ss01" k="27" />
    <hkern u1="P" g2="egrave.ss01" k="27" />
    <hkern u1="P" g2="edotaccent.ss01" k="27" />
    <hkern u1="P" g2="edieresis.ss01" k="27" />
    <hkern u1="P" g2="ecircumflex.ss01" k="27" />
    <hkern u1="P" g2="ecaron.ss01" k="27" />
    <hkern u1="P" g2="eacute.ss01" k="27" />
    <hkern u1="P" g2="e.ss01" k="27" />
    <hkern u1="P" g2="ae.ss01" k="27" />
    <hkern u1="P" g2="T_l" k="21" />
    <hkern u1="P" g2="T_h" k="21" />
    <hkern u1="P" g2="T_T" k="21" />
    <hkern u1="P" u2="&#x201d;" k="-23" />
    <hkern u1="P" u2="&#x2019;" k="-23" />
    <hkern u1="P" u2="&#x1ef2;" k="29" />
    <hkern u1="P" u2="&#x2bc;" k="-23" />
    <hkern u1="P" u2="&#x21a;" k="21" />
    <hkern u1="P" u2="&#x1ce;" k="27" />
    <hkern u1="P" u2="&#x1cd;" k="66" />
    <hkern u1="P" u2="&#x178;" k="29" />
    <hkern u1="P" u2="&#x176;" k="29" />
    <hkern u1="P" u2="&#x164;" k="21" />
    <hkern u1="P" u2="&#x158;" k="8" />
    <hkern u1="P" u2="&#x156;" k="8" />
    <hkern u1="P" u2="&#x154;" k="8" />
    <hkern u1="P" u2="&#x153;" k="27" />
    <hkern u1="P" u2="&#x151;" k="27" />
    <hkern u1="P" u2="&#x14d;" k="27" />
    <hkern u1="P" u2="&#x14a;" k="8" />
    <hkern u1="P" u2="&#x147;" k="8" />
    <hkern u1="P" u2="&#x145;" k="8" />
    <hkern u1="P" u2="&#x143;" k="8" />
    <hkern u1="P" u2="&#x141;" k="8" />
    <hkern u1="P" u2="&#x13d;" k="8" />
    <hkern u1="P" u2="&#x13b;" k="8" />
    <hkern u1="P" u2="&#x139;" k="8" />
    <hkern u1="P" u2="&#x136;" k="8" />
    <hkern u1="P" u2="&#x130;" k="8" />
    <hkern u1="P" u2="&#x12e;" k="8" />
    <hkern u1="P" u2="&#x12a;" k="8" />
    <hkern u1="P" u2="&#x123;" k="27" />
    <hkern u1="P" u2="&#x121;" k="27" />
    <hkern u1="P" u2="&#x11f;" k="27" />
    <hkern u1="P" u2="&#x11b;" k="27" />
    <hkern u1="P" u2="&#x11a;" k="8" />
    <hkern u1="P" u2="&#x119;" k="27" />
    <hkern u1="P" u2="&#x118;" k="8" />
    <hkern u1="P" u2="&#x117;" k="27" />
    <hkern u1="P" u2="&#x116;" k="8" />
    <hkern u1="P" u2="&#x113;" k="27" />
    <hkern u1="P" u2="&#x112;" k="8" />
    <hkern u1="P" u2="&#x111;" k="27" />
    <hkern u1="P" u2="&#x110;" k="8" />
    <hkern u1="P" u2="&#x10f;" k="27" />
    <hkern u1="P" u2="&#x10e;" k="8" />
    <hkern u1="P" u2="&#x10d;" k="27" />
    <hkern u1="P" u2="&#x10b;" k="27" />
    <hkern u1="P" u2="&#x107;" k="27" />
    <hkern u1="P" u2="&#x105;" k="27" />
    <hkern u1="P" u2="&#x104;" k="66" />
    <hkern u1="P" u2="&#x103;" k="27" />
    <hkern u1="P" u2="&#x102;" k="66" />
    <hkern u1="P" u2="&#x101;" k="27" />
    <hkern u1="P" u2="&#x100;" k="66" />
    <hkern u1="P" u2="&#xf8;" k="27" />
    <hkern u1="P" u2="&#xf6;" k="27" />
    <hkern u1="P" u2="&#xf5;" k="27" />
    <hkern u1="P" u2="&#xf4;" k="27" />
    <hkern u1="P" u2="&#xf3;" k="27" />
    <hkern u1="P" u2="&#xf2;" k="27" />
    <hkern u1="P" u2="&#xeb;" k="27" />
    <hkern u1="P" u2="&#xea;" k="27" />
    <hkern u1="P" u2="&#xe9;" k="27" />
    <hkern u1="P" u2="&#xe8;" k="27" />
    <hkern u1="P" u2="&#xe7;" k="27" />
    <hkern u1="P" u2="&#xe6;" k="27" />
    <hkern u1="P" u2="&#xe5;" k="27" />
    <hkern u1="P" u2="&#xe4;" k="27" />
    <hkern u1="P" u2="&#xe3;" k="27" />
    <hkern u1="P" u2="&#xe2;" k="27" />
    <hkern u1="P" u2="&#xe1;" k="27" />
    <hkern u1="P" u2="&#xe0;" k="27" />
    <hkern u1="P" u2="&#xde;" k="8" />
    <hkern u1="P" u2="&#xdd;" k="29" />
    <hkern u1="P" u2="&#xd1;" k="8" />
    <hkern u1="P" u2="&#xd0;" k="8" />
    <hkern u1="P" u2="&#xcf;" k="8" />
    <hkern u1="P" u2="&#xce;" k="8" />
    <hkern u1="P" u2="&#xcd;" k="8" />
    <hkern u1="P" u2="&#xcc;" k="8" />
    <hkern u1="P" u2="&#xcb;" k="8" />
    <hkern u1="P" u2="&#xca;" k="8" />
    <hkern u1="P" u2="&#xc9;" k="8" />
    <hkern u1="P" u2="&#xc8;" k="8" />
    <hkern u1="P" u2="&#xc6;" k="66" />
    <hkern u1="P" u2="&#xc5;" k="66" />
    <hkern u1="P" u2="&#xc4;" k="66" />
    <hkern u1="P" u2="&#xc3;" k="66" />
    <hkern u1="P" u2="&#xc2;" k="66" />
    <hkern u1="P" u2="&#xc1;" k="66" />
    <hkern u1="P" u2="&#xc0;" k="66" />
    <hkern u1="P" u2="&#x7c;" k="8" />
    <hkern u1="P" u2="s" k="19" />
    <hkern u1="P" u2="q" k="27" />
    <hkern u1="P" u2="o" k="27" />
    <hkern u1="P" u2="g" k="27" />
    <hkern u1="P" u2="e" k="27" />
    <hkern u1="P" u2="d" k="27" />
    <hkern u1="P" u2="c" k="27" />
    <hkern u1="P" u2="a" k="27" />
    <hkern u1="P" u2="Z" k="30" />
    <hkern u1="P" u2="Y" k="29" />
    <hkern u1="P" u2="X" k="35" />
    <hkern u1="P" u2="V" k="10" />
    <hkern u1="P" u2="T" k="21" />
    <hkern u1="P" u2="R" k="8" />
    <hkern u1="P" u2="P" k="8" />
    <hkern u1="P" u2="N" k="8" />
    <hkern u1="P" u2="M" k="8" />
    <hkern u1="P" u2="L" k="8" />
    <hkern u1="P" u2="K" k="8" />
    <hkern u1="P" u2="J" k="128" />
    <hkern u1="P" u2="I" k="8" />
    <hkern u1="P" u2="H" k="8" />
    <hkern u1="P" u2="F" k="8" />
    <hkern u1="P" u2="E" k="8" />
    <hkern u1="P" u2="D" k="8" />
    <hkern u1="P" u2="B" k="8" />
    <hkern u1="P" u2="A" k="66" />
    <hkern u1="P" u2="&#x40;" k="27" />
    <hkern u1="P" u2="&#x2f;" k="56" />
    <hkern u1="P" u2="&#x2e;" k="85" />
    <hkern u1="P" u2="&#x2d;" k="50" />
    <hkern u1="P" u2="&#x2c;" k="85" />
    <hkern u1="P" u2="&#x20;" k="18" />
    <hkern u1="Q" g2="T_l" k="50" />
    <hkern u1="Q" g2="T_h" k="50" />
    <hkern u1="Q" g2="T_T" k="50" />
    <hkern u1="Q" u2="&#x201d;" k="30" />
    <hkern u1="Q" u2="&#x2019;" k="30" />
    <hkern u1="Q" u2="&#x1ef2;" k="61" />
    <hkern u1="Q" u2="&#x2bc;" k="30" />
    <hkern u1="Q" u2="&#x21a;" k="50" />
    <hkern u1="Q" u2="&#x178;" k="61" />
    <hkern u1="Q" u2="&#x176;" k="61" />
    <hkern u1="Q" u2="&#x164;" k="50" />
    <hkern u1="Q" u2="&#xdd;" k="61" />
    <hkern u1="Q" u2="&#x7d;" k="-18" />
    <hkern u1="Q" u2="Y" k="61" />
    <hkern u1="Q" u2="X" k="14" />
    <hkern u1="Q" u2="W" k="32" />
    <hkern u1="Q" u2="T" k="50" />
    <hkern u1="Q" u2="&#x2f;" k="-17" />
    <hkern u1="Q" u2="&#x2e;" k="22" />
    <hkern u1="Q" u2="&#x2c;" k="22" />
    <hkern u1="R" g2="o_u_t_f_i_t_t_e_r" k="14" />
    <hkern u1="R" g2="o_u_t_f_i_t_t_e_d" k="14" />
    <hkern u1="R" g2="o_u_t_f_i_t" k="14" />
    <hkern u1="R" g2="f_t" k="20" />
    <hkern u1="R" g2="f_j" k="20" />
    <hkern u1="R" g2="f_f" k="20" />
    <hkern u1="R" g2="t.ss01" k="20" />
    <hkern u1="R" g2="oe.ss01" k="29" />
    <hkern u1="R" g2="eogonek.ss01" k="29" />
    <hkern u1="R" g2="emacron.ss01" k="29" />
    <hkern u1="R" g2="egrave.ss01" k="29" />
    <hkern u1="R" g2="edotaccent.ss01" k="29" />
    <hkern u1="R" g2="edieresis.ss01" k="29" />
    <hkern u1="R" g2="ecircumflex.ss01" k="29" />
    <hkern u1="R" g2="ecaron.ss01" k="29" />
    <hkern u1="R" g2="eacute.ss01" k="29" />
    <hkern u1="R" g2="e.ss01" k="29" />
    <hkern u1="R" g2="ae.ss01" k="29" />
    <hkern u1="R" g2="T_l" k="51" />
    <hkern u1="R" g2="T_h" k="51" />
    <hkern u1="R" g2="T_T" k="51" />
    <hkern u1="R" g2="fl" k="20" />
    <hkern u1="R" g2="fi" k="20" />
    <hkern u1="R" u2="&#x201d;" k="24" />
    <hkern u1="R" u2="&#x2019;" k="24" />
    <hkern u1="R" u2="&#x1ef3;" k="26" />
    <hkern u1="R" u2="&#x1ef2;" k="57" />
    <hkern u1="R" u2="&#x2bc;" k="24" />
    <hkern u1="R" u2="&#x21b;" k="20" />
    <hkern u1="R" u2="&#x21a;" k="51" />
    <hkern u1="R" u2="&#x1ce;" k="29" />
    <hkern u1="R" u2="&#x1cd;" k="36" />
    <hkern u1="R" u2="&#x178;" k="57" />
    <hkern u1="R" u2="&#x177;" k="26" />
    <hkern u1="R" u2="&#x176;" k="57" />
    <hkern u1="R" u2="&#x173;" k="14" />
    <hkern u1="R" u2="&#x172;" k="16" />
    <hkern u1="R" u2="&#x171;" k="14" />
    <hkern u1="R" u2="&#x170;" k="16" />
    <hkern u1="R" u2="&#x16f;" k="14" />
    <hkern u1="R" u2="&#x16e;" k="16" />
    <hkern u1="R" u2="&#x16d;" k="14" />
    <hkern u1="R" u2="&#x16c;" k="16" />
    <hkern u1="R" u2="&#x16b;" k="14" />
    <hkern u1="R" u2="&#x16a;" k="16" />
    <hkern u1="R" u2="&#x165;" k="20" />
    <hkern u1="R" u2="&#x164;" k="51" />
    <hkern u1="R" u2="&#x158;" k="14" />
    <hkern u1="R" u2="&#x156;" k="14" />
    <hkern u1="R" u2="&#x154;" k="14" />
    <hkern u1="R" u2="&#x153;" k="29" />
    <hkern u1="R" u2="&#x152;" k="24" />
    <hkern u1="R" u2="&#x151;" k="29" />
    <hkern u1="R" u2="&#x150;" k="24" />
    <hkern u1="R" u2="&#x14d;" k="29" />
    <hkern u1="R" u2="&#x14c;" k="24" />
    <hkern u1="R" u2="&#x14a;" k="14" />
    <hkern u1="R" u2="&#x147;" k="14" />
    <hkern u1="R" u2="&#x145;" k="14" />
    <hkern u1="R" u2="&#x143;" k="14" />
    <hkern u1="R" u2="&#x141;" k="14" />
    <hkern u1="R" u2="&#x13d;" k="14" />
    <hkern u1="R" u2="&#x13b;" k="14" />
    <hkern u1="R" u2="&#x139;" k="14" />
    <hkern u1="R" u2="&#x136;" k="14" />
    <hkern u1="R" u2="&#x130;" k="14" />
    <hkern u1="R" u2="&#x12e;" k="14" />
    <hkern u1="R" u2="&#x12a;" k="14" />
    <hkern u1="R" u2="&#x123;" k="29" />
    <hkern u1="R" u2="&#x122;" k="24" />
    <hkern u1="R" u2="&#x121;" k="29" />
    <hkern u1="R" u2="&#x120;" k="24" />
    <hkern u1="R" u2="&#x11f;" k="29" />
    <hkern u1="R" u2="&#x11e;" k="24" />
    <hkern u1="R" u2="&#x11b;" k="29" />
    <hkern u1="R" u2="&#x11a;" k="14" />
    <hkern u1="R" u2="&#x119;" k="29" />
    <hkern u1="R" u2="&#x118;" k="14" />
    <hkern u1="R" u2="&#x117;" k="29" />
    <hkern u1="R" u2="&#x116;" k="14" />
    <hkern u1="R" u2="&#x113;" k="29" />
    <hkern u1="R" u2="&#x112;" k="14" />
    <hkern u1="R" u2="&#x111;" k="29" />
    <hkern u1="R" u2="&#x110;" k="14" />
    <hkern u1="R" u2="&#x10f;" k="29" />
    <hkern u1="R" u2="&#x10e;" k="14" />
    <hkern u1="R" u2="&#x10d;" k="29" />
    <hkern u1="R" u2="&#x10c;" k="24" />
    <hkern u1="R" u2="&#x10b;" k="29" />
    <hkern u1="R" u2="&#x10a;" k="24" />
    <hkern u1="R" u2="&#x107;" k="29" />
    <hkern u1="R" u2="&#x106;" k="24" />
    <hkern u1="R" u2="&#x105;" k="29" />
    <hkern u1="R" u2="&#x104;" k="36" />
    <hkern u1="R" u2="&#x103;" k="29" />
    <hkern u1="R" u2="&#x102;" k="36" />
    <hkern u1="R" u2="&#x101;" k="29" />
    <hkern u1="R" u2="&#x100;" k="36" />
    <hkern u1="R" u2="&#xff;" k="26" />
    <hkern u1="R" u2="&#xfd;" k="26" />
    <hkern u1="R" u2="&#xfc;" k="14" />
    <hkern u1="R" u2="&#xfb;" k="14" />
    <hkern u1="R" u2="&#xfa;" k="14" />
    <hkern u1="R" u2="&#xf9;" k="14" />
    <hkern u1="R" u2="&#xf8;" k="29" />
    <hkern u1="R" u2="&#xf6;" k="29" />
    <hkern u1="R" u2="&#xf5;" k="29" />
    <hkern u1="R" u2="&#xf4;" k="29" />
    <hkern u1="R" u2="&#xf3;" k="29" />
    <hkern u1="R" u2="&#xf2;" k="29" />
    <hkern u1="R" u2="&#xeb;" k="29" />
    <hkern u1="R" u2="&#xea;" k="29" />
    <hkern u1="R" u2="&#xe9;" k="29" />
    <hkern u1="R" u2="&#xe8;" k="29" />
    <hkern u1="R" u2="&#xe7;" k="29" />
    <hkern u1="R" u2="&#xe6;" k="29" />
    <hkern u1="R" u2="&#xe5;" k="29" />
    <hkern u1="R" u2="&#xe4;" k="29" />
    <hkern u1="R" u2="&#xe3;" k="29" />
    <hkern u1="R" u2="&#xe2;" k="29" />
    <hkern u1="R" u2="&#xe1;" k="29" />
    <hkern u1="R" u2="&#xe0;" k="29" />
    <hkern u1="R" u2="&#xde;" k="14" />
    <hkern u1="R" u2="&#xdd;" k="57" />
    <hkern u1="R" u2="&#xdc;" k="16" />
    <hkern u1="R" u2="&#xdb;" k="16" />
    <hkern u1="R" u2="&#xda;" k="16" />
    <hkern u1="R" u2="&#xd9;" k="16" />
    <hkern u1="R" u2="&#xd8;" k="24" />
    <hkern u1="R" u2="&#xd6;" k="24" />
    <hkern u1="R" u2="&#xd5;" k="24" />
    <hkern u1="R" u2="&#xd4;" k="24" />
    <hkern u1="R" u2="&#xd3;" k="24" />
    <hkern u1="R" u2="&#xd2;" k="24" />
    <hkern u1="R" u2="&#xd1;" k="14" />
    <hkern u1="R" u2="&#xd0;" k="14" />
    <hkern u1="R" u2="&#xcf;" k="14" />
    <hkern u1="R" u2="&#xce;" k="14" />
    <hkern u1="R" u2="&#xcd;" k="14" />
    <hkern u1="R" u2="&#xcc;" k="14" />
    <hkern u1="R" u2="&#xcb;" k="14" />
    <hkern u1="R" u2="&#xca;" k="14" />
    <hkern u1="R" u2="&#xc9;" k="14" />
    <hkern u1="R" u2="&#xc8;" k="14" />
    <hkern u1="R" u2="&#xc7;" k="24" />
    <hkern u1="R" u2="&#xc6;" k="36" />
    <hkern u1="R" u2="&#xc5;" k="36" />
    <hkern u1="R" u2="&#xc4;" k="36" />
    <hkern u1="R" u2="&#xc3;" k="36" />
    <hkern u1="R" u2="&#xc2;" k="36" />
    <hkern u1="R" u2="&#xc1;" k="36" />
    <hkern u1="R" u2="&#xc0;" k="36" />
    <hkern u1="R" u2="&#x7c;" k="14" />
    <hkern u1="R" u2="z" k="18" />
    <hkern u1="R" u2="y" k="26" />
    <hkern u1="R" u2="x" k="39" />
    <hkern u1="R" u2="w" k="23" />
    <hkern u1="R" u2="v" k="26" />
    <hkern u1="R" u2="u" k="14" />
    <hkern u1="R" u2="t" k="20" />
    <hkern u1="R" u2="s" k="28" />
    <hkern u1="R" u2="q" k="29" />
    <hkern u1="R" u2="o" k="29" />
    <hkern u1="R" u2="g" k="29" />
    <hkern u1="R" u2="f" k="20" />
    <hkern u1="R" u2="e" k="29" />
    <hkern u1="R" u2="d" k="29" />
    <hkern u1="R" u2="c" k="29" />
    <hkern u1="R" u2="a" k="29" />
    <hkern u1="R" u2="Z" k="29" />
    <hkern u1="R" u2="Y" k="57" />
    <hkern u1="R" u2="X" k="39" />
    <hkern u1="R" u2="W" k="28" />
    <hkern u1="R" u2="V" k="40" />
    <hkern u1="R" u2="U" k="16" />
    <hkern u1="R" u2="T" k="51" />
    <hkern u1="R" u2="S" k="35" />
    <hkern u1="R" u2="R" k="14" />
    <hkern u1="R" u2="Q" k="24" />
    <hkern u1="R" u2="P" k="14" />
    <hkern u1="R" u2="O" k="24" />
    <hkern u1="R" u2="N" k="14" />
    <hkern u1="R" u2="M" k="14" />
    <hkern u1="R" u2="L" k="14" />
    <hkern u1="R" u2="K" k="14" />
    <hkern u1="R" u2="J" k="29" />
    <hkern u1="R" u2="I" k="14" />
    <hkern u1="R" u2="H" k="14" />
    <hkern u1="R" u2="G" k="24" />
    <hkern u1="R" u2="F" k="14" />
    <hkern u1="R" u2="E" k="14" />
    <hkern u1="R" u2="D" k="14" />
    <hkern u1="R" u2="C" k="24" />
    <hkern u1="R" u2="B" k="14" />
    <hkern u1="R" u2="A" k="36" />
    <hkern u1="R" u2="&#x40;" k="29" />
    <hkern u1="R" u2="&#x2d;" k="60" />
    <hkern u1="R" u2="&#x27;" k="18" />
    <hkern u1="R" u2="&#x22;" k="18" />
    <hkern u1="S" g2="oe.ss01" k="7" />
    <hkern u1="S" g2="eogonek.ss01" k="7" />
    <hkern u1="S" g2="emacron.ss01" k="7" />
    <hkern u1="S" g2="egrave.ss01" k="7" />
    <hkern u1="S" g2="edotaccent.ss01" k="7" />
    <hkern u1="S" g2="edieresis.ss01" k="7" />
    <hkern u1="S" g2="ecircumflex.ss01" k="7" />
    <hkern u1="S" g2="ecaron.ss01" k="7" />
    <hkern u1="S" g2="eacute.ss01" k="7" />
    <hkern u1="S" g2="e.ss01" k="7" />
    <hkern u1="S" g2="ae.ss01" k="7" />
    <hkern u1="S" g2="T_l" k="32" />
    <hkern u1="S" g2="T_h" k="32" />
    <hkern u1="S" g2="T_T" k="32" />
    <hkern u1="S" u2="&#x1ef3;" k="18" />
    <hkern u1="S" u2="&#x1ef2;" k="40" />
    <hkern u1="S" u2="&#x21a;" k="32" />
    <hkern u1="S" u2="&#x1ce;" k="7" />
    <hkern u1="S" u2="&#x1cd;" k="22" />
    <hkern u1="S" u2="&#x178;" k="40" />
    <hkern u1="S" u2="&#x177;" k="18" />
    <hkern u1="S" u2="&#x176;" k="40" />
    <hkern u1="S" u2="&#x172;" k="-12" />
    <hkern u1="S" u2="&#x170;" k="-12" />
    <hkern u1="S" u2="&#x16e;" k="-12" />
    <hkern u1="S" u2="&#x16c;" k="-12" />
    <hkern u1="S" u2="&#x16a;" k="-12" />
    <hkern u1="S" u2="&#x164;" k="32" />
    <hkern u1="S" u2="&#x153;" k="7" />
    <hkern u1="S" u2="&#x152;" k="-3" />
    <hkern u1="S" u2="&#x151;" k="7" />
    <hkern u1="S" u2="&#x150;" k="-3" />
    <hkern u1="S" u2="&#x14d;" k="7" />
    <hkern u1="S" u2="&#x14c;" k="-3" />
    <hkern u1="S" u2="&#x123;" k="7" />
    <hkern u1="S" u2="&#x122;" k="-3" />
    <hkern u1="S" u2="&#x121;" k="7" />
    <hkern u1="S" u2="&#x120;" k="-3" />
    <hkern u1="S" u2="&#x11f;" k="7" />
    <hkern u1="S" u2="&#x11e;" k="-3" />
    <hkern u1="S" u2="&#x11b;" k="7" />
    <hkern u1="S" u2="&#x119;" k="7" />
    <hkern u1="S" u2="&#x117;" k="7" />
    <hkern u1="S" u2="&#x113;" k="7" />
    <hkern u1="S" u2="&#x111;" k="7" />
    <hkern u1="S" u2="&#x10f;" k="7" />
    <hkern u1="S" u2="&#x10d;" k="7" />
    <hkern u1="S" u2="&#x10c;" k="-3" />
    <hkern u1="S" u2="&#x10b;" k="7" />
    <hkern u1="S" u2="&#x10a;" k="-3" />
    <hkern u1="S" u2="&#x107;" k="7" />
    <hkern u1="S" u2="&#x106;" k="-3" />
    <hkern u1="S" u2="&#x105;" k="7" />
    <hkern u1="S" u2="&#x104;" k="22" />
    <hkern u1="S" u2="&#x103;" k="7" />
    <hkern u1="S" u2="&#x102;" k="22" />
    <hkern u1="S" u2="&#x101;" k="7" />
    <hkern u1="S" u2="&#x100;" k="22" />
    <hkern u1="S" u2="&#xff;" k="18" />
    <hkern u1="S" u2="&#xfd;" k="18" />
    <hkern u1="S" u2="&#xf8;" k="7" />
    <hkern u1="S" u2="&#xf6;" k="7" />
    <hkern u1="S" u2="&#xf5;" k="7" />
    <hkern u1="S" u2="&#xf4;" k="7" />
    <hkern u1="S" u2="&#xf3;" k="7" />
    <hkern u1="S" u2="&#xf2;" k="7" />
    <hkern u1="S" u2="&#xeb;" k="7" />
    <hkern u1="S" u2="&#xea;" k="7" />
    <hkern u1="S" u2="&#xe9;" k="7" />
    <hkern u1="S" u2="&#xe8;" k="7" />
    <hkern u1="S" u2="&#xe7;" k="7" />
    <hkern u1="S" u2="&#xe6;" k="7" />
    <hkern u1="S" u2="&#xe5;" k="7" />
    <hkern u1="S" u2="&#xe4;" k="7" />
    <hkern u1="S" u2="&#xe3;" k="7" />
    <hkern u1="S" u2="&#xe2;" k="7" />
    <hkern u1="S" u2="&#xe1;" k="7" />
    <hkern u1="S" u2="&#xe0;" k="7" />
    <hkern u1="S" u2="&#xdd;" k="40" />
    <hkern u1="S" u2="&#xdc;" k="-12" />
    <hkern u1="S" u2="&#xdb;" k="-12" />
    <hkern u1="S" u2="&#xda;" k="-12" />
    <hkern u1="S" u2="&#xd9;" k="-12" />
    <hkern u1="S" u2="&#xd8;" k="-3" />
    <hkern u1="S" u2="&#xd6;" k="-3" />
    <hkern u1="S" u2="&#xd5;" k="-3" />
    <hkern u1="S" u2="&#xd4;" k="-3" />
    <hkern u1="S" u2="&#xd3;" k="-3" />
    <hkern u1="S" u2="&#xd2;" k="-3" />
    <hkern u1="S" u2="&#xc7;" k="-3" />
    <hkern u1="S" u2="&#xc6;" k="22" />
    <hkern u1="S" u2="&#xc5;" k="22" />
    <hkern u1="S" u2="&#xc4;" k="22" />
    <hkern u1="S" u2="&#xc3;" k="22" />
    <hkern u1="S" u2="&#xc2;" k="22" />
    <hkern u1="S" u2="&#xc1;" k="22" />
    <hkern u1="S" u2="&#xc0;" k="22" />
    <hkern u1="S" u2="y" k="18" />
    <hkern u1="S" u2="x" k="15" />
    <hkern u1="S" u2="w" k="12" />
    <hkern u1="S" u2="v" k="18" />
    <hkern u1="S" u2="q" k="7" />
    <hkern u1="S" u2="o" k="7" />
    <hkern u1="S" u2="g" k="7" />
    <hkern u1="S" u2="e" k="7" />
    <hkern u1="S" u2="d" k="7" />
    <hkern u1="S" u2="c" k="7" />
    <hkern u1="S" u2="a" k="7" />
    <hkern u1="S" u2="Y" k="40" />
    <hkern u1="S" u2="X" k="16" />
    <hkern u1="S" u2="W" k="11" />
    <hkern u1="S" u2="V" k="17" />
    <hkern u1="S" u2="U" k="-12" />
    <hkern u1="S" u2="T" k="32" />
    <hkern u1="S" u2="S" k="18" />
    <hkern u1="S" u2="Q" k="-3" />
    <hkern u1="S" u2="O" k="-3" />
    <hkern u1="S" u2="J" k="40" />
    <hkern u1="S" u2="G" k="-3" />
    <hkern u1="S" u2="C" k="-3" />
    <hkern u1="S" u2="A" k="22" />
    <hkern u1="S" u2="&#x40;" k="7" />
    <hkern u1="S" u2="&#x2d;" k="-12" />
    <hkern u1="T" g2="g.ss01" k="118" />
    <hkern u1="T" u2="z" k="110" />
    <hkern u1="T" u2="x" k="128" />
    <hkern u1="T" u2="w" k="109" />
    <hkern u1="T" u2="s" k="120" />
    <hkern u1="T" u2="Z" k="40" />
    <hkern u1="T" u2="X" k="48" />
    <hkern u1="T" u2="W" k="30" />
    <hkern u1="T" u2="V" k="41" />
    <hkern u1="T" u2="S" k="43" />
    <hkern u1="T" u2="J" k="147" />
    <hkern u1="T" u2="&#x3f;" k="-10" />
    <hkern u1="T" u2="&#x2f;" k="48" />
    <hkern u1="T" u2="&#x2d;" k="78" />
    <hkern u1="T" u2="&#x26;" k="28" />
    <hkern u1="T" u2="&#x20;" k="16" />
    <hkern u1="U" g2="g.ss01" k="8" />
    <hkern u1="U" u2="X" k="18" />
    <hkern u1="U" u2="S" k="8" />
    <hkern u1="U" u2="J" k="48" />
    <hkern u1="U" u2="&#x2f;" k="17" />
    <hkern u1="V" g2="ampersand.ss01" k="36" />
    <hkern u1="V" g2="r_t" k="75" />
    <hkern u1="V" g2="r_f" k="75" />
    <hkern u1="V" g2="o_u_t_f_i_t_t_e_r" k="64" />
    <hkern u1="V" g2="o_u_t_f_i_t_t_e_d" k="64" />
    <hkern u1="V" g2="o_u_t_f_i_t" k="64" />
    <hkern u1="V" g2="f_t" k="46" />
    <hkern u1="V" g2="f_j" k="46" />
    <hkern u1="V" g2="f_f" k="46" />
    <hkern u1="V" g2="t.ss01" k="46" />
    <hkern u1="V" g2="oe.ss01" k="98" />
    <hkern u1="V" g2="eng.ss01" k="75" />
    <hkern u1="V" g2="ntilde.ss01" k="75" />
    <hkern u1="V" g2="uni0146.ss01" k="75" />
    <hkern u1="V" g2="ncaron.ss01" k="75" />
    <hkern u1="V" g2="nacute.ss01" k="75" />
    <hkern u1="V" g2="n.ss01" k="75" />
    <hkern u1="V" g2="m.ss01" k="75" />
    <hkern u1="V" g2="g.ss01" k="96" />
    <hkern u1="V" g2="eogonek.ss01" k="98" />
    <hkern u1="V" g2="emacron.ss01" k="98" />
    <hkern u1="V" g2="egrave.ss01" k="98" />
    <hkern u1="V" g2="edotaccent.ss01" k="98" />
    <hkern u1="V" g2="edieresis.ss01" k="98" />
    <hkern u1="V" g2="ecircumflex.ss01" k="98" />
    <hkern u1="V" g2="ecaron.ss01" k="98" />
    <hkern u1="V" g2="eacute.ss01" k="98" />
    <hkern u1="V" g2="e.ss01" k="98" />
    <hkern u1="V" g2="ae.ss01" k="98" />
    <hkern u1="V" g2="T_l" k="41" />
    <hkern u1="V" g2="T_h" k="41" />
    <hkern u1="V" g2="T_T" k="41" />
    <hkern u1="V" g2="fl" k="46" />
    <hkern u1="V" g2="fi" k="46" />
    <hkern u1="V" u2="&#x1ef3;" k="64" />
    <hkern u1="V" u2="&#x1ef2;" k="32" />
    <hkern u1="V" u2="&#x21b;" k="46" />
    <hkern u1="V" u2="&#x21a;" k="41" />
    <hkern u1="V" u2="&#x1ce;" k="98" />
    <hkern u1="V" u2="&#x1cd;" k="95" />
    <hkern u1="V" u2="&#x178;" k="32" />
    <hkern u1="V" u2="&#x177;" k="64" />
    <hkern u1="V" u2="&#x176;" k="32" />
    <hkern u1="V" u2="&#x173;" k="64" />
    <hkern u1="V" u2="&#x171;" k="64" />
    <hkern u1="V" u2="&#x16f;" k="64" />
    <hkern u1="V" u2="&#x16d;" k="64" />
    <hkern u1="V" u2="&#x16b;" k="64" />
    <hkern u1="V" u2="&#x165;" k="46" />
    <hkern u1="V" u2="&#x164;" k="41" />
    <hkern u1="V" u2="&#x159;" k="75" />
    <hkern u1="V" u2="&#x157;" k="75" />
    <hkern u1="V" u2="&#x155;" k="75" />
    <hkern u1="V" u2="&#x153;" k="98" />
    <hkern u1="V" u2="&#x152;" k="39" />
    <hkern u1="V" u2="&#x151;" k="98" />
    <hkern u1="V" u2="&#x150;" k="39" />
    <hkern u1="V" u2="&#x14d;" k="98" />
    <hkern u1="V" u2="&#x14c;" k="39" />
    <hkern u1="V" u2="&#x14b;" k="75" />
    <hkern u1="V" u2="&#x148;" k="75" />
    <hkern u1="V" u2="&#x146;" k="75" />
    <hkern u1="V" u2="&#x144;" k="75" />
    <hkern u1="V" u2="&#x123;" k="98" />
    <hkern u1="V" u2="&#x122;" k="39" />
    <hkern u1="V" u2="&#x121;" k="98" />
    <hkern u1="V" u2="&#x120;" k="39" />
    <hkern u1="V" u2="&#x11f;" k="98" />
    <hkern u1="V" u2="&#x11e;" k="39" />
    <hkern u1="V" u2="&#x11b;" k="98" />
    <hkern u1="V" u2="&#x119;" k="98" />
    <hkern u1="V" u2="&#x117;" k="98" />
    <hkern u1="V" u2="&#x113;" k="98" />
    <hkern u1="V" u2="&#x111;" k="98" />
    <hkern u1="V" u2="&#x10f;" k="98" />
    <hkern u1="V" u2="&#x10d;" k="98" />
    <hkern u1="V" u2="&#x10c;" k="39" />
    <hkern u1="V" u2="&#x10b;" k="98" />
    <hkern u1="V" u2="&#x10a;" k="39" />
    <hkern u1="V" u2="&#x107;" k="98" />
    <hkern u1="V" u2="&#x106;" k="39" />
    <hkern u1="V" u2="&#x105;" k="98" />
    <hkern u1="V" u2="&#x104;" k="95" />
    <hkern u1="V" u2="&#x103;" k="98" />
    <hkern u1="V" u2="&#x102;" k="95" />
    <hkern u1="V" u2="&#x101;" k="98" />
    <hkern u1="V" u2="&#x100;" k="95" />
    <hkern u1="V" u2="&#xff;" k="64" />
    <hkern u1="V" u2="&#xfe;" k="75" />
    <hkern u1="V" u2="&#xfd;" k="64" />
    <hkern u1="V" u2="&#xfc;" k="64" />
    <hkern u1="V" u2="&#xfb;" k="64" />
    <hkern u1="V" u2="&#xfa;" k="64" />
    <hkern u1="V" u2="&#xf9;" k="64" />
    <hkern u1="V" u2="&#xf8;" k="98" />
    <hkern u1="V" u2="&#xf6;" k="98" />
    <hkern u1="V" u2="&#xf5;" k="98" />
    <hkern u1="V" u2="&#xf4;" k="98" />
    <hkern u1="V" u2="&#xf3;" k="98" />
    <hkern u1="V" u2="&#xf2;" k="98" />
    <hkern u1="V" u2="&#xf1;" k="75" />
    <hkern u1="V" u2="&#xeb;" k="98" />
    <hkern u1="V" u2="&#xea;" k="98" />
    <hkern u1="V" u2="&#xe9;" k="98" />
    <hkern u1="V" u2="&#xe8;" k="98" />
    <hkern u1="V" u2="&#xe7;" k="98" />
    <hkern u1="V" u2="&#xe6;" k="98" />
    <hkern u1="V" u2="&#xe5;" k="98" />
    <hkern u1="V" u2="&#xe4;" k="98" />
    <hkern u1="V" u2="&#xe3;" k="98" />
    <hkern u1="V" u2="&#xe2;" k="98" />
    <hkern u1="V" u2="&#xe1;" k="98" />
    <hkern u1="V" u2="&#xe0;" k="98" />
    <hkern u1="V" u2="&#xdd;" k="32" />
    <hkern u1="V" u2="&#xd8;" k="39" />
    <hkern u1="V" u2="&#xd6;" k="39" />
    <hkern u1="V" u2="&#xd5;" k="39" />
    <hkern u1="V" u2="&#xd4;" k="39" />
    <hkern u1="V" u2="&#xd3;" k="39" />
    <hkern u1="V" u2="&#xd2;" k="39" />
    <hkern u1="V" u2="&#xc7;" k="39" />
    <hkern u1="V" u2="&#xc6;" k="95" />
    <hkern u1="V" u2="&#xc5;" k="95" />
    <hkern u1="V" u2="&#xc4;" k="95" />
    <hkern u1="V" u2="&#xc3;" k="95" />
    <hkern u1="V" u2="&#xc2;" k="95" />
    <hkern u1="V" u2="&#xc1;" k="95" />
    <hkern u1="V" u2="&#xc0;" k="95" />
    <hkern u1="V" u2="z" k="74" />
    <hkern u1="V" u2="y" k="64" />
    <hkern u1="V" u2="x" k="104" />
    <hkern u1="V" u2="w" k="54" />
    <hkern u1="V" u2="v" k="64" />
    <hkern u1="V" u2="u" k="64" />
    <hkern u1="V" u2="t" k="46" />
    <hkern u1="V" u2="s" k="96" />
    <hkern u1="V" u2="r" k="75" />
    <hkern u1="V" u2="q" k="98" />
    <hkern u1="V" u2="p" k="75" />
    <hkern u1="V" u2="o" k="104" />
    <hkern u1="V" u2="n" k="75" />
    <hkern u1="V" u2="m" k="75" />
    <hkern u1="V" u2="g" k="98" />
    <hkern u1="V" u2="f" k="46" />
    <hkern u1="V" u2="e" k="98" />
    <hkern u1="V" u2="d" k="98" />
    <hkern u1="V" u2="c" k="98" />
    <hkern u1="V" u2="a" k="98" />
    <hkern u1="V" u2="Z" k="24" />
    <hkern u1="V" u2="Y" k="32" />
    <hkern u1="V" u2="X" k="36" />
    <hkern u1="V" u2="W" k="28" />
    <hkern u1="V" u2="V" k="34" />
    <hkern u1="V" u2="T" k="41" />
    <hkern u1="V" u2="S" k="46" />
    <hkern u1="V" u2="Q" k="39" />
    <hkern u1="V" u2="O" k="39" />
    <hkern u1="V" u2="J" k="126" />
    <hkern u1="V" u2="G" k="39" />
    <hkern u1="V" u2="C" k="39" />
    <hkern u1="V" u2="A" k="95" />
    <hkern u1="V" u2="&#x40;" k="98" />
    <hkern u1="V" u2="&#x3f;" k="-6" />
    <hkern u1="V" u2="&#x3b;" k="65" />
    <hkern u1="V" u2="&#x3a;" k="65" />
    <hkern u1="V" u2="&#x2f;" k="32" />
    <hkern u1="V" u2="&#x2e;" k="79" />
    <hkern u1="V" u2="&#x2d;" k="68" />
    <hkern u1="V" u2="&#x2c;" k="79" />
    <hkern u1="V" u2="&#x26;" k="28" />
    <hkern u1="W" g2="ampersand.ss01" k="26" />
    <hkern u1="W" g2="r_t" k="64" />
    <hkern u1="W" g2="r_f" k="64" />
    <hkern u1="W" g2="o_u_t_f_i_t_t_e_r" k="52" />
    <hkern u1="W" g2="o_u_t_f_i_t_t_e_d" k="52" />
    <hkern u1="W" g2="o_u_t_f_i_t" k="52" />
    <hkern u1="W" g2="f_t" k="36" />
    <hkern u1="W" g2="f_j" k="36" />
    <hkern u1="W" g2="f_f" k="36" />
    <hkern u1="W" g2="t.ss01" k="36" />
    <hkern u1="W" g2="oe.ss01" k="73" />
    <hkern u1="W" g2="eng.ss01" k="64" />
    <hkern u1="W" g2="ntilde.ss01" k="64" />
    <hkern u1="W" g2="uni0146.ss01" k="64" />
    <hkern u1="W" g2="ncaron.ss01" k="64" />
    <hkern u1="W" g2="nacute.ss01" k="64" />
    <hkern u1="W" g2="n.ss01" k="64" />
    <hkern u1="W" g2="m.ss01" k="64" />
    <hkern u1="W" g2="g.ss01" k="68" />
    <hkern u1="W" g2="eogonek.ss01" k="73" />
    <hkern u1="W" g2="emacron.ss01" k="73" />
    <hkern u1="W" g2="egrave.ss01" k="73" />
    <hkern u1="W" g2="edotaccent.ss01" k="73" />
    <hkern u1="W" g2="edieresis.ss01" k="73" />
    <hkern u1="W" g2="ecircumflex.ss01" k="73" />
    <hkern u1="W" g2="ecaron.ss01" k="73" />
    <hkern u1="W" g2="eacute.ss01" k="73" />
    <hkern u1="W" g2="e.ss01" k="73" />
    <hkern u1="W" g2="ae.ss01" k="73" />
    <hkern u1="W" g2="T_l" k="30" />
    <hkern u1="W" g2="T_h" k="30" />
    <hkern u1="W" g2="T_T" k="30" />
    <hkern u1="W" g2="fl" k="36" />
    <hkern u1="W" g2="fi" k="36" />
    <hkern u1="W" u2="&#x1ef3;" k="58" />
    <hkern u1="W" u2="&#x1ef2;" k="36" />
    <hkern u1="W" u2="&#x21b;" k="36" />
    <hkern u1="W" u2="&#x21a;" k="30" />
    <hkern u1="W" u2="&#x1ce;" k="73" />
    <hkern u1="W" u2="&#x178;" k="36" />
    <hkern u1="W" u2="&#x177;" k="58" />
    <hkern u1="W" u2="&#x176;" k="36" />
    <hkern u1="W" u2="&#x173;" k="52" />
    <hkern u1="W" u2="&#x171;" k="52" />
    <hkern u1="W" u2="&#x16f;" k="52" />
    <hkern u1="W" u2="&#x16d;" k="52" />
    <hkern u1="W" u2="&#x16b;" k="52" />
    <hkern u1="W" u2="&#x165;" k="36" />
    <hkern u1="W" u2="&#x164;" k="30" />
    <hkern u1="W" u2="&#x159;" k="64" />
    <hkern u1="W" u2="&#x157;" k="64" />
    <hkern u1="W" u2="&#x155;" k="64" />
    <hkern u1="W" u2="&#x153;" k="73" />
    <hkern u1="W" u2="&#x152;" k="32" />
    <hkern u1="W" u2="&#x151;" k="73" />
    <hkern u1="W" u2="&#x150;" k="32" />
    <hkern u1="W" u2="&#x14d;" k="73" />
    <hkern u1="W" u2="&#x14c;" k="32" />
    <hkern u1="W" u2="&#x14b;" k="64" />
    <hkern u1="W" u2="&#x148;" k="64" />
    <hkern u1="W" u2="&#x146;" k="64" />
    <hkern u1="W" u2="&#x144;" k="64" />
    <hkern u1="W" u2="&#x123;" k="73" />
    <hkern u1="W" u2="&#x122;" k="32" />
    <hkern u1="W" u2="&#x121;" k="73" />
    <hkern u1="W" u2="&#x120;" k="32" />
    <hkern u1="W" u2="&#x11f;" k="73" />
    <hkern u1="W" u2="&#x11e;" k="32" />
    <hkern u1="W" u2="&#x11b;" k="73" />
    <hkern u1="W" u2="&#x119;" k="73" />
    <hkern u1="W" u2="&#x117;" k="73" />
    <hkern u1="W" u2="&#x113;" k="73" />
    <hkern u1="W" u2="&#x111;" k="73" />
    <hkern u1="W" u2="&#x10f;" k="73" />
    <hkern u1="W" u2="&#x10d;" k="73" />
    <hkern u1="W" u2="&#x10c;" k="32" />
    <hkern u1="W" u2="&#x10b;" k="73" />
    <hkern u1="W" u2="&#x10a;" k="32" />
    <hkern u1="W" u2="&#x107;" k="73" />
    <hkern u1="W" u2="&#x106;" k="32" />
    <hkern u1="W" u2="&#x105;" k="73" />
    <hkern u1="W" u2="&#x103;" k="73" />
    <hkern u1="W" u2="&#x101;" k="73" />
    <hkern u1="W" u2="&#xff;" k="58" />
    <hkern u1="W" u2="&#xfe;" k="64" />
    <hkern u1="W" u2="&#xfd;" k="58" />
    <hkern u1="W" u2="&#xfc;" k="52" />
    <hkern u1="W" u2="&#xfb;" k="52" />
    <hkern u1="W" u2="&#xfa;" k="52" />
    <hkern u1="W" u2="&#xf9;" k="52" />
    <hkern u1="W" u2="&#xf8;" k="73" />
    <hkern u1="W" u2="&#xf6;" k="73" />
    <hkern u1="W" u2="&#xf5;" k="73" />
    <hkern u1="W" u2="&#xf4;" k="73" />
    <hkern u1="W" u2="&#xf3;" k="73" />
    <hkern u1="W" u2="&#xf2;" k="73" />
    <hkern u1="W" u2="&#xf1;" k="64" />
    <hkern u1="W" u2="&#xeb;" k="73" />
    <hkern u1="W" u2="&#xea;" k="73" />
    <hkern u1="W" u2="&#xe9;" k="73" />
    <hkern u1="W" u2="&#xe8;" k="73" />
    <hkern u1="W" u2="&#xe7;" k="73" />
    <hkern u1="W" u2="&#xe6;" k="73" />
    <hkern u1="W" u2="&#xe5;" k="73" />
    <hkern u1="W" u2="&#xe4;" k="73" />
    <hkern u1="W" u2="&#xe3;" k="73" />
    <hkern u1="W" u2="&#xe2;" k="73" />
    <hkern u1="W" u2="&#xe1;" k="73" />
    <hkern u1="W" u2="&#xe0;" k="73" />
    <hkern u1="W" u2="&#xdd;" k="36" />
    <hkern u1="W" u2="&#xd8;" k="32" />
    <hkern u1="W" u2="&#xd6;" k="32" />
    <hkern u1="W" u2="&#xd5;" k="32" />
    <hkern u1="W" u2="&#xd4;" k="32" />
    <hkern u1="W" u2="&#xd3;" k="32" />
    <hkern u1="W" u2="&#xd2;" k="32" />
    <hkern u1="W" u2="&#xc7;" k="32" />
    <hkern u1="W" u2="z" k="58" />
    <hkern u1="W" u2="y" k="58" />
    <hkern u1="W" u2="x" k="62" />
    <hkern u1="W" u2="w" k="48" />
    <hkern u1="W" u2="v" k="58" />
    <hkern u1="W" u2="u" k="52" />
    <hkern u1="W" u2="t" k="36" />
    <hkern u1="W" u2="s" k="76" />
    <hkern u1="W" u2="r" k="64" />
    <hkern u1="W" u2="q" k="73" />
    <hkern u1="W" u2="p" k="64" />
    <hkern u1="W" u2="o" k="73" />
    <hkern u1="W" u2="n" k="64" />
    <hkern u1="W" u2="m" k="64" />
    <hkern u1="W" u2="g" k="73" />
    <hkern u1="W" u2="f" k="36" />
    <hkern u1="W" u2="e" k="73" />
    <hkern u1="W" u2="d" k="73" />
    <hkern u1="W" u2="c" k="73" />
    <hkern u1="W" u2="a" k="73" />
    <hkern u1="W" u2="Z" k="18" />
    <hkern u1="W" u2="Y" k="36" />
    <hkern u1="W" u2="X" k="28" />
    <hkern u1="W" u2="W" k="28" />
    <hkern u1="W" u2="V" k="28" />
    <hkern u1="W" u2="T" k="30" />
    <hkern u1="W" u2="S" k="33" />
    <hkern u1="W" u2="Q" k="32" />
    <hkern u1="W" u2="O" k="32" />
    <hkern u1="W" u2="J" k="110" />
    <hkern u1="W" u2="G" k="32" />
    <hkern u1="W" u2="C" k="32" />
    <hkern u1="W" u2="&#x40;" k="73" />
    <hkern u1="W" u2="&#x3f;" k="-6" />
    <hkern u1="W" u2="&#x3b;" k="34" />
    <hkern u1="W" u2="&#x3a;" k="34" />
    <hkern u1="W" u2="&#x2f;" k="32" />
    <hkern u1="W" u2="&#x2e;" k="56" />
    <hkern u1="W" u2="&#x2d;" k="43" />
    <hkern u1="W" u2="&#x2c;" k="56" />
    <hkern u1="W" u2="&#x26;" k="28" />
    <hkern u1="X" g2="ampersand.ss01" k="14" />
    <hkern u1="X" g2="r_t" k="24" />
    <hkern u1="X" g2="r_f" k="24" />
    <hkern u1="X" g2="o_u_t_f_i_t_t_e_r" k="32" />
    <hkern u1="X" g2="o_u_t_f_i_t_t_e_d" k="32" />
    <hkern u1="X" g2="o_u_t_f_i_t" k="32" />
    <hkern u1="X" g2="f_t" k="54" />
    <hkern u1="X" g2="f_j" k="54" />
    <hkern u1="X" g2="f_f" k="54" />
    <hkern u1="X" g2="t.ss01" k="54" />
    <hkern u1="X" g2="oe.ss01" k="44" />
    <hkern u1="X" g2="eng.ss01" k="24" />
    <hkern u1="X" g2="ntilde.ss01" k="24" />
    <hkern u1="X" g2="uni0146.ss01" k="24" />
    <hkern u1="X" g2="ncaron.ss01" k="24" />
    <hkern u1="X" g2="nacute.ss01" k="24" />
    <hkern u1="X" g2="n.ss01" k="24" />
    <hkern u1="X" g2="m.ss01" k="24" />
    <hkern u1="X" g2="uni0137.ss01" k="18" />
    <hkern u1="X" g2="k.ss01" k="18" />
    <hkern u1="X" g2="g.ss01" k="26" />
    <hkern u1="X" g2="eogonek.ss01" k="44" />
    <hkern u1="X" g2="emacron.ss01" k="44" />
    <hkern u1="X" g2="egrave.ss01" k="44" />
    <hkern u1="X" g2="edotaccent.ss01" k="44" />
    <hkern u1="X" g2="edieresis.ss01" k="44" />
    <hkern u1="X" g2="ecircumflex.ss01" k="44" />
    <hkern u1="X" g2="ecaron.ss01" k="44" />
    <hkern u1="X" g2="eacute.ss01" k="44" />
    <hkern u1="X" g2="e.ss01" k="44" />
    <hkern u1="X" g2="ae.ss01" k="44" />
    <hkern u1="X" g2="T_l" k="48" />
    <hkern u1="X" g2="T_h" k="48" />
    <hkern u1="X" g2="T_T" k="48" />
    <hkern u1="X" g2="fl" k="54" />
    <hkern u1="X" g2="fi" k="54" />
    <hkern u1="X" u2="&#x201d;" k="24" />
    <hkern u1="X" u2="&#x2019;" k="24" />
    <hkern u1="X" u2="&#x1ef3;" k="58" />
    <hkern u1="X" u2="&#x1ef2;" k="42" />
    <hkern u1="X" u2="&#x2bc;" k="24" />
    <hkern u1="X" u2="&#x21b;" k="54" />
    <hkern u1="X" u2="&#x21a;" k="48" />
    <hkern u1="X" u2="&#x1ce;" k="44" />
    <hkern u1="X" u2="&#x1cd;" k="38" />
    <hkern u1="X" u2="&#x178;" k="42" />
    <hkern u1="X" u2="&#x177;" k="58" />
    <hkern u1="X" u2="&#x176;" k="42" />
    <hkern u1="X" u2="&#x173;" k="32" />
    <hkern u1="X" u2="&#x172;" k="18" />
    <hkern u1="X" u2="&#x171;" k="32" />
    <hkern u1="X" u2="&#x170;" k="18" />
    <hkern u1="X" u2="&#x16f;" k="32" />
    <hkern u1="X" u2="&#x16e;" k="18" />
    <hkern u1="X" u2="&#x16d;" k="32" />
    <hkern u1="X" u2="&#x16c;" k="18" />
    <hkern u1="X" u2="&#x16b;" k="32" />
    <hkern u1="X" u2="&#x16a;" k="18" />
    <hkern u1="X" u2="&#x165;" k="54" />
    <hkern u1="X" u2="&#x164;" k="48" />
    <hkern u1="X" u2="&#x159;" k="24" />
    <hkern u1="X" u2="&#x158;" k="14" />
    <hkern u1="X" u2="&#x157;" k="24" />
    <hkern u1="X" u2="&#x156;" k="14" />
    <hkern u1="X" u2="&#x155;" k="24" />
    <hkern u1="X" u2="&#x154;" k="14" />
    <hkern u1="X" u2="&#x153;" k="44" />
    <hkern u1="X" u2="&#x152;" k="41" />
    <hkern u1="X" u2="&#x151;" k="44" />
    <hkern u1="X" u2="&#x150;" k="41" />
    <hkern u1="X" u2="&#x14d;" k="44" />
    <hkern u1="X" u2="&#x14c;" k="41" />
    <hkern u1="X" u2="&#x14b;" k="24" />
    <hkern u1="X" u2="&#x14a;" k="14" />
    <hkern u1="X" u2="&#x148;" k="24" />
    <hkern u1="X" u2="&#x147;" k="14" />
    <hkern u1="X" u2="&#x146;" k="24" />
    <hkern u1="X" u2="&#x145;" k="14" />
    <hkern u1="X" u2="&#x144;" k="24" />
    <hkern u1="X" u2="&#x143;" k="14" />
    <hkern u1="X" u2="&#x142;" k="18" />
    <hkern u1="X" u2="&#x141;" k="14" />
    <hkern u1="X" u2="&#x13e;" k="18" />
    <hkern u1="X" u2="&#x13d;" k="14" />
    <hkern u1="X" u2="&#x13c;" k="18" />
    <hkern u1="X" u2="&#x13b;" k="14" />
    <hkern u1="X" u2="&#x13a;" k="18" />
    <hkern u1="X" u2="&#x139;" k="14" />
    <hkern u1="X" u2="&#x137;" k="18" />
    <hkern u1="X" u2="&#x136;" k="14" />
    <hkern u1="X" u2="&#x130;" k="14" />
    <hkern u1="X" u2="&#x12e;" k="14" />
    <hkern u1="X" u2="&#x12a;" k="14" />
    <hkern u1="X" u2="&#x123;" k="44" />
    <hkern u1="X" u2="&#x122;" k="41" />
    <hkern u1="X" u2="&#x121;" k="44" />
    <hkern u1="X" u2="&#x120;" k="41" />
    <hkern u1="X" u2="&#x11f;" k="44" />
    <hkern u1="X" u2="&#x11e;" k="41" />
    <hkern u1="X" u2="&#x11b;" k="44" />
    <hkern u1="X" u2="&#x11a;" k="14" />
    <hkern u1="X" u2="&#x119;" k="44" />
    <hkern u1="X" u2="&#x118;" k="14" />
    <hkern u1="X" u2="&#x117;" k="44" />
    <hkern u1="X" u2="&#x116;" k="14" />
    <hkern u1="X" u2="&#x113;" k="44" />
    <hkern u1="X" u2="&#x112;" k="14" />
    <hkern u1="X" u2="&#x111;" k="44" />
    <hkern u1="X" u2="&#x110;" k="14" />
    <hkern u1="X" u2="&#x10f;" k="44" />
    <hkern u1="X" u2="&#x10e;" k="14" />
    <hkern u1="X" u2="&#x10d;" k="44" />
    <hkern u1="X" u2="&#x10c;" k="41" />
    <hkern u1="X" u2="&#x10b;" k="44" />
    <hkern u1="X" u2="&#x10a;" k="41" />
    <hkern u1="X" u2="&#x107;" k="44" />
    <hkern u1="X" u2="&#x106;" k="41" />
    <hkern u1="X" u2="&#x105;" k="44" />
    <hkern u1="X" u2="&#x104;" k="38" />
    <hkern u1="X" u2="&#x103;" k="44" />
    <hkern u1="X" u2="&#x102;" k="38" />
    <hkern u1="X" u2="&#x101;" k="44" />
    <hkern u1="X" u2="&#x100;" k="38" />
    <hkern u1="X" u2="&#xff;" k="58" />
    <hkern u1="X" u2="&#xfe;" k="24" />
    <hkern u1="X" u2="&#xfd;" k="58" />
    <hkern u1="X" u2="&#xfc;" k="32" />
    <hkern u1="X" u2="&#xfb;" k="32" />
    <hkern u1="X" u2="&#xfa;" k="32" />
    <hkern u1="X" u2="&#xf9;" k="32" />
    <hkern u1="X" u2="&#xf8;" k="44" />
    <hkern u1="X" u2="&#xf6;" k="44" />
    <hkern u1="X" u2="&#xf5;" k="44" />
    <hkern u1="X" u2="&#xf4;" k="44" />
    <hkern u1="X" u2="&#xf3;" k="44" />
    <hkern u1="X" u2="&#xf2;" k="44" />
    <hkern u1="X" u2="&#xf1;" k="24" />
    <hkern u1="X" u2="&#xeb;" k="44" />
    <hkern u1="X" u2="&#xea;" k="44" />
    <hkern u1="X" u2="&#xe9;" k="44" />
    <hkern u1="X" u2="&#xe8;" k="44" />
    <hkern u1="X" u2="&#xe7;" k="44" />
    <hkern u1="X" u2="&#xe6;" k="44" />
    <hkern u1="X" u2="&#xe5;" k="44" />
    <hkern u1="X" u2="&#xe4;" k="44" />
    <hkern u1="X" u2="&#xe3;" k="44" />
    <hkern u1="X" u2="&#xe2;" k="44" />
    <hkern u1="X" u2="&#xe1;" k="44" />
    <hkern u1="X" u2="&#xe0;" k="44" />
    <hkern u1="X" u2="&#xde;" k="14" />
    <hkern u1="X" u2="&#xdd;" k="42" />
    <hkern u1="X" u2="&#xdc;" k="18" />
    <hkern u1="X" u2="&#xdb;" k="18" />
    <hkern u1="X" u2="&#xda;" k="18" />
    <hkern u1="X" u2="&#xd9;" k="18" />
    <hkern u1="X" u2="&#xd8;" k="41" />
    <hkern u1="X" u2="&#xd6;" k="41" />
    <hkern u1="X" u2="&#xd5;" k="41" />
    <hkern u1="X" u2="&#xd4;" k="41" />
    <hkern u1="X" u2="&#xd3;" k="41" />
    <hkern u1="X" u2="&#xd2;" k="41" />
    <hkern u1="X" u2="&#xd1;" k="14" />
    <hkern u1="X" u2="&#xd0;" k="14" />
    <hkern u1="X" u2="&#xcf;" k="14" />
    <hkern u1="X" u2="&#xce;" k="14" />
    <hkern u1="X" u2="&#xcd;" k="14" />
    <hkern u1="X" u2="&#xcc;" k="14" />
    <hkern u1="X" u2="&#xcb;" k="14" />
    <hkern u1="X" u2="&#xca;" k="14" />
    <hkern u1="X" u2="&#xc9;" k="14" />
    <hkern u1="X" u2="&#xc8;" k="14" />
    <hkern u1="X" u2="&#xc7;" k="41" />
    <hkern u1="X" u2="&#xc6;" k="38" />
    <hkern u1="X" u2="&#xc5;" k="38" />
    <hkern u1="X" u2="&#xc4;" k="38" />
    <hkern u1="X" u2="&#xc3;" k="38" />
    <hkern u1="X" u2="&#xc2;" k="38" />
    <hkern u1="X" u2="&#xc1;" k="38" />
    <hkern u1="X" u2="&#xc0;" k="38" />
    <hkern u1="X" u2="&#x7c;" k="14" />
    <hkern u1="X" u2="z" k="24" />
    <hkern u1="X" u2="y" k="58" />
    <hkern u1="X" u2="x" k="38" />
    <hkern u1="X" u2="w" k="52" />
    <hkern u1="X" u2="v" k="58" />
    <hkern u1="X" u2="u" k="32" />
    <hkern u1="X" u2="t" k="54" />
    <hkern u1="X" u2="s" k="24" />
    <hkern u1="X" u2="r" k="24" />
    <hkern u1="X" u2="q" k="44" />
    <hkern u1="X" u2="p" k="24" />
    <hkern u1="X" u2="o" k="44" />
    <hkern u1="X" u2="n" k="24" />
    <hkern u1="X" u2="m" k="24" />
    <hkern u1="X" u2="l" k="18" />
    <hkern u1="X" u2="k" k="18" />
    <hkern u1="X" u2="h" k="18" />
    <hkern u1="X" u2="g" k="44" />
    <hkern u1="X" u2="f" k="54" />
    <hkern u1="X" u2="e" k="44" />
    <hkern u1="X" u2="d" k="44" />
    <hkern u1="X" u2="c" k="44" />
    <hkern u1="X" u2="b" k="18" />
    <hkern u1="X" u2="a" k="44" />
    <hkern u1="X" u2="Z" k="18" />
    <hkern u1="X" u2="Y" k="42" />
    <hkern u1="X" u2="X" k="46" />
    <hkern u1="X" u2="W" k="28" />
    <hkern u1="X" u2="V" k="36" />
    <hkern u1="X" u2="U" k="18" />
    <hkern u1="X" u2="T" k="48" />
    <hkern u1="X" u2="S" k="40" />
    <hkern u1="X" u2="R" k="14" />
    <hkern u1="X" u2="Q" k="41" />
    <hkern u1="X" u2="P" k="14" />
    <hkern u1="X" u2="O" k="41" />
    <hkern u1="X" u2="N" k="14" />
    <hkern u1="X" u2="M" k="14" />
    <hkern u1="X" u2="L" k="14" />
    <hkern u1="X" u2="K" k="14" />
    <hkern u1="X" u2="J" k="38" />
    <hkern u1="X" u2="I" k="14" />
    <hkern u1="X" u2="H" k="14" />
    <hkern u1="X" u2="G" k="41" />
    <hkern u1="X" u2="F" k="14" />
    <hkern u1="X" u2="E" k="14" />
    <hkern u1="X" u2="D" k="14" />
    <hkern u1="X" u2="C" k="41" />
    <hkern u1="X" u2="B" k="14" />
    <hkern u1="X" u2="A" k="38" />
    <hkern u1="X" u2="&#x40;" k="44" />
    <hkern u1="X" u2="&#x2d;" k="61" />
    <hkern u1="X" u2="&#x26;" k="8" />
    <hkern u1="Y" g2="ampersand.ss01" k="56" />
    <hkern u1="Y" g2="g.ss01" k="118" />
    <hkern u1="Y" u2="z" k="96" />
    <hkern u1="Y" u2="x" k="118" />
    <hkern u1="Y" u2="w" k="91" />
    <hkern u1="Y" u2="s" k="139" />
    <hkern u1="Y" u2="j" k="44" />
    <hkern u1="Y" u2="i" k="38" />
    <hkern u1="Y" u2="Z" k="22" />
    <hkern u1="Y" u2="Y" k="42" />
    <hkern u1="Y" u2="X" k="42" />
    <hkern u1="Y" u2="W" k="36" />
    <hkern u1="Y" u2="V" k="32" />
    <hkern u1="Y" u2="U" k="12" />
    <hkern u1="Y" u2="S" k="70" />
    <hkern u1="Y" u2="J" k="145" />
    <hkern u1="Y" u2="&#x3f;" k="16" />
    <hkern u1="Y" u2="&#x2f;" k="68" />
    <hkern u1="Y" u2="&#x2d;" k="85" />
    <hkern u1="Y" u2="&#x26;" k="61" />
    <hkern u1="Y" u2="&#x20;" k="28" />
    <hkern u1="Z" g2="f_t" k="31" />
    <hkern u1="Z" g2="f_j" k="31" />
    <hkern u1="Z" g2="f_f" k="31" />
    <hkern u1="Z" g2="t.ss01" k="31" />
    <hkern u1="Z" g2="oe.ss01" k="30" />
    <hkern u1="Z" g2="eogonek.ss01" k="30" />
    <hkern u1="Z" g2="emacron.ss01" k="30" />
    <hkern u1="Z" g2="egrave.ss01" k="30" />
    <hkern u1="Z" g2="edotaccent.ss01" k="30" />
    <hkern u1="Z" g2="edieresis.ss01" k="30" />
    <hkern u1="Z" g2="ecircumflex.ss01" k="30" />
    <hkern u1="Z" g2="ecaron.ss01" k="30" />
    <hkern u1="Z" g2="eacute.ss01" k="30" />
    <hkern u1="Z" g2="e.ss01" k="30" />
    <hkern u1="Z" g2="ae.ss01" k="30" />
    <hkern u1="Z" g2="T_l" k="24" />
    <hkern u1="Z" g2="T_h" k="24" />
    <hkern u1="Z" g2="T_T" k="24" />
    <hkern u1="Z" g2="fl" k="31" />
    <hkern u1="Z" g2="fi" k="31" />
    <hkern u1="Z" u2="&#x1ef3;" k="28" />
    <hkern u1="Z" u2="&#x1ef2;" k="22" />
    <hkern u1="Z" u2="&#x21b;" k="31" />
    <hkern u1="Z" u2="&#x21a;" k="24" />
    <hkern u1="Z" u2="&#x1ce;" k="30" />
    <hkern u1="Z" u2="&#x1cd;" k="28" />
    <hkern u1="Z" u2="&#x178;" k="22" />
    <hkern u1="Z" u2="&#x177;" k="28" />
    <hkern u1="Z" u2="&#x176;" k="22" />
    <hkern u1="Z" u2="&#x165;" k="31" />
    <hkern u1="Z" u2="&#x164;" k="24" />
    <hkern u1="Z" u2="&#x153;" k="30" />
    <hkern u1="Z" u2="&#x152;" k="22" />
    <hkern u1="Z" u2="&#x151;" k="30" />
    <hkern u1="Z" u2="&#x150;" k="22" />
    <hkern u1="Z" u2="&#x14d;" k="30" />
    <hkern u1="Z" u2="&#x14c;" k="22" />
    <hkern u1="Z" u2="&#x123;" k="30" />
    <hkern u1="Z" u2="&#x122;" k="22" />
    <hkern u1="Z" u2="&#x121;" k="30" />
    <hkern u1="Z" u2="&#x120;" k="22" />
    <hkern u1="Z" u2="&#x11f;" k="30" />
    <hkern u1="Z" u2="&#x11e;" k="22" />
    <hkern u1="Z" u2="&#x11b;" k="30" />
    <hkern u1="Z" u2="&#x119;" k="30" />
    <hkern u1="Z" u2="&#x117;" k="30" />
    <hkern u1="Z" u2="&#x113;" k="30" />
    <hkern u1="Z" u2="&#x111;" k="30" />
    <hkern u1="Z" u2="&#x10f;" k="30" />
    <hkern u1="Z" u2="&#x10d;" k="30" />
    <hkern u1="Z" u2="&#x10c;" k="22" />
    <hkern u1="Z" u2="&#x10b;" k="30" />
    <hkern u1="Z" u2="&#x10a;" k="22" />
    <hkern u1="Z" u2="&#x107;" k="30" />
    <hkern u1="Z" u2="&#x106;" k="22" />
    <hkern u1="Z" u2="&#x105;" k="30" />
    <hkern u1="Z" u2="&#x104;" k="28" />
    <hkern u1="Z" u2="&#x103;" k="30" />
    <hkern u1="Z" u2="&#x102;" k="28" />
    <hkern u1="Z" u2="&#x101;" k="30" />
    <hkern u1="Z" u2="&#x100;" k="28" />
    <hkern u1="Z" u2="&#xff;" k="28" />
    <hkern u1="Z" u2="&#xfd;" k="28" />
    <hkern u1="Z" u2="&#xf8;" k="30" />
    <hkern u1="Z" u2="&#xf6;" k="30" />
    <hkern u1="Z" u2="&#xf5;" k="30" />
    <hkern u1="Z" u2="&#xf4;" k="30" />
    <hkern u1="Z" u2="&#xf3;" k="30" />
    <hkern u1="Z" u2="&#xf2;" k="30" />
    <hkern u1="Z" u2="&#xeb;" k="30" />
    <hkern u1="Z" u2="&#xea;" k="30" />
    <hkern u1="Z" u2="&#xe9;" k="30" />
    <hkern u1="Z" u2="&#xe8;" k="30" />
    <hkern u1="Z" u2="&#xe7;" k="30" />
    <hkern u1="Z" u2="&#xe6;" k="30" />
    <hkern u1="Z" u2="&#xe5;" k="30" />
    <hkern u1="Z" u2="&#xe4;" k="30" />
    <hkern u1="Z" u2="&#xe3;" k="30" />
    <hkern u1="Z" u2="&#xe2;" k="30" />
    <hkern u1="Z" u2="&#xe1;" k="30" />
    <hkern u1="Z" u2="&#xe0;" k="30" />
    <hkern u1="Z" u2="&#xdd;" k="22" />
    <hkern u1="Z" u2="&#xd8;" k="22" />
    <hkern u1="Z" u2="&#xd6;" k="22" />
    <hkern u1="Z" u2="&#xd5;" k="22" />
    <hkern u1="Z" u2="&#xd4;" k="22" />
    <hkern u1="Z" u2="&#xd3;" k="22" />
    <hkern u1="Z" u2="&#xd2;" k="22" />
    <hkern u1="Z" u2="&#xc7;" k="22" />
    <hkern u1="Z" u2="&#xc6;" k="28" />
    <hkern u1="Z" u2="&#xc5;" k="28" />
    <hkern u1="Z" u2="&#xc4;" k="28" />
    <hkern u1="Z" u2="&#xc3;" k="28" />
    <hkern u1="Z" u2="&#xc2;" k="28" />
    <hkern u1="Z" u2="&#xc1;" k="28" />
    <hkern u1="Z" u2="&#xc0;" k="28" />
    <hkern u1="Z" u2="y" k="28" />
    <hkern u1="Z" u2="w" k="22" />
    <hkern u1="Z" u2="v" k="28" />
    <hkern u1="Z" u2="t" k="31" />
    <hkern u1="Z" u2="q" k="30" />
    <hkern u1="Z" u2="o" k="30" />
    <hkern u1="Z" u2="g" k="30" />
    <hkern u1="Z" u2="f" k="31" />
    <hkern u1="Z" u2="e" k="30" />
    <hkern u1="Z" u2="d" k="30" />
    <hkern u1="Z" u2="c" k="30" />
    <hkern u1="Z" u2="a" k="30" />
    <hkern u1="Z" u2="Z" k="28" />
    <hkern u1="Z" u2="Y" k="22" />
    <hkern u1="Z" u2="X" k="18" />
    <hkern u1="Z" u2="W" k="12" />
    <hkern u1="Z" u2="V" k="24" />
    <hkern u1="Z" u2="T" k="24" />
    <hkern u1="Z" u2="Q" k="22" />
    <hkern u1="Z" u2="O" k="22" />
    <hkern u1="Z" u2="J" k="24" />
    <hkern u1="Z" u2="G" k="22" />
    <hkern u1="Z" u2="C" k="22" />
    <hkern u1="Z" u2="A" k="28" />
    <hkern u1="Z" u2="&#x40;" k="30" />
    <hkern u1="Z" u2="&#x3f;" k="-6" />
    <hkern u1="Z" u2="&#x2d;" k="31" />
    <hkern u1="[" u2="&#x152;" k="12" />
    <hkern u1="[" u2="&#x150;" k="12" />
    <hkern u1="[" u2="&#x14c;" k="12" />
    <hkern u1="[" u2="&#x122;" k="12" />
    <hkern u1="[" u2="&#x120;" k="12" />
    <hkern u1="[" u2="&#x11e;" k="12" />
    <hkern u1="[" u2="&#x10c;" k="12" />
    <hkern u1="[" u2="&#x10a;" k="12" />
    <hkern u1="[" u2="&#x106;" k="12" />
    <hkern u1="[" u2="&#xd8;" k="12" />
    <hkern u1="[" u2="&#xd6;" k="12" />
    <hkern u1="[" u2="&#xd5;" k="12" />
    <hkern u1="[" u2="&#xd4;" k="12" />
    <hkern u1="[" u2="&#xd3;" k="12" />
    <hkern u1="[" u2="&#xd2;" k="12" />
    <hkern u1="[" u2="&#xc7;" k="12" />
    <hkern u1="[" u2="j" k="-82" />
    <hkern u1="[" u2="Q" k="12" />
    <hkern u1="[" u2="O" k="12" />
    <hkern u1="[" u2="G" k="12" />
    <hkern u1="[" u2="C" k="12" />
    <hkern u1="a" g2="f_t" k="2" />
    <hkern u1="a" g2="f_j" k="2" />
    <hkern u1="a" g2="f_f" k="2" />
    <hkern u1="a" g2="t.ss01" k="2" />
    <hkern u1="a" g2="fl" k="2" />
    <hkern u1="a" g2="fi" k="2" />
    <hkern u1="a" u2="&#x21b;" k="2" />
    <hkern u1="a" u2="&#x165;" k="2" />
    <hkern u1="a" u2="w" k="2" />
    <hkern u1="a" u2="t" k="2" />
    <hkern u1="a" u2="f" k="2" />
    <hkern u1="b" u2="z" k="16" />
    <hkern u1="b" u2="x" k="15" />
    <hkern u1="b" u2="w" k="14" />
    <hkern u1="b" u2="&#x2d;" k="-16" />
    <hkern u1="c" u2="x" k="3" />
    <hkern u1="c" u2="&#x2d;" k="26" />
    <hkern u1="e" u2="x" k="15" />
    <hkern u1="e" u2="w" k="6" />
    <hkern u1="e" u2="&#x2d;" k="-16" />
    <hkern u1="f" g2="g.ss01" k="24" />
    <hkern u1="f" u2="&#x7d;" k="-54" />
    <hkern u1="f" u2="z" k="18" />
    <hkern u1="f" u2="x" k="26" />
    <hkern u1="f" u2="w" k="14" />
    <hkern u1="f" u2="s" k="18" />
    <hkern u1="f" u2="]" k="-56" />
    <hkern u1="f" u2="&#x3f;" k="-44" />
    <hkern u1="f" u2="&#x2f;" k="20" />
    <hkern u1="f" u2="&#x2d;" k="52" />
    <hkern u1="f" u2="&#x2a;" k="-18" />
    <hkern u1="f" u2="&#x29;" k="-56" />
    <hkern u1="g" u2="j" k="-52" />
    <hkern u1="i" u2="w" k="8" />
    <hkern u1="j" u2="j" k="-63" />
    <hkern u1="k" g2="r_t" k="16" />
    <hkern u1="k" g2="r_f" k="16" />
    <hkern u1="k" g2="o_u_t_f_i_t_t_e_r" k="18" />
    <hkern u1="k" g2="o_u_t_f_i_t_t_e_d" k="18" />
    <hkern u1="k" g2="o_u_t_f_i_t" k="18" />
    <hkern u1="k" g2="f_t" k="34" />
    <hkern u1="k" g2="f_j" k="34" />
    <hkern u1="k" g2="f_f" k="34" />
    <hkern u1="k" g2="t.ss01" k="34" />
    <hkern u1="k" g2="oe.ss01" k="27" />
    <hkern u1="k" g2="eng.ss01" k="16" />
    <hkern u1="k" g2="ntilde.ss01" k="16" />
    <hkern u1="k" g2="uni0146.ss01" k="16" />
    <hkern u1="k" g2="ncaron.ss01" k="16" />
    <hkern u1="k" g2="nacute.ss01" k="16" />
    <hkern u1="k" g2="n.ss01" k="16" />
    <hkern u1="k" g2="m.ss01" k="16" />
    <hkern u1="k" g2="g.ss01" k="22" />
    <hkern u1="k" g2="eogonek.ss01" k="27" />
    <hkern u1="k" g2="emacron.ss01" k="27" />
    <hkern u1="k" g2="egrave.ss01" k="27" />
    <hkern u1="k" g2="edotaccent.ss01" k="27" />
    <hkern u1="k" g2="edieresis.ss01" k="27" />
    <hkern u1="k" g2="ecircumflex.ss01" k="27" />
    <hkern u1="k" g2="ecaron.ss01" k="27" />
    <hkern u1="k" g2="eacute.ss01" k="27" />
    <hkern u1="k" g2="e.ss01" k="27" />
    <hkern u1="k" g2="ae.ss01" k="27" />
    <hkern u1="k" g2="fl" k="34" />
    <hkern u1="k" g2="fi" k="34" />
    <hkern u1="k" u2="&#x1ef3;" k="33" />
    <hkern u1="k" u2="&#x21b;" k="34" />
    <hkern u1="k" u2="&#x1ce;" k="27" />
    <hkern u1="k" u2="&#x177;" k="33" />
    <hkern u1="k" u2="&#x173;" k="18" />
    <hkern u1="k" u2="&#x171;" k="18" />
    <hkern u1="k" u2="&#x16f;" k="18" />
    <hkern u1="k" u2="&#x16d;" k="18" />
    <hkern u1="k" u2="&#x16b;" k="18" />
    <hkern u1="k" u2="&#x165;" k="34" />
    <hkern u1="k" u2="&#x159;" k="16" />
    <hkern u1="k" u2="&#x157;" k="16" />
    <hkern u1="k" u2="&#x155;" k="16" />
    <hkern u1="k" u2="&#x153;" k="27" />
    <hkern u1="k" u2="&#x151;" k="27" />
    <hkern u1="k" u2="&#x14d;" k="27" />
    <hkern u1="k" u2="&#x14b;" k="16" />
    <hkern u1="k" u2="&#x148;" k="16" />
    <hkern u1="k" u2="&#x146;" k="16" />
    <hkern u1="k" u2="&#x144;" k="16" />
    <hkern u1="k" u2="&#x123;" k="27" />
    <hkern u1="k" u2="&#x121;" k="27" />
    <hkern u1="k" u2="&#x11f;" k="27" />
    <hkern u1="k" u2="&#x11b;" k="27" />
    <hkern u1="k" u2="&#x119;" k="27" />
    <hkern u1="k" u2="&#x117;" k="27" />
    <hkern u1="k" u2="&#x113;" k="27" />
    <hkern u1="k" u2="&#x111;" k="27" />
    <hkern u1="k" u2="&#x10f;" k="27" />
    <hkern u1="k" u2="&#x10d;" k="27" />
    <hkern u1="k" u2="&#x10b;" k="27" />
    <hkern u1="k" u2="&#x107;" k="27" />
    <hkern u1="k" u2="&#x105;" k="27" />
    <hkern u1="k" u2="&#x103;" k="27" />
    <hkern u1="k" u2="&#x101;" k="27" />
    <hkern u1="k" u2="&#xff;" k="33" />
    <hkern u1="k" u2="&#xfe;" k="16" />
    <hkern u1="k" u2="&#xfd;" k="33" />
    <hkern u1="k" u2="&#xfc;" k="18" />
    <hkern u1="k" u2="&#xfb;" k="18" />
    <hkern u1="k" u2="&#xfa;" k="18" />
    <hkern u1="k" u2="&#xf9;" k="18" />
    <hkern u1="k" u2="&#xf8;" k="27" />
    <hkern u1="k" u2="&#xf6;" k="27" />
    <hkern u1="k" u2="&#xf5;" k="27" />
    <hkern u1="k" u2="&#xf4;" k="27" />
    <hkern u1="k" u2="&#xf3;" k="27" />
    <hkern u1="k" u2="&#xf2;" k="27" />
    <hkern u1="k" u2="&#xf1;" k="16" />
    <hkern u1="k" u2="&#xeb;" k="27" />
    <hkern u1="k" u2="&#xea;" k="27" />
    <hkern u1="k" u2="&#xe9;" k="27" />
    <hkern u1="k" u2="&#xe8;" k="27" />
    <hkern u1="k" u2="&#xe7;" k="27" />
    <hkern u1="k" u2="&#xe6;" k="27" />
    <hkern u1="k" u2="&#xe5;" k="27" />
    <hkern u1="k" u2="&#xe4;" k="27" />
    <hkern u1="k" u2="&#xe3;" k="27" />
    <hkern u1="k" u2="&#xe2;" k="27" />
    <hkern u1="k" u2="&#xe1;" k="27" />
    <hkern u1="k" u2="&#xe0;" k="27" />
    <hkern u1="k" u2="z" k="28" />
    <hkern u1="k" u2="y" k="33" />
    <hkern u1="k" u2="x" k="36" />
    <hkern u1="k" u2="w" k="33" />
    <hkern u1="k" u2="v" k="33" />
    <hkern u1="k" u2="u" k="18" />
    <hkern u1="k" u2="t" k="34" />
    <hkern u1="k" u2="s" k="28" />
    <hkern u1="k" u2="r" k="16" />
    <hkern u1="k" u2="q" k="27" />
    <hkern u1="k" u2="p" k="16" />
    <hkern u1="k" u2="o" k="27" />
    <hkern u1="k" u2="n" k="16" />
    <hkern u1="k" u2="m" k="16" />
    <hkern u1="k" u2="j" k="18" />
    <hkern u1="k" u2="i" k="18" />
    <hkern u1="k" u2="g" k="27" />
    <hkern u1="k" u2="f" k="34" />
    <hkern u1="k" u2="e" k="27" />
    <hkern u1="k" u2="d" k="27" />
    <hkern u1="k" u2="c" k="27" />
    <hkern u1="k" u2="a" k="27" />
    <hkern u1="k" u2="&#x40;" k="27" />
    <hkern u1="k" u2="&#x3f;" k="30" />
    <hkern u1="k" u2="&#x2e;" k="16" />
    <hkern u1="k" u2="&#x2d;" k="58" />
    <hkern u1="k" u2="&#x2c;" k="16" />
    <hkern u1="o" u2="z" k="16" />
    <hkern u1="o" u2="x" k="15" />
    <hkern u1="o" u2="w" k="14" />
    <hkern u1="o" u2="&#x2d;" k="-16" />
    <hkern u1="p" u2="z" k="16" />
    <hkern u1="p" u2="x" k="15" />
    <hkern u1="p" u2="w" k="14" />
    <hkern u1="p" u2="&#x2d;" k="-16" />
    <hkern u1="q" u2="j" k="-82" />
    <hkern u1="q" u2="&#x27;" k="21" />
    <hkern u1="q" u2="&#x22;" k="21" />
    <hkern u1="r" g2="r_t" k="16" />
    <hkern u1="r" g2="r_f" k="16" />
    <hkern u1="r" g2="o_u_t_f_i_t_t_e_r" k="18" />
    <hkern u1="r" g2="o_u_t_f_i_t_t_e_d" k="18" />
    <hkern u1="r" g2="o_u_t_f_i_t" k="18" />
    <hkern u1="r" g2="f_t" k="19" />
    <hkern u1="r" g2="f_j" k="19" />
    <hkern u1="r" g2="f_f" k="19" />
    <hkern u1="r" g2="t.ss01" k="19" />
    <hkern u1="r" g2="oe.ss01" k="33" />
    <hkern u1="r" g2="eng.ss01" k="16" />
    <hkern u1="r" g2="ntilde.ss01" k="16" />
    <hkern u1="r" g2="uni0146.ss01" k="16" />
    <hkern u1="r" g2="ncaron.ss01" k="16" />
    <hkern u1="r" g2="nacute.ss01" k="16" />
    <hkern u1="r" g2="n.ss01" k="16" />
    <hkern u1="r" g2="m.ss01" k="16" />
    <hkern u1="r" g2="uni0137.ss01" k="18" />
    <hkern u1="r" g2="k.ss01" k="18" />
    <hkern u1="r" g2="g.ss01" k="34" />
    <hkern u1="r" g2="eogonek.ss01" k="33" />
    <hkern u1="r" g2="emacron.ss01" k="33" />
    <hkern u1="r" g2="egrave.ss01" k="33" />
    <hkern u1="r" g2="edotaccent.ss01" k="33" />
    <hkern u1="r" g2="edieresis.ss01" k="33" />
    <hkern u1="r" g2="ecircumflex.ss01" k="33" />
    <hkern u1="r" g2="ecaron.ss01" k="33" />
    <hkern u1="r" g2="eacute.ss01" k="33" />
    <hkern u1="r" g2="e.ss01" k="33" />
    <hkern u1="r" g2="ae.ss01" k="33" />
    <hkern u1="r" g2="fl" k="19" />
    <hkern u1="r" g2="fi" k="19" />
    <hkern u1="r" u2="&#x201d;" k="-18" />
    <hkern u1="r" u2="&#x2019;" k="-18" />
    <hkern u1="r" u2="&#x1ef3;" k="26" />
    <hkern u1="r" u2="&#x2bc;" k="-18" />
    <hkern u1="r" u2="&#x21b;" k="19" />
    <hkern u1="r" u2="&#x1ce;" k="33" />
    <hkern u1="r" u2="&#x177;" k="26" />
    <hkern u1="r" u2="&#x173;" k="18" />
    <hkern u1="r" u2="&#x171;" k="18" />
    <hkern u1="r" u2="&#x16f;" k="18" />
    <hkern u1="r" u2="&#x16d;" k="18" />
    <hkern u1="r" u2="&#x16b;" k="18" />
    <hkern u1="r" u2="&#x165;" k="19" />
    <hkern u1="r" u2="&#x159;" k="16" />
    <hkern u1="r" u2="&#x157;" k="16" />
    <hkern u1="r" u2="&#x155;" k="16" />
    <hkern u1="r" u2="&#x153;" k="33" />
    <hkern u1="r" u2="&#x151;" k="33" />
    <hkern u1="r" u2="&#x14d;" k="33" />
    <hkern u1="r" u2="&#x14b;" k="16" />
    <hkern u1="r" u2="&#x148;" k="16" />
    <hkern u1="r" u2="&#x146;" k="16" />
    <hkern u1="r" u2="&#x144;" k="16" />
    <hkern u1="r" u2="&#x142;" k="18" />
    <hkern u1="r" u2="&#x13e;" k="18" />
    <hkern u1="r" u2="&#x13c;" k="18" />
    <hkern u1="r" u2="&#x13a;" k="18" />
    <hkern u1="r" u2="&#x137;" k="18" />
    <hkern u1="r" u2="&#x123;" k="33" />
    <hkern u1="r" u2="&#x121;" k="33" />
    <hkern u1="r" u2="&#x11f;" k="33" />
    <hkern u1="r" u2="&#x11b;" k="33" />
    <hkern u1="r" u2="&#x119;" k="33" />
    <hkern u1="r" u2="&#x117;" k="33" />
    <hkern u1="r" u2="&#x113;" k="33" />
    <hkern u1="r" u2="&#x111;" k="33" />
    <hkern u1="r" u2="&#x10f;" k="33" />
    <hkern u1="r" u2="&#x10d;" k="33" />
    <hkern u1="r" u2="&#x10b;" k="33" />
    <hkern u1="r" u2="&#x107;" k="33" />
    <hkern u1="r" u2="&#x105;" k="33" />
    <hkern u1="r" u2="&#x103;" k="33" />
    <hkern u1="r" u2="&#x101;" k="33" />
    <hkern u1="r" u2="&#xff;" k="26" />
    <hkern u1="r" u2="&#xfe;" k="16" />
    <hkern u1="r" u2="&#xfd;" k="26" />
    <hkern u1="r" u2="&#xfc;" k="18" />
    <hkern u1="r" u2="&#xfb;" k="18" />
    <hkern u1="r" u2="&#xfa;" k="18" />
    <hkern u1="r" u2="&#xf9;" k="18" />
    <hkern u1="r" u2="&#xf8;" k="33" />
    <hkern u1="r" u2="&#xf6;" k="33" />
    <hkern u1="r" u2="&#xf5;" k="33" />
    <hkern u1="r" u2="&#xf4;" k="33" />
    <hkern u1="r" u2="&#xf3;" k="33" />
    <hkern u1="r" u2="&#xf2;" k="33" />
    <hkern u1="r" u2="&#xf1;" k="16" />
    <hkern u1="r" u2="&#xeb;" k="33" />
    <hkern u1="r" u2="&#xea;" k="33" />
    <hkern u1="r" u2="&#xe9;" k="33" />
    <hkern u1="r" u2="&#xe8;" k="33" />
    <hkern u1="r" u2="&#xe7;" k="33" />
    <hkern u1="r" u2="&#xe6;" k="33" />
    <hkern u1="r" u2="&#xe5;" k="33" />
    <hkern u1="r" u2="&#xe4;" k="33" />
    <hkern u1="r" u2="&#xe3;" k="33" />
    <hkern u1="r" u2="&#xe2;" k="33" />
    <hkern u1="r" u2="&#xe1;" k="33" />
    <hkern u1="r" u2="&#xe0;" k="33" />
    <hkern u1="r" u2="z" k="28" />
    <hkern u1="r" u2="y" k="26" />
    <hkern u1="r" u2="x" k="24" />
    <hkern u1="r" u2="w" k="23" />
    <hkern u1="r" u2="v" k="26" />
    <hkern u1="r" u2="u" k="18" />
    <hkern u1="r" u2="t" k="19" />
    <hkern u1="r" u2="s" k="28" />
    <hkern u1="r" u2="r" k="16" />
    <hkern u1="r" u2="q" k="33" />
    <hkern u1="r" u2="p" k="16" />
    <hkern u1="r" u2="o" k="33" />
    <hkern u1="r" u2="n" k="16" />
    <hkern u1="r" u2="m" k="16" />
    <hkern u1="r" u2="l" k="18" />
    <hkern u1="r" u2="k" k="18" />
    <hkern u1="r" u2="j" k="17" />
    <hkern u1="r" u2="i" k="18" />
    <hkern u1="r" u2="h" k="18" />
    <hkern u1="r" u2="g" k="33" />
    <hkern u1="r" u2="f" k="19" />
    <hkern u1="r" u2="e" k="33" />
    <hkern u1="r" u2="d" k="33" />
    <hkern u1="r" u2="c" k="33" />
    <hkern u1="r" u2="b" k="28" />
    <hkern u1="r" u2="a" k="33" />
    <hkern u1="r" u2="&#x40;" k="33" />
    <hkern u1="r" u2="&#x2f;" k="66" />
    <hkern u1="r" u2="&#x2e;" k="77" />
    <hkern u1="r" u2="&#x2d;" k="50" />
    <hkern u1="r" u2="&#x2c;" k="77" />
    <hkern u1="r" u2="&#x26;" k="19" />
    <hkern u1="s" u2="&#x27;" k="22" />
    <hkern u1="s" u2="&#x22;" k="22" />
    <hkern u1="t" u2="z" k="14" />
    <hkern u1="t" u2="x" k="7" />
    <hkern u1="t" u2="s" k="10" />
    <hkern u1="t" u2="&#x2d;" k="19" />
    <hkern u1="v" g2="g.ss01" k="18" />
    <hkern u1="v" u2="z" k="8" />
    <hkern u1="v" u2="x" k="20" />
    <hkern u1="v" u2="w" k="16" />
    <hkern u1="v" u2="s" k="14" />
    <hkern u1="v" u2="&#x2d;" k="26" />
    <hkern u1="v" u2="&#x29;" k="36" />
    <hkern u1="w" g2="oe.ss01" k="14" />
    <hkern u1="w" g2="g.ss01" k="18" />
    <hkern u1="w" g2="eogonek.ss01" k="14" />
    <hkern u1="w" g2="emacron.ss01" k="14" />
    <hkern u1="w" g2="egrave.ss01" k="14" />
    <hkern u1="w" g2="edotaccent.ss01" k="14" />
    <hkern u1="w" g2="edieresis.ss01" k="14" />
    <hkern u1="w" g2="ecircumflex.ss01" k="14" />
    <hkern u1="w" g2="ecaron.ss01" k="14" />
    <hkern u1="w" g2="eacute.ss01" k="14" />
    <hkern u1="w" g2="e.ss01" k="14" />
    <hkern u1="w" g2="ae.ss01" k="14" />
    <hkern u1="w" u2="&#x1ef3;" k="16" />
    <hkern u1="w" u2="&#x1ce;" k="14" />
    <hkern u1="w" u2="&#x177;" k="16" />
    <hkern u1="w" u2="&#x153;" k="14" />
    <hkern u1="w" u2="&#x151;" k="14" />
    <hkern u1="w" u2="&#x14d;" k="14" />
    <hkern u1="w" u2="&#x123;" k="14" />
    <hkern u1="w" u2="&#x121;" k="14" />
    <hkern u1="w" u2="&#x11f;" k="14" />
    <hkern u1="w" u2="&#x11b;" k="14" />
    <hkern u1="w" u2="&#x119;" k="14" />
    <hkern u1="w" u2="&#x117;" k="14" />
    <hkern u1="w" u2="&#x113;" k="14" />
    <hkern u1="w" u2="&#x111;" k="14" />
    <hkern u1="w" u2="&#x10f;" k="14" />
    <hkern u1="w" u2="&#x10d;" k="14" />
    <hkern u1="w" u2="&#x10b;" k="14" />
    <hkern u1="w" u2="&#x107;" k="14" />
    <hkern u1="w" u2="&#x105;" k="14" />
    <hkern u1="w" u2="&#x103;" k="14" />
    <hkern u1="w" u2="&#x101;" k="14" />
    <hkern u1="w" u2="&#xff;" k="16" />
    <hkern u1="w" u2="&#xfd;" k="16" />
    <hkern u1="w" u2="&#xf8;" k="14" />
    <hkern u1="w" u2="&#xf6;" k="14" />
    <hkern u1="w" u2="&#xf5;" k="14" />
    <hkern u1="w" u2="&#xf4;" k="14" />
    <hkern u1="w" u2="&#xf3;" k="14" />
    <hkern u1="w" u2="&#xf2;" k="14" />
    <hkern u1="w" u2="&#xeb;" k="14" />
    <hkern u1="w" u2="&#xea;" k="14" />
    <hkern u1="w" u2="&#xe9;" k="14" />
    <hkern u1="w" u2="&#xe8;" k="14" />
    <hkern u1="w" u2="&#xe7;" k="14" />
    <hkern u1="w" u2="&#xe6;" k="14" />
    <hkern u1="w" u2="&#xe5;" k="14" />
    <hkern u1="w" u2="&#xe4;" k="14" />
    <hkern u1="w" u2="&#xe3;" k="14" />
    <hkern u1="w" u2="&#xe2;" k="14" />
    <hkern u1="w" u2="&#xe1;" k="14" />
    <hkern u1="w" u2="&#xe0;" k="14" />
    <hkern u1="w" u2="z" k="8" />
    <hkern u1="w" u2="y" k="16" />
    <hkern u1="w" u2="x" k="14" />
    <hkern u1="w" u2="w" k="22" />
    <hkern u1="w" u2="v" k="16" />
    <hkern u1="w" u2="s" k="12" />
    <hkern u1="w" u2="q" k="14" />
    <hkern u1="w" u2="o" k="14" />
    <hkern u1="w" u2="i" k="8" />
    <hkern u1="w" u2="g" k="14" />
    <hkern u1="w" u2="e" k="14" />
    <hkern u1="w" u2="d" k="14" />
    <hkern u1="w" u2="c" k="14" />
    <hkern u1="w" u2="a" k="14" />
    <hkern u1="w" u2="&#x40;" k="14" />
    <hkern u1="w" u2="&#x2e;" k="33" />
    <hkern u1="w" u2="&#x2c;" k="33" />
    <hkern u1="w" u2="&#x29;" k="28" />
    <hkern u1="w" u2="&#x27;" k="3" />
    <hkern u1="w" u2="&#x22;" k="3" />
    <hkern u1="x" g2="f_t" k="20" />
    <hkern u1="x" g2="f_j" k="20" />
    <hkern u1="x" g2="f_f" k="20" />
    <hkern u1="x" g2="t.ss01" k="20" />
    <hkern u1="x" g2="oe.ss01" k="15" />
    <hkern u1="x" g2="eogonek.ss01" k="15" />
    <hkern u1="x" g2="emacron.ss01" k="15" />
    <hkern u1="x" g2="egrave.ss01" k="15" />
    <hkern u1="x" g2="edotaccent.ss01" k="15" />
    <hkern u1="x" g2="edieresis.ss01" k="15" />
    <hkern u1="x" g2="ecircumflex.ss01" k="15" />
    <hkern u1="x" g2="ecaron.ss01" k="15" />
    <hkern u1="x" g2="eacute.ss01" k="15" />
    <hkern u1="x" g2="e.ss01" k="15" />
    <hkern u1="x" g2="ae.ss01" k="15" />
    <hkern u1="x" g2="fl" k="20" />
    <hkern u1="x" g2="fi" k="20" />
    <hkern u1="x" u2="&#x1ef3;" k="20" />
    <hkern u1="x" u2="&#x21b;" k="20" />
    <hkern u1="x" u2="&#x1ce;" k="15" />
    <hkern u1="x" u2="&#x177;" k="20" />
    <hkern u1="x" u2="&#x165;" k="20" />
    <hkern u1="x" u2="&#x153;" k="15" />
    <hkern u1="x" u2="&#x151;" k="15" />
    <hkern u1="x" u2="&#x14d;" k="15" />
    <hkern u1="x" u2="&#x123;" k="15" />
    <hkern u1="x" u2="&#x121;" k="15" />
    <hkern u1="x" u2="&#x11f;" k="15" />
    <hkern u1="x" u2="&#x11b;" k="15" />
    <hkern u1="x" u2="&#x119;" k="15" />
    <hkern u1="x" u2="&#x117;" k="15" />
    <hkern u1="x" u2="&#x113;" k="15" />
    <hkern u1="x" u2="&#x111;" k="15" />
    <hkern u1="x" u2="&#x10f;" k="15" />
    <hkern u1="x" u2="&#x10d;" k="15" />
    <hkern u1="x" u2="&#x10b;" k="15" />
    <hkern u1="x" u2="&#x107;" k="15" />
    <hkern u1="x" u2="&#x105;" k="15" />
    <hkern u1="x" u2="&#x103;" k="15" />
    <hkern u1="x" u2="&#x101;" k="15" />
    <hkern u1="x" u2="&#xff;" k="20" />
    <hkern u1="x" u2="&#xfd;" k="20" />
    <hkern u1="x" u2="&#xf8;" k="15" />
    <hkern u1="x" u2="&#xf6;" k="15" />
    <hkern u1="x" u2="&#xf5;" k="15" />
    <hkern u1="x" u2="&#xf4;" k="15" />
    <hkern u1="x" u2="&#xf3;" k="15" />
    <hkern u1="x" u2="&#xf2;" k="15" />
    <hkern u1="x" u2="&#xeb;" k="15" />
    <hkern u1="x" u2="&#xea;" k="15" />
    <hkern u1="x" u2="&#xe9;" k="15" />
    <hkern u1="x" u2="&#xe8;" k="15" />
    <hkern u1="x" u2="&#xe7;" k="15" />
    <hkern u1="x" u2="&#xe6;" k="15" />
    <hkern u1="x" u2="&#xe5;" k="15" />
    <hkern u1="x" u2="&#xe4;" k="15" />
    <hkern u1="x" u2="&#xe3;" k="15" />
    <hkern u1="x" u2="&#xe2;" k="15" />
    <hkern u1="x" u2="&#xe1;" k="15" />
    <hkern u1="x" u2="&#xe0;" k="15" />
    <hkern u1="x" u2="y" k="20" />
    <hkern u1="x" u2="x" k="26" />
    <hkern u1="x" u2="w" k="14" />
    <hkern u1="x" u2="v" k="20" />
    <hkern u1="x" u2="t" k="20" />
    <hkern u1="x" u2="s" k="16" />
    <hkern u1="x" u2="q" k="15" />
    <hkern u1="x" u2="o" k="15" />
    <hkern u1="x" u2="g" k="15" />
    <hkern u1="x" u2="f" k="20" />
    <hkern u1="x" u2="e" k="15" />
    <hkern u1="x" u2="d" k="15" />
    <hkern u1="x" u2="c" k="15" />
    <hkern u1="x" u2="a" k="15" />
    <hkern u1="x" u2="&#x40;" k="15" />
    <hkern u1="x" u2="&#x2d;" k="27" />
    <hkern u1="x" u2="&#x27;" k="3" />
    <hkern u1="x" u2="&#x22;" k="3" />
    <hkern u1="y" g2="r_t" k="12" />
    <hkern u1="y" g2="r_f" k="12" />
    <hkern u1="y" g2="f_t" k="8" />
    <hkern u1="y" g2="f_j" k="8" />
    <hkern u1="y" g2="f_f" k="8" />
    <hkern u1="y" g2="t.ss01" k="8" />
    <hkern u1="y" g2="oe.ss01" k="26" />
    <hkern u1="y" g2="eng.ss01" k="12" />
    <hkern u1="y" g2="ntilde.ss01" k="12" />
    <hkern u1="y" g2="uni0146.ss01" k="12" />
    <hkern u1="y" g2="ncaron.ss01" k="12" />
    <hkern u1="y" g2="nacute.ss01" k="12" />
    <hkern u1="y" g2="n.ss01" k="12" />
    <hkern u1="y" g2="m.ss01" k="12" />
    <hkern u1="y" g2="uni0137.ss01" k="12" />
    <hkern u1="y" g2="k.ss01" k="12" />
    <hkern u1="y" g2="g.ss01" k="42" />
    <hkern u1="y" g2="eogonek.ss01" k="26" />
    <hkern u1="y" g2="emacron.ss01" k="26" />
    <hkern u1="y" g2="egrave.ss01" k="26" />
    <hkern u1="y" g2="edotaccent.ss01" k="26" />
    <hkern u1="y" g2="edieresis.ss01" k="26" />
    <hkern u1="y" g2="ecircumflex.ss01" k="26" />
    <hkern u1="y" g2="ecaron.ss01" k="26" />
    <hkern u1="y" g2="eacute.ss01" k="26" />
    <hkern u1="y" g2="e.ss01" k="26" />
    <hkern u1="y" g2="ae.ss01" k="26" />
    <hkern u1="y" g2="fl" k="8" />
    <hkern u1="y" g2="fi" k="8" />
    <hkern u1="y" u2="&#x1ef3;" k="18" />
    <hkern u1="y" u2="&#x21b;" k="8" />
    <hkern u1="y" u2="&#x1ce;" k="26" />
    <hkern u1="y" u2="&#x177;" k="18" />
    <hkern u1="y" u2="&#x165;" k="8" />
    <hkern u1="y" u2="&#x159;" k="12" />
    <hkern u1="y" u2="&#x157;" k="12" />
    <hkern u1="y" u2="&#x155;" k="12" />
    <hkern u1="y" u2="&#x153;" k="26" />
    <hkern u1="y" u2="&#x151;" k="26" />
    <hkern u1="y" u2="&#x14d;" k="26" />
    <hkern u1="y" u2="&#x14b;" k="12" />
    <hkern u1="y" u2="&#x148;" k="12" />
    <hkern u1="y" u2="&#x146;" k="12" />
    <hkern u1="y" u2="&#x144;" k="12" />
    <hkern u1="y" u2="&#x142;" k="12" />
    <hkern u1="y" u2="&#x13e;" k="12" />
    <hkern u1="y" u2="&#x13c;" k="12" />
    <hkern u1="y" u2="&#x13a;" k="12" />
    <hkern u1="y" u2="&#x137;" k="12" />
    <hkern u1="y" u2="&#x123;" k="26" />
    <hkern u1="y" u2="&#x121;" k="26" />
    <hkern u1="y" u2="&#x11f;" k="26" />
    <hkern u1="y" u2="&#x11b;" k="26" />
    <hkern u1="y" u2="&#x119;" k="26" />
    <hkern u1="y" u2="&#x117;" k="26" />
    <hkern u1="y" u2="&#x113;" k="26" />
    <hkern u1="y" u2="&#x111;" k="26" />
    <hkern u1="y" u2="&#x10f;" k="26" />
    <hkern u1="y" u2="&#x10d;" k="26" />
    <hkern u1="y" u2="&#x10b;" k="26" />
    <hkern u1="y" u2="&#x107;" k="26" />
    <hkern u1="y" u2="&#x105;" k="26" />
    <hkern u1="y" u2="&#x103;" k="26" />
    <hkern u1="y" u2="&#x101;" k="26" />
    <hkern u1="y" u2="&#xff;" k="18" />
    <hkern u1="y" u2="&#xfe;" k="12" />
    <hkern u1="y" u2="&#xfd;" k="18" />
    <hkern u1="y" u2="&#xf8;" k="26" />
    <hkern u1="y" u2="&#xf6;" k="26" />
    <hkern u1="y" u2="&#xf5;" k="26" />
    <hkern u1="y" u2="&#xf4;" k="26" />
    <hkern u1="y" u2="&#xf3;" k="26" />
    <hkern u1="y" u2="&#xf2;" k="26" />
    <hkern u1="y" u2="&#xf1;" k="12" />
    <hkern u1="y" u2="&#xeb;" k="26" />
    <hkern u1="y" u2="&#xea;" k="26" />
    <hkern u1="y" u2="&#xe9;" k="26" />
    <hkern u1="y" u2="&#xe8;" k="26" />
    <hkern u1="y" u2="&#xe7;" k="26" />
    <hkern u1="y" u2="&#xe6;" k="26" />
    <hkern u1="y" u2="&#xe5;" k="26" />
    <hkern u1="y" u2="&#xe4;" k="26" />
    <hkern u1="y" u2="&#xe3;" k="26" />
    <hkern u1="y" u2="&#xe2;" k="26" />
    <hkern u1="y" u2="&#xe1;" k="26" />
    <hkern u1="y" u2="&#xe0;" k="26" />
    <hkern u1="y" u2="&#x7d;" k="18" />
    <hkern u1="y" u2="z" k="22" />
    <hkern u1="y" u2="y" k="18" />
    <hkern u1="y" u2="x" k="28" />
    <hkern u1="y" u2="w" k="28" />
    <hkern u1="y" u2="v" k="18" />
    <hkern u1="y" u2="t" k="8" />
    <hkern u1="y" u2="s" k="18" />
    <hkern u1="y" u2="r" k="12" />
    <hkern u1="y" u2="q" k="26" />
    <hkern u1="y" u2="p" k="12" />
    <hkern u1="y" u2="o" k="26" />
    <hkern u1="y" u2="n" k="12" />
    <hkern u1="y" u2="m" k="12" />
    <hkern u1="y" u2="l" k="12" />
    <hkern u1="y" u2="k" k="12" />
    <hkern u1="y" u2="j" k="14" />
    <hkern u1="y" u2="i" k="14" />
    <hkern u1="y" u2="h" k="12" />
    <hkern u1="y" u2="g" k="26" />
    <hkern u1="y" u2="f" k="8" />
    <hkern u1="y" u2="e" k="26" />
    <hkern u1="y" u2="d" k="26" />
    <hkern u1="y" u2="c" k="26" />
    <hkern u1="y" u2="b" k="12" />
    <hkern u1="y" u2="a" k="26" />
    <hkern u1="y" u2="]" k="10" />
    <hkern u1="y" u2="&#x40;" k="26" />
    <hkern u1="y" u2="&#x3b;" k="14" />
    <hkern u1="y" u2="&#x3a;" k="14" />
    <hkern u1="y" u2="&#x2e;" k="68" />
    <hkern u1="y" u2="&#x2d;" k="35" />
    <hkern u1="y" u2="&#x2c;" k="68" />
    <hkern u1="y" u2="&#x29;" k="36" />
    <hkern u1="z" g2="f_t" k="12" />
    <hkern u1="z" g2="f_j" k="12" />
    <hkern u1="z" g2="f_f" k="12" />
    <hkern u1="z" g2="t.ss01" k="12" />
    <hkern u1="z" g2="oe.ss01" k="9" />
    <hkern u1="z" g2="eogonek.ss01" k="9" />
    <hkern u1="z" g2="emacron.ss01" k="9" />
    <hkern u1="z" g2="egrave.ss01" k="9" />
    <hkern u1="z" g2="edotaccent.ss01" k="9" />
    <hkern u1="z" g2="edieresis.ss01" k="9" />
    <hkern u1="z" g2="ecircumflex.ss01" k="9" />
    <hkern u1="z" g2="ecaron.ss01" k="9" />
    <hkern u1="z" g2="eacute.ss01" k="9" />
    <hkern u1="z" g2="e.ss01" k="9" />
    <hkern u1="z" g2="ae.ss01" k="9" />
    <hkern u1="z" g2="fl" k="12" />
    <hkern u1="z" g2="fi" k="12" />
    <hkern u1="z" u2="&#x21b;" k="12" />
    <hkern u1="z" u2="&#x1ce;" k="9" />
    <hkern u1="z" u2="&#x165;" k="12" />
    <hkern u1="z" u2="&#x153;" k="9" />
    <hkern u1="z" u2="&#x151;" k="9" />
    <hkern u1="z" u2="&#x14d;" k="9" />
    <hkern u1="z" u2="&#x123;" k="9" />
    <hkern u1="z" u2="&#x121;" k="9" />
    <hkern u1="z" u2="&#x11f;" k="9" />
    <hkern u1="z" u2="&#x11b;" k="9" />
    <hkern u1="z" u2="&#x119;" k="9" />
    <hkern u1="z" u2="&#x117;" k="9" />
    <hkern u1="z" u2="&#x113;" k="9" />
    <hkern u1="z" u2="&#x111;" k="9" />
    <hkern u1="z" u2="&#x10f;" k="9" />
    <hkern u1="z" u2="&#x10d;" k="9" />
    <hkern u1="z" u2="&#x10b;" k="9" />
    <hkern u1="z" u2="&#x107;" k="9" />
    <hkern u1="z" u2="&#x105;" k="9" />
    <hkern u1="z" u2="&#x103;" k="9" />
    <hkern u1="z" u2="&#x101;" k="9" />
    <hkern u1="z" u2="&#xf8;" k="9" />
    <hkern u1="z" u2="&#xf6;" k="9" />
    <hkern u1="z" u2="&#xf5;" k="9" />
    <hkern u1="z" u2="&#xf4;" k="9" />
    <hkern u1="z" u2="&#xf3;" k="9" />
    <hkern u1="z" u2="&#xf2;" k="9" />
    <hkern u1="z" u2="&#xeb;" k="9" />
    <hkern u1="z" u2="&#xea;" k="9" />
    <hkern u1="z" u2="&#xe9;" k="9" />
    <hkern u1="z" u2="&#xe8;" k="9" />
    <hkern u1="z" u2="&#xe7;" k="9" />
    <hkern u1="z" u2="&#xe6;" k="9" />
    <hkern u1="z" u2="&#xe5;" k="9" />
    <hkern u1="z" u2="&#xe4;" k="9" />
    <hkern u1="z" u2="&#xe3;" k="9" />
    <hkern u1="z" u2="&#xe2;" k="9" />
    <hkern u1="z" u2="&#xe1;" k="9" />
    <hkern u1="z" u2="&#xe0;" k="9" />
    <hkern u1="z" u2="t" k="12" />
    <hkern u1="z" u2="q" k="9" />
    <hkern u1="z" u2="o" k="13" />
    <hkern u1="z" u2="g" k="9" />
    <hkern u1="z" u2="f" k="12" />
    <hkern u1="z" u2="e" k="9" />
    <hkern u1="z" u2="d" k="9" />
    <hkern u1="z" u2="c" k="9" />
    <hkern u1="z" u2="a" k="9" />
    <hkern u1="z" u2="&#x40;" k="9" />
    <hkern u1="z" u2="&#x2f;" k="-18" />
    <hkern u1="z" u2="&#x2d;" k="23" />
    <hkern u1="&#x7b;" u2="j" k="-98" />
    <hkern u1="&#x7c;" u2="x" k="12" />
    <hkern u1="&#x7c;" u2="S" k="14" />
    <hkern u1="&#x7c;" u2="&#x2f;" k="-14" />
    <hkern u1="&#xa1;" g2="T_l" k="28" />
    <hkern u1="&#xa1;" g2="T_h" k="28" />
    <hkern u1="&#xa1;" g2="T_T" k="28" />
    <hkern u1="&#xa1;" u2="&#x1ef2;" k="36" />
    <hkern u1="&#xa1;" u2="&#x21a;" k="28" />
    <hkern u1="&#xa1;" u2="&#x178;" k="36" />
    <hkern u1="&#xa1;" u2="&#x176;" k="36" />
    <hkern u1="&#xa1;" u2="&#x164;" k="28" />
    <hkern u1="&#xa1;" u2="&#xdd;" k="36" />
    <hkern u1="&#xa1;" u2="j" k="-89" />
    <hkern u1="&#xa1;" u2="Y" k="36" />
    <hkern u1="&#xa1;" u2="W" k="18" />
    <hkern u1="&#xa1;" u2="V" k="18" />
    <hkern u1="&#xa1;" u2="T" k="28" />
    <hkern u1="&#xa3;" u2="&#x39;" k="18" />
    <hkern u1="&#xa3;" u2="&#x37;" k="18" />
    <hkern u1="&#xa3;" u2="&#x36;" k="28" />
    <hkern u1="&#xa3;" u2="&#x34;" k="39" />
    <hkern u1="&#xa5;" u2="&#x39;" k="7" />
    <hkern u1="&#xa5;" u2="&#x38;" k="4" />
    <hkern u1="&#xa5;" u2="&#x37;" k="31" />
    <hkern u1="&#xa5;" u2="&#x36;" k="7" />
    <hkern u1="&#xa5;" u2="&#x32;" k="18" />
    <hkern u1="&#xa5;" u2="&#x30;" k="7" />
    <hkern u1="&#xbf;" g2="oe.ss01" k="11" />
    <hkern u1="&#xbf;" g2="eogonek.ss01" k="11" />
    <hkern u1="&#xbf;" g2="emacron.ss01" k="11" />
    <hkern u1="&#xbf;" g2="egrave.ss01" k="11" />
    <hkern u1="&#xbf;" g2="edotaccent.ss01" k="11" />
    <hkern u1="&#xbf;" g2="edieresis.ss01" k="11" />
    <hkern u1="&#xbf;" g2="ecircumflex.ss01" k="11" />
    <hkern u1="&#xbf;" g2="ecaron.ss01" k="11" />
    <hkern u1="&#xbf;" g2="eacute.ss01" k="11" />
    <hkern u1="&#xbf;" g2="e.ss01" k="11" />
    <hkern u1="&#xbf;" g2="ae.ss01" k="11" />
    <hkern u1="&#xbf;" g2="T_l" k="95" />
    <hkern u1="&#xbf;" g2="T_h" k="95" />
    <hkern u1="&#xbf;" g2="T_T" k="95" />
    <hkern u1="&#xbf;" u2="&#x1ef2;" k="93" />
    <hkern u1="&#xbf;" u2="&#x21a;" k="95" />
    <hkern u1="&#xbf;" u2="&#x1ce;" k="11" />
    <hkern u1="&#xbf;" u2="&#x1cd;" k="29" />
    <hkern u1="&#xbf;" u2="&#x178;" k="93" />
    <hkern u1="&#xbf;" u2="&#x176;" k="93" />
    <hkern u1="&#xbf;" u2="&#x172;" k="19" />
    <hkern u1="&#xbf;" u2="&#x170;" k="19" />
    <hkern u1="&#xbf;" u2="&#x16e;" k="19" />
    <hkern u1="&#xbf;" u2="&#x16c;" k="19" />
    <hkern u1="&#xbf;" u2="&#x16a;" k="19" />
    <hkern u1="&#xbf;" u2="&#x164;" k="95" />
    <hkern u1="&#xbf;" u2="&#x153;" k="11" />
    <hkern u1="&#xbf;" u2="&#x152;" k="25" />
    <hkern u1="&#xbf;" u2="&#x151;" k="11" />
    <hkern u1="&#xbf;" u2="&#x150;" k="25" />
    <hkern u1="&#xbf;" u2="&#x14d;" k="11" />
    <hkern u1="&#xbf;" u2="&#x14c;" k="25" />
    <hkern u1="&#xbf;" u2="&#x123;" k="11" />
    <hkern u1="&#xbf;" u2="&#x122;" k="25" />
    <hkern u1="&#xbf;" u2="&#x121;" k="11" />
    <hkern u1="&#xbf;" u2="&#x120;" k="25" />
    <hkern u1="&#xbf;" u2="&#x11f;" k="11" />
    <hkern u1="&#xbf;" u2="&#x11e;" k="25" />
    <hkern u1="&#xbf;" u2="&#x11b;" k="11" />
    <hkern u1="&#xbf;" u2="&#x119;" k="11" />
    <hkern u1="&#xbf;" u2="&#x117;" k="11" />
    <hkern u1="&#xbf;" u2="&#x113;" k="11" />
    <hkern u1="&#xbf;" u2="&#x111;" k="11" />
    <hkern u1="&#xbf;" u2="&#x10f;" k="11" />
    <hkern u1="&#xbf;" u2="&#x10d;" k="11" />
    <hkern u1="&#xbf;" u2="&#x10c;" k="25" />
    <hkern u1="&#xbf;" u2="&#x10b;" k="11" />
    <hkern u1="&#xbf;" u2="&#x10a;" k="25" />
    <hkern u1="&#xbf;" u2="&#x107;" k="11" />
    <hkern u1="&#xbf;" u2="&#x106;" k="25" />
    <hkern u1="&#xbf;" u2="&#x105;" k="11" />
    <hkern u1="&#xbf;" u2="&#x104;" k="29" />
    <hkern u1="&#xbf;" u2="&#x103;" k="11" />
    <hkern u1="&#xbf;" u2="&#x102;" k="29" />
    <hkern u1="&#xbf;" u2="&#x101;" k="11" />
    <hkern u1="&#xbf;" u2="&#x100;" k="29" />
    <hkern u1="&#xbf;" u2="&#xf8;" k="11" />
    <hkern u1="&#xbf;" u2="&#xf6;" k="11" />
    <hkern u1="&#xbf;" u2="&#xf5;" k="11" />
    <hkern u1="&#xbf;" u2="&#xf4;" k="11" />
    <hkern u1="&#xbf;" u2="&#xf3;" k="11" />
    <hkern u1="&#xbf;" u2="&#xf2;" k="11" />
    <hkern u1="&#xbf;" u2="&#xeb;" k="11" />
    <hkern u1="&#xbf;" u2="&#xea;" k="11" />
    <hkern u1="&#xbf;" u2="&#xe9;" k="11" />
    <hkern u1="&#xbf;" u2="&#xe8;" k="11" />
    <hkern u1="&#xbf;" u2="&#xe7;" k="11" />
    <hkern u1="&#xbf;" u2="&#xe6;" k="11" />
    <hkern u1="&#xbf;" u2="&#xe5;" k="11" />
    <hkern u1="&#xbf;" u2="&#xe4;" k="11" />
    <hkern u1="&#xbf;" u2="&#xe3;" k="11" />
    <hkern u1="&#xbf;" u2="&#xe2;" k="11" />
    <hkern u1="&#xbf;" u2="&#xe1;" k="11" />
    <hkern u1="&#xbf;" u2="&#xe0;" k="11" />
    <hkern u1="&#xbf;" u2="&#xdd;" k="93" />
    <hkern u1="&#xbf;" u2="&#xdc;" k="19" />
    <hkern u1="&#xbf;" u2="&#xdb;" k="19" />
    <hkern u1="&#xbf;" u2="&#xda;" k="19" />
    <hkern u1="&#xbf;" u2="&#xd9;" k="19" />
    <hkern u1="&#xbf;" u2="&#xd8;" k="25" />
    <hkern u1="&#xbf;" u2="&#xd6;" k="25" />
    <hkern u1="&#xbf;" u2="&#xd5;" k="25" />
    <hkern u1="&#xbf;" u2="&#xd4;" k="25" />
    <hkern u1="&#xbf;" u2="&#xd3;" k="25" />
    <hkern u1="&#xbf;" u2="&#xd2;" k="25" />
    <hkern u1="&#xbf;" u2="&#xc7;" k="25" />
    <hkern u1="&#xbf;" u2="&#xc6;" k="29" />
    <hkern u1="&#xbf;" u2="&#xc5;" k="29" />
    <hkern u1="&#xbf;" u2="&#xc4;" k="29" />
    <hkern u1="&#xbf;" u2="&#xc3;" k="29" />
    <hkern u1="&#xbf;" u2="&#xc2;" k="29" />
    <hkern u1="&#xbf;" u2="&#xc1;" k="29" />
    <hkern u1="&#xbf;" u2="&#xc0;" k="29" />
    <hkern u1="&#xbf;" u2="q" k="11" />
    <hkern u1="&#xbf;" u2="o" k="11" />
    <hkern u1="&#xbf;" u2="j" k="-48" />
    <hkern u1="&#xbf;" u2="g" k="11" />
    <hkern u1="&#xbf;" u2="e" k="11" />
    <hkern u1="&#xbf;" u2="d" k="11" />
    <hkern u1="&#xbf;" u2="c" k="11" />
    <hkern u1="&#xbf;" u2="a" k="11" />
    <hkern u1="&#xbf;" u2="Y" k="93" />
    <hkern u1="&#xbf;" u2="W" k="56" />
    <hkern u1="&#xbf;" u2="V" k="64" />
    <hkern u1="&#xbf;" u2="U" k="19" />
    <hkern u1="&#xbf;" u2="T" k="95" />
    <hkern u1="&#xbf;" u2="Q" k="25" />
    <hkern u1="&#xbf;" u2="O" k="25" />
    <hkern u1="&#xbf;" u2="J" k="33" />
    <hkern u1="&#xbf;" u2="G" k="25" />
    <hkern u1="&#xbf;" u2="C" k="25" />
    <hkern u1="&#xbf;" u2="A" k="29" />
    <hkern u1="&#xbf;" u2="&#x40;" k="11" />
    <hkern u1="&#xc0;" g2="ampersand.ss01" k="28" />
    <hkern u1="&#xc0;" g2="g.ss01" k="18" />
    <hkern u1="&#xc0;" u2="y" k="52" />
    <hkern u1="&#xc0;" u2="x" k="46" />
    <hkern u1="&#xc0;" u2="w" k="57" />
    <hkern u1="&#xc0;" u2="j" k="8" />
    <hkern u1="&#xc0;" u2="Z" k="18" />
    <hkern u1="&#xc0;" u2="X" k="38" />
    <hkern u1="&#xc0;" u2="V" k="95" />
    <hkern u1="&#xc0;" u2="S" k="27" />
    <hkern u1="&#xc0;" u2="J" k="18" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="58" />
    <hkern u1="&#xc0;" u2="&#x2d;" k="40" />
    <hkern u1="&#xc0;" u2="&#x2a;" k="124" />
    <hkern u1="&#xc0;" u2="&#x20;" k="28" />
    <hkern u1="&#xc1;" g2="ampersand.ss01" k="28" />
    <hkern u1="&#xc1;" g2="g.ss01" k="18" />
    <hkern u1="&#xc1;" u2="y" k="52" />
    <hkern u1="&#xc1;" u2="x" k="46" />
    <hkern u1="&#xc1;" u2="w" k="57" />
    <hkern u1="&#xc1;" u2="j" k="8" />
    <hkern u1="&#xc1;" u2="Z" k="18" />
    <hkern u1="&#xc1;" u2="X" k="38" />
    <hkern u1="&#xc1;" u2="V" k="95" />
    <hkern u1="&#xc1;" u2="S" k="27" />
    <hkern u1="&#xc1;" u2="J" k="18" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="58" />
    <hkern u1="&#xc1;" u2="&#x2d;" k="40" />
    <hkern u1="&#xc1;" u2="&#x2a;" k="124" />
    <hkern u1="&#xc1;" u2="&#x20;" k="28" />
    <hkern u1="&#xc2;" g2="ampersand.ss01" k="28" />
    <hkern u1="&#xc2;" g2="g.ss01" k="18" />
    <hkern u1="&#xc2;" u2="y" k="52" />
    <hkern u1="&#xc2;" u2="x" k="46" />
    <hkern u1="&#xc2;" u2="w" k="57" />
    <hkern u1="&#xc2;" u2="j" k="8" />
    <hkern u1="&#xc2;" u2="Z" k="18" />
    <hkern u1="&#xc2;" u2="X" k="38" />
    <hkern u1="&#xc2;" u2="V" k="95" />
    <hkern u1="&#xc2;" u2="S" k="27" />
    <hkern u1="&#xc2;" u2="J" k="18" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="58" />
    <hkern u1="&#xc2;" u2="&#x2d;" k="40" />
    <hkern u1="&#xc2;" u2="&#x2a;" k="124" />
    <hkern u1="&#xc2;" u2="&#x20;" k="28" />
    <hkern u1="&#xc3;" g2="ampersand.ss01" k="28" />
    <hkern u1="&#xc3;" g2="g.ss01" k="18" />
    <hkern u1="&#xc3;" u2="y" k="52" />
    <hkern u1="&#xc3;" u2="x" k="46" />
    <hkern u1="&#xc3;" u2="w" k="57" />
    <hkern u1="&#xc3;" u2="j" k="8" />
    <hkern u1="&#xc3;" u2="Z" k="18" />
    <hkern u1="&#xc3;" u2="X" k="38" />
    <hkern u1="&#xc3;" u2="V" k="95" />
    <hkern u1="&#xc3;" u2="S" k="27" />
    <hkern u1="&#xc3;" u2="J" k="18" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="58" />
    <hkern u1="&#xc3;" u2="&#x2d;" k="40" />
    <hkern u1="&#xc3;" u2="&#x2a;" k="124" />
    <hkern u1="&#xc3;" u2="&#x20;" k="28" />
    <hkern u1="&#xc4;" g2="ampersand.ss01" k="28" />
    <hkern u1="&#xc4;" g2="g.ss01" k="18" />
    <hkern u1="&#xc4;" u2="y" k="52" />
    <hkern u1="&#xc4;" u2="x" k="46" />
    <hkern u1="&#xc4;" u2="w" k="57" />
    <hkern u1="&#xc4;" u2="j" k="8" />
    <hkern u1="&#xc4;" u2="Z" k="18" />
    <hkern u1="&#xc4;" u2="X" k="38" />
    <hkern u1="&#xc4;" u2="V" k="95" />
    <hkern u1="&#xc4;" u2="S" k="27" />
    <hkern u1="&#xc4;" u2="J" k="18" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="58" />
    <hkern u1="&#xc4;" u2="&#x2d;" k="40" />
    <hkern u1="&#xc4;" u2="&#x2a;" k="124" />
    <hkern u1="&#xc4;" u2="&#x20;" k="28" />
    <hkern u1="&#xc5;" g2="ampersand.ss01" k="28" />
    <hkern u1="&#xc5;" g2="g.ss01" k="18" />
    <hkern u1="&#xc5;" u2="y" k="52" />
    <hkern u1="&#xc5;" u2="x" k="46" />
    <hkern u1="&#xc5;" u2="w" k="57" />
    <hkern u1="&#xc5;" u2="j" k="8" />
    <hkern u1="&#xc5;" u2="Z" k="18" />
    <hkern u1="&#xc5;" u2="X" k="38" />
    <hkern u1="&#xc5;" u2="V" k="95" />
    <hkern u1="&#xc5;" u2="S" k="27" />
    <hkern u1="&#xc5;" u2="J" k="18" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="58" />
    <hkern u1="&#xc5;" u2="&#x2d;" k="40" />
    <hkern u1="&#xc5;" u2="&#x2a;" k="124" />
    <hkern u1="&#xc5;" u2="&#x20;" k="28" />
    <hkern u1="&#xc6;" u2="&#x7d;" k="-26" />
    <hkern u1="&#xc6;" u2="]" k="-26" />
    <hkern u1="&#xc6;" u2="Y" k="14" />
    <hkern u1="&#xc6;" u2="V" k="14" />
    <hkern u1="&#xc6;" u2="S" k="18" />
    <hkern u1="&#xc6;" u2="J" k="35" />
    <hkern u1="&#xc6;" u2="&#x2d;" k="34" />
    <hkern u1="&#xc6;" u2="&#x29;" k="-25" />
    <hkern u1="&#xc7;" g2="g.ss01" k="24" />
    <hkern u1="&#xc7;" u2="&#x7d;" k="-23" />
    <hkern u1="&#xc7;" u2="x" k="16" />
    <hkern u1="&#xc7;" u2="w" k="38" />
    <hkern u1="&#xc7;" u2="Y" k="28" />
    <hkern u1="&#xc7;" u2="S" k="12" />
    <hkern u1="&#xc7;" u2="J" k="48" />
    <hkern u1="&#xc7;" u2="&#x2d;" k="20" />
    <hkern u1="&#xc8;" u2="&#x7d;" k="-26" />
    <hkern u1="&#xc8;" u2="]" k="-26" />
    <hkern u1="&#xc8;" u2="Y" k="14" />
    <hkern u1="&#xc8;" u2="V" k="14" />
    <hkern u1="&#xc8;" u2="S" k="18" />
    <hkern u1="&#xc8;" u2="J" k="35" />
    <hkern u1="&#xc8;" u2="&#x2d;" k="34" />
    <hkern u1="&#xc8;" u2="&#x29;" k="-25" />
    <hkern u1="&#xc9;" u2="&#x7d;" k="-26" />
    <hkern u1="&#xc9;" u2="]" k="-26" />
    <hkern u1="&#xc9;" u2="Y" k="14" />
    <hkern u1="&#xc9;" u2="V" k="14" />
    <hkern u1="&#xc9;" u2="S" k="18" />
    <hkern u1="&#xc9;" u2="J" k="35" />
    <hkern u1="&#xc9;" u2="&#x2d;" k="34" />
    <hkern u1="&#xc9;" u2="&#x29;" k="-25" />
    <hkern u1="&#xca;" u2="&#x7d;" k="-26" />
    <hkern u1="&#xca;" u2="]" k="-26" />
    <hkern u1="&#xca;" u2="Y" k="14" />
    <hkern u1="&#xca;" u2="V" k="14" />
    <hkern u1="&#xca;" u2="S" k="18" />
    <hkern u1="&#xca;" u2="J" k="35" />
    <hkern u1="&#xca;" u2="&#x2d;" k="34" />
    <hkern u1="&#xca;" u2="&#x29;" k="-25" />
    <hkern u1="&#xcb;" u2="&#x7d;" k="-26" />
    <hkern u1="&#xcb;" u2="]" k="-26" />
    <hkern u1="&#xcb;" u2="Y" k="14" />
    <hkern u1="&#xcb;" u2="V" k="14" />
    <hkern u1="&#xcb;" u2="S" k="18" />
    <hkern u1="&#xcb;" u2="J" k="35" />
    <hkern u1="&#xcb;" u2="&#x2d;" k="34" />
    <hkern u1="&#xcb;" u2="&#x29;" k="-25" />
    <hkern u1="&#xcc;" u2="x" k="12" />
    <hkern u1="&#xcc;" u2="S" k="14" />
    <hkern u1="&#xcc;" u2="&#x2f;" k="-14" />
    <hkern u1="&#xcd;" u2="x" k="12" />
    <hkern u1="&#xcd;" u2="S" k="14" />
    <hkern u1="&#xcd;" u2="&#x2f;" k="-14" />
    <hkern u1="&#xce;" u2="x" k="12" />
    <hkern u1="&#xce;" u2="S" k="14" />
    <hkern u1="&#xce;" u2="&#x2f;" k="-14" />
    <hkern u1="&#xcf;" u2="x" k="12" />
    <hkern u1="&#xcf;" u2="S" k="14" />
    <hkern u1="&#xcf;" u2="&#x2f;" k="-14" />
    <hkern u1="&#xd1;" u2="x" k="12" />
    <hkern u1="&#xd1;" u2="S" k="14" />
    <hkern u1="&#xd1;" u2="&#x2f;" k="-14" />
    <hkern u1="&#xd2;" u2="x" k="11" />
    <hkern u1="&#xd2;" u2="j" k="-24" />
    <hkern u1="&#xd2;" u2="]" k="12" />
    <hkern u1="&#xd2;" u2="Z" k="14" />
    <hkern u1="&#xd2;" u2="X" k="41" />
    <hkern u1="&#xd2;" u2="W" k="32" />
    <hkern u1="&#xd2;" u2="V" k="39" />
    <hkern u1="&#xd2;" u2="S" k="19" />
    <hkern u1="&#xd2;" u2="J" k="62" />
    <hkern u1="&#xd2;" u2="&#x2f;" k="27" />
    <hkern u1="&#xd2;" u2="&#x2d;" k="-13" />
    <hkern u1="&#xd2;" u2="&#x29;" k="12" />
    <hkern u1="&#xd3;" u2="x" k="11" />
    <hkern u1="&#xd3;" u2="j" k="-24" />
    <hkern u1="&#xd3;" u2="]" k="12" />
    <hkern u1="&#xd3;" u2="Z" k="14" />
    <hkern u1="&#xd3;" u2="X" k="41" />
    <hkern u1="&#xd3;" u2="W" k="32" />
    <hkern u1="&#xd3;" u2="V" k="39" />
    <hkern u1="&#xd3;" u2="S" k="19" />
    <hkern u1="&#xd3;" u2="J" k="62" />
    <hkern u1="&#xd3;" u2="&#x2f;" k="27" />
    <hkern u1="&#xd3;" u2="&#x2d;" k="-13" />
    <hkern u1="&#xd3;" u2="&#x29;" k="12" />
    <hkern u1="&#xd4;" u2="x" k="11" />
    <hkern u1="&#xd4;" u2="j" k="-24" />
    <hkern u1="&#xd4;" u2="]" k="12" />
    <hkern u1="&#xd4;" u2="Z" k="14" />
    <hkern u1="&#xd4;" u2="X" k="41" />
    <hkern u1="&#xd4;" u2="W" k="32" />
    <hkern u1="&#xd4;" u2="V" k="39" />
    <hkern u1="&#xd4;" u2="S" k="19" />
    <hkern u1="&#xd4;" u2="J" k="62" />
    <hkern u1="&#xd4;" u2="&#x2f;" k="27" />
    <hkern u1="&#xd4;" u2="&#x2d;" k="-13" />
    <hkern u1="&#xd4;" u2="&#x29;" k="12" />
    <hkern u1="&#xd5;" u2="x" k="11" />
    <hkern u1="&#xd5;" u2="j" k="-24" />
    <hkern u1="&#xd5;" u2="]" k="12" />
    <hkern u1="&#xd5;" u2="Z" k="14" />
    <hkern u1="&#xd5;" u2="X" k="41" />
    <hkern u1="&#xd5;" u2="W" k="32" />
    <hkern u1="&#xd5;" u2="V" k="39" />
    <hkern u1="&#xd5;" u2="S" k="19" />
    <hkern u1="&#xd5;" u2="J" k="62" />
    <hkern u1="&#xd5;" u2="&#x2f;" k="27" />
    <hkern u1="&#xd5;" u2="&#x2d;" k="-13" />
    <hkern u1="&#xd5;" u2="&#x29;" k="12" />
    <hkern u1="&#xd6;" u2="x" k="11" />
    <hkern u1="&#xd6;" u2="j" k="-24" />
    <hkern u1="&#xd6;" u2="]" k="12" />
    <hkern u1="&#xd6;" u2="Z" k="14" />
    <hkern u1="&#xd6;" u2="X" k="41" />
    <hkern u1="&#xd6;" u2="W" k="32" />
    <hkern u1="&#xd6;" u2="V" k="39" />
    <hkern u1="&#xd6;" u2="S" k="19" />
    <hkern u1="&#xd6;" u2="J" k="62" />
    <hkern u1="&#xd6;" u2="&#x2f;" k="27" />
    <hkern u1="&#xd6;" u2="&#x2d;" k="-13" />
    <hkern u1="&#xd6;" u2="&#x29;" k="12" />
    <hkern u1="&#xd8;" u2="x" k="11" />
    <hkern u1="&#xd8;" u2="j" k="-24" />
    <hkern u1="&#xd8;" u2="]" k="12" />
    <hkern u1="&#xd8;" u2="Z" k="14" />
    <hkern u1="&#xd8;" u2="X" k="41" />
    <hkern u1="&#xd8;" u2="W" k="32" />
    <hkern u1="&#xd8;" u2="V" k="39" />
    <hkern u1="&#xd8;" u2="S" k="19" />
    <hkern u1="&#xd8;" u2="J" k="62" />
    <hkern u1="&#xd8;" u2="&#x2f;" k="27" />
    <hkern u1="&#xd8;" u2="&#x2d;" k="-13" />
    <hkern u1="&#xd8;" u2="&#x29;" k="12" />
    <hkern u1="&#xd9;" g2="g.ss01" k="8" />
    <hkern u1="&#xd9;" u2="X" k="18" />
    <hkern u1="&#xd9;" u2="S" k="8" />
    <hkern u1="&#xd9;" u2="J" k="48" />
    <hkern u1="&#xd9;" u2="&#x2f;" k="17" />
    <hkern u1="&#xda;" g2="g.ss01" k="8" />
    <hkern u1="&#xda;" u2="X" k="18" />
    <hkern u1="&#xda;" u2="S" k="8" />
    <hkern u1="&#xda;" u2="J" k="48" />
    <hkern u1="&#xda;" u2="&#x2f;" k="17" />
    <hkern u1="&#xdb;" g2="g.ss01" k="8" />
    <hkern u1="&#xdb;" u2="X" k="18" />
    <hkern u1="&#xdb;" u2="S" k="8" />
    <hkern u1="&#xdb;" u2="J" k="48" />
    <hkern u1="&#xdb;" u2="&#x2f;" k="17" />
    <hkern u1="&#xdc;" g2="g.ss01" k="8" />
    <hkern u1="&#xdc;" u2="X" k="18" />
    <hkern u1="&#xdc;" u2="S" k="8" />
    <hkern u1="&#xdc;" u2="J" k="48" />
    <hkern u1="&#xdc;" u2="&#x2f;" k="17" />
    <hkern u1="&#xdd;" g2="ampersand.ss01" k="56" />
    <hkern u1="&#xdd;" g2="g.ss01" k="118" />
    <hkern u1="&#xdd;" u2="z" k="96" />
    <hkern u1="&#xdd;" u2="x" k="118" />
    <hkern u1="&#xdd;" u2="w" k="91" />
    <hkern u1="&#xdd;" u2="s" k="139" />
    <hkern u1="&#xdd;" u2="j" k="44" />
    <hkern u1="&#xdd;" u2="i" k="38" />
    <hkern u1="&#xdd;" u2="Z" k="22" />
    <hkern u1="&#xdd;" u2="Y" k="42" />
    <hkern u1="&#xdd;" u2="X" k="42" />
    <hkern u1="&#xdd;" u2="W" k="36" />
    <hkern u1="&#xdd;" u2="V" k="32" />
    <hkern u1="&#xdd;" u2="U" k="12" />
    <hkern u1="&#xdd;" u2="S" k="70" />
    <hkern u1="&#xdd;" u2="J" k="145" />
    <hkern u1="&#xdd;" u2="&#x3f;" k="16" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="68" />
    <hkern u1="&#xdd;" u2="&#x2d;" k="85" />
    <hkern u1="&#xdd;" u2="&#x26;" k="61" />
    <hkern u1="&#xdd;" u2="&#x20;" k="28" />
    <hkern u1="&#xe6;" u2="x" k="15" />
    <hkern u1="&#xe6;" u2="w" k="6" />
    <hkern u1="&#xe6;" u2="&#x2d;" k="-16" />
    <hkern u1="&#xe7;" u2="x" k="3" />
    <hkern u1="&#xe7;" u2="&#x2d;" k="26" />
    <hkern u1="&#xe8;" u2="x" k="15" />
    <hkern u1="&#xe8;" u2="w" k="6" />
    <hkern u1="&#xe8;" u2="&#x2d;" k="-16" />
    <hkern u1="&#xe9;" u2="x" k="15" />
    <hkern u1="&#xe9;" u2="w" k="6" />
    <hkern u1="&#xe9;" u2="&#x2d;" k="-16" />
    <hkern u1="&#xea;" u2="x" k="15" />
    <hkern u1="&#xea;" u2="w" k="6" />
    <hkern u1="&#xea;" u2="&#x2d;" k="-16" />
    <hkern u1="&#xeb;" u2="x" k="15" />
    <hkern u1="&#xeb;" u2="w" k="6" />
    <hkern u1="&#xeb;" u2="&#x2d;" k="-16" />
    <hkern u1="&#xf2;" u2="z" k="16" />
    <hkern u1="&#xf2;" u2="x" k="15" />
    <hkern u1="&#xf2;" u2="w" k="14" />
    <hkern u1="&#xf2;" u2="&#x2d;" k="-16" />
    <hkern u1="&#xf3;" u2="z" k="16" />
    <hkern u1="&#xf3;" u2="x" k="15" />
    <hkern u1="&#xf3;" u2="w" k="14" />
    <hkern u1="&#xf3;" u2="&#x2d;" k="-16" />
    <hkern u1="&#xf4;" u2="z" k="16" />
    <hkern u1="&#xf4;" u2="x" k="15" />
    <hkern u1="&#xf4;" u2="w" k="14" />
    <hkern u1="&#xf4;" u2="&#x2d;" k="-16" />
    <hkern u1="&#xf5;" u2="z" k="16" />
    <hkern u1="&#xf5;" u2="x" k="15" />
    <hkern u1="&#xf5;" u2="w" k="14" />
    <hkern u1="&#xf5;" u2="&#x2d;" k="-16" />
    <hkern u1="&#xf6;" u2="z" k="16" />
    <hkern u1="&#xf6;" u2="x" k="15" />
    <hkern u1="&#xf6;" u2="w" k="14" />
    <hkern u1="&#xf6;" u2="&#x2d;" k="-16" />
    <hkern u1="&#xf8;" u2="z" k="16" />
    <hkern u1="&#xf8;" u2="x" k="15" />
    <hkern u1="&#xf8;" u2="w" k="14" />
    <hkern u1="&#xf8;" u2="&#x2d;" k="-16" />
    <hkern u1="&#xfe;" u2="z" k="16" />
    <hkern u1="&#xfe;" u2="x" k="15" />
    <hkern u1="&#xfe;" u2="w" k="14" />
    <hkern u1="&#xfe;" u2="&#x2d;" k="-16" />
    <hkern u1="&#x100;" g2="ampersand.ss01" k="28" />
    <hkern u1="&#x100;" g2="g.ss01" k="18" />
    <hkern u1="&#x100;" u2="y" k="52" />
    <hkern u1="&#x100;" u2="x" k="46" />
    <hkern u1="&#x100;" u2="w" k="57" />
    <hkern u1="&#x100;" u2="j" k="8" />
    <hkern u1="&#x100;" u2="Z" k="18" />
    <hkern u1="&#x100;" u2="X" k="38" />
    <hkern u1="&#x100;" u2="V" k="95" />
    <hkern u1="&#x100;" u2="S" k="27" />
    <hkern u1="&#x100;" u2="J" k="18" />
    <hkern u1="&#x100;" u2="&#x3f;" k="58" />
    <hkern u1="&#x100;" u2="&#x2d;" k="40" />
    <hkern u1="&#x100;" u2="&#x2a;" k="124" />
    <hkern u1="&#x100;" u2="&#x20;" k="28" />
    <hkern u1="&#x102;" g2="ampersand.ss01" k="28" />
    <hkern u1="&#x102;" g2="g.ss01" k="18" />
    <hkern u1="&#x102;" u2="y" k="52" />
    <hkern u1="&#x102;" u2="x" k="46" />
    <hkern u1="&#x102;" u2="w" k="57" />
    <hkern u1="&#x102;" u2="j" k="8" />
    <hkern u1="&#x102;" u2="Z" k="18" />
    <hkern u1="&#x102;" u2="X" k="38" />
    <hkern u1="&#x102;" u2="V" k="95" />
    <hkern u1="&#x102;" u2="S" k="27" />
    <hkern u1="&#x102;" u2="J" k="18" />
    <hkern u1="&#x102;" u2="&#x3f;" k="58" />
    <hkern u1="&#x102;" u2="&#x2d;" k="40" />
    <hkern u1="&#x102;" u2="&#x2a;" k="124" />
    <hkern u1="&#x102;" u2="&#x20;" k="28" />
    <hkern u1="&#x104;" g2="ampersand.ss01" k="28" />
    <hkern u1="&#x104;" g2="g.ss01" k="18" />
    <hkern u1="&#x104;" u2="y" k="52" />
    <hkern u1="&#x104;" u2="x" k="46" />
    <hkern u1="&#x104;" u2="w" k="57" />
    <hkern u1="&#x104;" u2="j" k="8" />
    <hkern u1="&#x104;" u2="Z" k="18" />
    <hkern u1="&#x104;" u2="X" k="38" />
    <hkern u1="&#x104;" u2="V" k="95" />
    <hkern u1="&#x104;" u2="S" k="27" />
    <hkern u1="&#x104;" u2="J" k="18" />
    <hkern u1="&#x104;" u2="&#x3f;" k="58" />
    <hkern u1="&#x104;" u2="&#x2d;" k="40" />
    <hkern u1="&#x104;" u2="&#x2a;" k="124" />
    <hkern u1="&#x104;" u2="&#x20;" k="28" />
    <hkern u1="&#x106;" g2="g.ss01" k="24" />
    <hkern u1="&#x106;" u2="&#x7d;" k="-23" />
    <hkern u1="&#x106;" u2="x" k="16" />
    <hkern u1="&#x106;" u2="w" k="38" />
    <hkern u1="&#x106;" u2="Y" k="28" />
    <hkern u1="&#x106;" u2="S" k="12" />
    <hkern u1="&#x106;" u2="J" k="48" />
    <hkern u1="&#x106;" u2="&#x2d;" k="20" />
    <hkern u1="&#x107;" u2="x" k="3" />
    <hkern u1="&#x107;" u2="&#x2d;" k="26" />
    <hkern u1="&#x10a;" g2="g.ss01" k="24" />
    <hkern u1="&#x10a;" u2="&#x7d;" k="-23" />
    <hkern u1="&#x10a;" u2="x" k="16" />
    <hkern u1="&#x10a;" u2="w" k="38" />
    <hkern u1="&#x10a;" u2="Y" k="28" />
    <hkern u1="&#x10a;" u2="S" k="12" />
    <hkern u1="&#x10a;" u2="J" k="48" />
    <hkern u1="&#x10a;" u2="&#x2d;" k="20" />
    <hkern u1="&#x10b;" u2="x" k="3" />
    <hkern u1="&#x10b;" u2="&#x2d;" k="26" />
    <hkern u1="&#x10c;" g2="g.ss01" k="24" />
    <hkern u1="&#x10c;" u2="&#x7d;" k="-23" />
    <hkern u1="&#x10c;" u2="x" k="16" />
    <hkern u1="&#x10c;" u2="w" k="38" />
    <hkern u1="&#x10c;" u2="Y" k="28" />
    <hkern u1="&#x10c;" u2="S" k="12" />
    <hkern u1="&#x10c;" u2="J" k="48" />
    <hkern u1="&#x10c;" u2="&#x2d;" k="20" />
    <hkern u1="&#x10d;" u2="x" k="3" />
    <hkern u1="&#x10d;" u2="&#x2d;" k="26" />
    <hkern u1="&#x112;" u2="&#x7d;" k="-26" />
    <hkern u1="&#x112;" u2="]" k="-26" />
    <hkern u1="&#x112;" u2="Y" k="14" />
    <hkern u1="&#x112;" u2="V" k="14" />
    <hkern u1="&#x112;" u2="S" k="18" />
    <hkern u1="&#x112;" u2="J" k="35" />
    <hkern u1="&#x112;" u2="&#x2d;" k="34" />
    <hkern u1="&#x112;" u2="&#x29;" k="-25" />
    <hkern u1="&#x113;" u2="x" k="15" />
    <hkern u1="&#x113;" u2="w" k="6" />
    <hkern u1="&#x113;" u2="&#x2d;" k="-16" />
    <hkern u1="&#x116;" u2="&#x7d;" k="-26" />
    <hkern u1="&#x116;" u2="]" k="-26" />
    <hkern u1="&#x116;" u2="Y" k="14" />
    <hkern u1="&#x116;" u2="V" k="14" />
    <hkern u1="&#x116;" u2="S" k="18" />
    <hkern u1="&#x116;" u2="J" k="35" />
    <hkern u1="&#x116;" u2="&#x2d;" k="34" />
    <hkern u1="&#x116;" u2="&#x29;" k="-25" />
    <hkern u1="&#x117;" u2="x" k="15" />
    <hkern u1="&#x117;" u2="w" k="6" />
    <hkern u1="&#x117;" u2="&#x2d;" k="-16" />
    <hkern u1="&#x118;" u2="&#x7d;" k="-26" />
    <hkern u1="&#x118;" u2="]" k="-26" />
    <hkern u1="&#x118;" u2="Y" k="14" />
    <hkern u1="&#x118;" u2="V" k="14" />
    <hkern u1="&#x118;" u2="S" k="18" />
    <hkern u1="&#x118;" u2="J" k="35" />
    <hkern u1="&#x118;" u2="&#x2d;" k="34" />
    <hkern u1="&#x118;" u2="&#x29;" k="-25" />
    <hkern u1="&#x119;" u2="x" k="15" />
    <hkern u1="&#x119;" u2="w" k="6" />
    <hkern u1="&#x119;" u2="&#x2d;" k="-16" />
    <hkern u1="&#x11a;" u2="&#x7d;" k="-26" />
    <hkern u1="&#x11a;" u2="]" k="-26" />
    <hkern u1="&#x11a;" u2="Y" k="14" />
    <hkern u1="&#x11a;" u2="V" k="14" />
    <hkern u1="&#x11a;" u2="S" k="18" />
    <hkern u1="&#x11a;" u2="J" k="35" />
    <hkern u1="&#x11a;" u2="&#x2d;" k="34" />
    <hkern u1="&#x11a;" u2="&#x29;" k="-25" />
    <hkern u1="&#x11b;" u2="x" k="15" />
    <hkern u1="&#x11b;" u2="w" k="6" />
    <hkern u1="&#x11b;" u2="&#x2d;" k="-16" />
    <hkern u1="&#x12a;" u2="x" k="12" />
    <hkern u1="&#x12a;" u2="S" k="14" />
    <hkern u1="&#x12a;" u2="&#x2f;" k="-14" />
    <hkern u1="&#x12e;" u2="x" k="12" />
    <hkern u1="&#x12e;" u2="S" k="14" />
    <hkern u1="&#x12e;" u2="&#x2f;" k="-14" />
    <hkern u1="&#x130;" u2="x" k="12" />
    <hkern u1="&#x130;" u2="S" k="14" />
    <hkern u1="&#x130;" u2="&#x2f;" k="-14" />
    <hkern u1="&#x143;" u2="x" k="12" />
    <hkern u1="&#x143;" u2="S" k="14" />
    <hkern u1="&#x143;" u2="&#x2f;" k="-14" />
    <hkern u1="&#x145;" u2="x" k="12" />
    <hkern u1="&#x145;" u2="S" k="14" />
    <hkern u1="&#x145;" u2="&#x2f;" k="-14" />
    <hkern u1="&#x147;" u2="x" k="12" />
    <hkern u1="&#x147;" u2="S" k="14" />
    <hkern u1="&#x147;" u2="&#x2f;" k="-14" />
    <hkern u1="&#x14a;" u2="x" k="12" />
    <hkern u1="&#x14a;" u2="S" k="14" />
    <hkern u1="&#x14a;" u2="&#x2f;" k="-14" />
    <hkern u1="&#x14c;" u2="x" k="11" />
    <hkern u1="&#x14c;" u2="j" k="-24" />
    <hkern u1="&#x14c;" u2="]" k="12" />
    <hkern u1="&#x14c;" u2="Z" k="14" />
    <hkern u1="&#x14c;" u2="X" k="41" />
    <hkern u1="&#x14c;" u2="W" k="32" />
    <hkern u1="&#x14c;" u2="V" k="39" />
    <hkern u1="&#x14c;" u2="S" k="19" />
    <hkern u1="&#x14c;" u2="J" k="62" />
    <hkern u1="&#x14c;" u2="&#x2f;" k="27" />
    <hkern u1="&#x14c;" u2="&#x2d;" k="-13" />
    <hkern u1="&#x14c;" u2="&#x29;" k="12" />
    <hkern u1="&#x14d;" u2="z" k="16" />
    <hkern u1="&#x14d;" u2="x" k="15" />
    <hkern u1="&#x14d;" u2="w" k="14" />
    <hkern u1="&#x14d;" u2="&#x2d;" k="-16" />
    <hkern u1="&#x150;" u2="x" k="11" />
    <hkern u1="&#x150;" u2="j" k="-24" />
    <hkern u1="&#x150;" u2="]" k="12" />
    <hkern u1="&#x150;" u2="Z" k="14" />
    <hkern u1="&#x150;" u2="X" k="41" />
    <hkern u1="&#x150;" u2="W" k="32" />
    <hkern u1="&#x150;" u2="V" k="39" />
    <hkern u1="&#x150;" u2="S" k="19" />
    <hkern u1="&#x150;" u2="J" k="62" />
    <hkern u1="&#x150;" u2="&#x2f;" k="27" />
    <hkern u1="&#x150;" u2="&#x2d;" k="-13" />
    <hkern u1="&#x150;" u2="&#x29;" k="12" />
    <hkern u1="&#x151;" u2="z" k="16" />
    <hkern u1="&#x151;" u2="x" k="15" />
    <hkern u1="&#x151;" u2="w" k="14" />
    <hkern u1="&#x151;" u2="&#x2d;" k="-16" />
    <hkern u1="&#x152;" u2="&#x7d;" k="-26" />
    <hkern u1="&#x152;" u2="]" k="-26" />
    <hkern u1="&#x152;" u2="Y" k="14" />
    <hkern u1="&#x152;" u2="V" k="14" />
    <hkern u1="&#x152;" u2="S" k="18" />
    <hkern u1="&#x152;" u2="J" k="35" />
    <hkern u1="&#x152;" u2="&#x2d;" k="34" />
    <hkern u1="&#x152;" u2="&#x29;" k="-25" />
    <hkern u1="&#x153;" u2="x" k="15" />
    <hkern u1="&#x153;" u2="w" k="6" />
    <hkern u1="&#x153;" u2="&#x2d;" k="-16" />
    <hkern u1="&#x164;" g2="g.ss01" k="118" />
    <hkern u1="&#x164;" u2="z" k="110" />
    <hkern u1="&#x164;" u2="x" k="128" />
    <hkern u1="&#x164;" u2="w" k="109" />
    <hkern u1="&#x164;" u2="s" k="120" />
    <hkern u1="&#x164;" u2="Z" k="40" />
    <hkern u1="&#x164;" u2="X" k="48" />
    <hkern u1="&#x164;" u2="W" k="30" />
    <hkern u1="&#x164;" u2="V" k="41" />
    <hkern u1="&#x164;" u2="S" k="43" />
    <hkern u1="&#x164;" u2="J" k="147" />
    <hkern u1="&#x164;" u2="&#x3f;" k="-10" />
    <hkern u1="&#x164;" u2="&#x2f;" k="48" />
    <hkern u1="&#x164;" u2="&#x2d;" k="78" />
    <hkern u1="&#x164;" u2="&#x26;" k="28" />
    <hkern u1="&#x164;" u2="&#x20;" k="16" />
    <hkern u1="&#x16a;" g2="g.ss01" k="8" />
    <hkern u1="&#x16a;" u2="X" k="18" />
    <hkern u1="&#x16a;" u2="S" k="8" />
    <hkern u1="&#x16a;" u2="J" k="48" />
    <hkern u1="&#x16a;" u2="&#x2f;" k="17" />
    <hkern u1="&#x16c;" g2="g.ss01" k="8" />
    <hkern u1="&#x16c;" u2="X" k="18" />
    <hkern u1="&#x16c;" u2="S" k="8" />
    <hkern u1="&#x16c;" u2="J" k="48" />
    <hkern u1="&#x16c;" u2="&#x2f;" k="17" />
    <hkern u1="&#x16e;" g2="g.ss01" k="8" />
    <hkern u1="&#x16e;" u2="X" k="18" />
    <hkern u1="&#x16e;" u2="S" k="8" />
    <hkern u1="&#x16e;" u2="J" k="48" />
    <hkern u1="&#x16e;" u2="&#x2f;" k="17" />
    <hkern u1="&#x170;" g2="g.ss01" k="8" />
    <hkern u1="&#x170;" u2="X" k="18" />
    <hkern u1="&#x170;" u2="S" k="8" />
    <hkern u1="&#x170;" u2="J" k="48" />
    <hkern u1="&#x170;" u2="&#x2f;" k="17" />
    <hkern u1="&#x172;" g2="g.ss01" k="8" />
    <hkern u1="&#x172;" u2="X" k="18" />
    <hkern u1="&#x172;" u2="S" k="8" />
    <hkern u1="&#x172;" u2="J" k="48" />
    <hkern u1="&#x172;" u2="&#x2f;" k="17" />
    <hkern u1="&#x176;" g2="ampersand.ss01" k="56" />
    <hkern u1="&#x176;" g2="g.ss01" k="118" />
    <hkern u1="&#x176;" u2="z" k="96" />
    <hkern u1="&#x176;" u2="x" k="118" />
    <hkern u1="&#x176;" u2="w" k="91" />
    <hkern u1="&#x176;" u2="s" k="139" />
    <hkern u1="&#x176;" u2="j" k="44" />
    <hkern u1="&#x176;" u2="i" k="38" />
    <hkern u1="&#x176;" u2="Z" k="22" />
    <hkern u1="&#x176;" u2="Y" k="42" />
    <hkern u1="&#x176;" u2="X" k="42" />
    <hkern u1="&#x176;" u2="W" k="36" />
    <hkern u1="&#x176;" u2="V" k="32" />
    <hkern u1="&#x176;" u2="U" k="12" />
    <hkern u1="&#x176;" u2="S" k="70" />
    <hkern u1="&#x176;" u2="J" k="145" />
    <hkern u1="&#x176;" u2="&#x3f;" k="16" />
    <hkern u1="&#x176;" u2="&#x2f;" k="68" />
    <hkern u1="&#x176;" u2="&#x2d;" k="85" />
    <hkern u1="&#x176;" u2="&#x26;" k="61" />
    <hkern u1="&#x176;" u2="&#x20;" k="28" />
    <hkern u1="&#x178;" g2="ampersand.ss01" k="56" />
    <hkern u1="&#x178;" g2="g.ss01" k="118" />
    <hkern u1="&#x178;" u2="z" k="96" />
    <hkern u1="&#x178;" u2="x" k="118" />
    <hkern u1="&#x178;" u2="w" k="91" />
    <hkern u1="&#x178;" u2="s" k="139" />
    <hkern u1="&#x178;" u2="j" k="44" />
    <hkern u1="&#x178;" u2="i" k="38" />
    <hkern u1="&#x178;" u2="Z" k="22" />
    <hkern u1="&#x178;" u2="Y" k="42" />
    <hkern u1="&#x178;" u2="X" k="42" />
    <hkern u1="&#x178;" u2="W" k="36" />
    <hkern u1="&#x178;" u2="V" k="32" />
    <hkern u1="&#x178;" u2="U" k="12" />
    <hkern u1="&#x178;" u2="S" k="70" />
    <hkern u1="&#x178;" u2="J" k="145" />
    <hkern u1="&#x178;" u2="&#x3f;" k="16" />
    <hkern u1="&#x178;" u2="&#x2f;" k="68" />
    <hkern u1="&#x178;" u2="&#x2d;" k="85" />
    <hkern u1="&#x178;" u2="&#x26;" k="61" />
    <hkern u1="&#x178;" u2="&#x20;" k="28" />
    <hkern u1="&#x1cd;" g2="ampersand.ss01" k="28" />
    <hkern u1="&#x1cd;" g2="g.ss01" k="18" />
    <hkern u1="&#x1cd;" u2="y" k="52" />
    <hkern u1="&#x1cd;" u2="x" k="46" />
    <hkern u1="&#x1cd;" u2="w" k="57" />
    <hkern u1="&#x1cd;" u2="j" k="8" />
    <hkern u1="&#x1cd;" u2="Z" k="18" />
    <hkern u1="&#x1cd;" u2="X" k="38" />
    <hkern u1="&#x1cd;" u2="V" k="95" />
    <hkern u1="&#x1cd;" u2="S" k="27" />
    <hkern u1="&#x1cd;" u2="J" k="18" />
    <hkern u1="&#x1cd;" u2="&#x3f;" k="58" />
    <hkern u1="&#x1cd;" u2="&#x2d;" k="40" />
    <hkern u1="&#x1cd;" u2="&#x2a;" k="124" />
    <hkern u1="&#x1cd;" u2="&#x20;" k="28" />
    <hkern u1="&#x21a;" g2="g.ss01" k="118" />
    <hkern u1="&#x21a;" u2="z" k="110" />
    <hkern u1="&#x21a;" u2="x" k="128" />
    <hkern u1="&#x21a;" u2="w" k="109" />
    <hkern u1="&#x21a;" u2="s" k="120" />
    <hkern u1="&#x21a;" u2="Z" k="40" />
    <hkern u1="&#x21a;" u2="X" k="48" />
    <hkern u1="&#x21a;" u2="W" k="30" />
    <hkern u1="&#x21a;" u2="V" k="41" />
    <hkern u1="&#x21a;" u2="S" k="43" />
    <hkern u1="&#x21a;" u2="J" k="147" />
    <hkern u1="&#x21a;" u2="&#x3f;" k="-10" />
    <hkern u1="&#x21a;" u2="&#x2f;" k="48" />
    <hkern u1="&#x21a;" u2="&#x2d;" k="78" />
    <hkern u1="&#x21a;" u2="&#x26;" k="28" />
    <hkern u1="&#x21a;" u2="&#x20;" k="16" />
    <hkern u1="&#x21b;" u2="z" k="14" />
    <hkern u1="&#x21b;" u2="x" k="7" />
    <hkern u1="&#x21b;" u2="s" k="10" />
    <hkern u1="&#x21b;" u2="&#x2d;" k="19" />
    <hkern u1="&#x2bc;" u2="z" k="38" />
    <hkern u1="&#x2bc;" u2="s" k="71" />
    <hkern u1="&#x2bc;" u2="X" k="18" />
    <hkern u1="&#x2bc;" u2="S" k="32" />
    <hkern u1="&#x2bc;" u2="J" k="118" />
    <hkern u1="&#x1ef2;" g2="ampersand.ss01" k="56" />
    <hkern u1="&#x1ef2;" g2="g.ss01" k="118" />
    <hkern u1="&#x1ef2;" u2="z" k="96" />
    <hkern u1="&#x1ef2;" u2="x" k="118" />
    <hkern u1="&#x1ef2;" u2="w" k="91" />
    <hkern u1="&#x1ef2;" u2="s" k="139" />
    <hkern u1="&#x1ef2;" u2="j" k="44" />
    <hkern u1="&#x1ef2;" u2="i" k="38" />
    <hkern u1="&#x1ef2;" u2="Z" k="22" />
    <hkern u1="&#x1ef2;" u2="Y" k="42" />
    <hkern u1="&#x1ef2;" u2="X" k="42" />
    <hkern u1="&#x1ef2;" u2="W" k="36" />
    <hkern u1="&#x1ef2;" u2="V" k="32" />
    <hkern u1="&#x1ef2;" u2="U" k="12" />
    <hkern u1="&#x1ef2;" u2="S" k="70" />
    <hkern u1="&#x1ef2;" u2="J" k="145" />
    <hkern u1="&#x1ef2;" u2="&#x3f;" k="16" />
    <hkern u1="&#x1ef2;" u2="&#x2f;" k="68" />
    <hkern u1="&#x1ef2;" u2="&#x2d;" k="85" />
    <hkern u1="&#x1ef2;" u2="&#x26;" k="61" />
    <hkern u1="&#x1ef2;" u2="&#x20;" k="28" />
    <hkern u1="&#x2018;" u2="s" k="36" />
    <hkern u1="&#x2018;" u2="J" k="133" />
    <hkern u1="&#x2019;" u2="z" k="38" />
    <hkern u1="&#x2019;" u2="s" k="71" />
    <hkern u1="&#x2019;" u2="X" k="18" />
    <hkern u1="&#x2019;" u2="S" k="32" />
    <hkern u1="&#x2019;" u2="J" k="118" />
    <hkern u1="&#x201c;" u2="s" k="36" />
    <hkern u1="&#x201c;" u2="J" k="133" />
    <hkern u1="&#x201d;" u2="z" k="38" />
    <hkern u1="&#x201d;" u2="s" k="71" />
    <hkern u1="&#x201d;" u2="X" k="18" />
    <hkern u1="&#x201d;" u2="S" k="32" />
    <hkern u1="&#x201d;" u2="J" k="118" />
    <hkern u1="&#x20ac;" u2="&#x34;" k="18" />
    <hkern g1="T_T" g2="g.ss01" k="118" />
    <hkern g1="T_T" u2="z" k="110" />
    <hkern g1="T_T" u2="x" k="128" />
    <hkern g1="T_T" u2="w" k="109" />
    <hkern g1="T_T" u2="s" k="120" />
    <hkern g1="T_T" u2="Z" k="40" />
    <hkern g1="T_T" u2="X" k="48" />
    <hkern g1="T_T" u2="W" k="30" />
    <hkern g1="T_T" u2="V" k="41" />
    <hkern g1="T_T" u2="S" k="43" />
    <hkern g1="T_T" u2="J" k="147" />
    <hkern g1="T_T" u2="&#x3f;" k="-10" />
    <hkern g1="T_T" u2="&#x2f;" k="48" />
    <hkern g1="T_T" u2="&#x2d;" k="78" />
    <hkern g1="T_T" u2="&#x26;" k="28" />
    <hkern g1="T_T" u2="&#x20;" k="16" />
    <hkern g1="ae.ss01" u2="x" k="15" />
    <hkern g1="ae.ss01" u2="w" k="6" />
    <hkern g1="ae.ss01" u2="&#x2d;" k="-16" />
    <hkern g1="e.ss01" u2="j" k="8" />
    <hkern g1="eacute.ss01" u2="x" k="15" />
    <hkern g1="eacute.ss01" u2="w" k="6" />
    <hkern g1="eacute.ss01" u2="&#x2d;" k="-16" />
    <hkern g1="ecaron.ss01" u2="x" k="15" />
    <hkern g1="ecaron.ss01" u2="w" k="6" />
    <hkern g1="ecaron.ss01" u2="&#x2d;" k="-16" />
    <hkern g1="ecircumflex.ss01" u2="x" k="15" />
    <hkern g1="ecircumflex.ss01" u2="w" k="6" />
    <hkern g1="ecircumflex.ss01" u2="&#x2d;" k="-16" />
    <hkern g1="edieresis.ss01" u2="x" k="15" />
    <hkern g1="edieresis.ss01" u2="w" k="6" />
    <hkern g1="edieresis.ss01" u2="&#x2d;" k="-16" />
    <hkern g1="edotaccent.ss01" u2="x" k="15" />
    <hkern g1="edotaccent.ss01" u2="w" k="6" />
    <hkern g1="edotaccent.ss01" u2="&#x2d;" k="-16" />
    <hkern g1="egrave.ss01" u2="x" k="15" />
    <hkern g1="egrave.ss01" u2="w" k="6" />
    <hkern g1="egrave.ss01" u2="&#x2d;" k="-16" />
    <hkern g1="emacron.ss01" u2="x" k="15" />
    <hkern g1="emacron.ss01" u2="w" k="6" />
    <hkern g1="emacron.ss01" u2="&#x2d;" k="-16" />
    <hkern g1="eogonek.ss01" u2="x" k="15" />
    <hkern g1="eogonek.ss01" u2="w" k="6" />
    <hkern g1="eogonek.ss01" u2="&#x2d;" k="-16" />
    <hkern g1="g.ss01" g2="oe.ss01" k="8" />
    <hkern g1="g.ss01" g2="eogonek.ss01" k="8" />
    <hkern g1="g.ss01" g2="emacron.ss01" k="8" />
    <hkern g1="g.ss01" g2="egrave.ss01" k="8" />
    <hkern g1="g.ss01" g2="edotaccent.ss01" k="8" />
    <hkern g1="g.ss01" g2="edieresis.ss01" k="8" />
    <hkern g1="g.ss01" g2="ecircumflex.ss01" k="8" />
    <hkern g1="g.ss01" g2="ecaron.ss01" k="8" />
    <hkern g1="g.ss01" g2="eacute.ss01" k="8" />
    <hkern g1="g.ss01" g2="e.ss01" k="8" />
    <hkern g1="g.ss01" g2="ae.ss01" k="8" />
    <hkern g1="g.ss01" u2="&#x1ef3;" k="8" />
    <hkern g1="g.ss01" u2="&#x1ce;" k="8" />
    <hkern g1="g.ss01" u2="&#x177;" k="8" />
    <hkern g1="g.ss01" u2="&#x153;" k="8" />
    <hkern g1="g.ss01" u2="&#x151;" k="8" />
    <hkern g1="g.ss01" u2="&#x14d;" k="8" />
    <hkern g1="g.ss01" u2="&#x123;" k="8" />
    <hkern g1="g.ss01" u2="&#x121;" k="8" />
    <hkern g1="g.ss01" u2="&#x11f;" k="8" />
    <hkern g1="g.ss01" u2="&#x11b;" k="8" />
    <hkern g1="g.ss01" u2="&#x119;" k="8" />
    <hkern g1="g.ss01" u2="&#x117;" k="8" />
    <hkern g1="g.ss01" u2="&#x113;" k="8" />
    <hkern g1="g.ss01" u2="&#x111;" k="8" />
    <hkern g1="g.ss01" u2="&#x10f;" k="8" />
    <hkern g1="g.ss01" u2="&#x10d;" k="8" />
    <hkern g1="g.ss01" u2="&#x10b;" k="8" />
    <hkern g1="g.ss01" u2="&#x107;" k="8" />
    <hkern g1="g.ss01" u2="&#x105;" k="8" />
    <hkern g1="g.ss01" u2="&#x103;" k="8" />
    <hkern g1="g.ss01" u2="&#x101;" k="8" />
    <hkern g1="g.ss01" u2="&#xff;" k="8" />
    <hkern g1="g.ss01" u2="&#xfd;" k="8" />
    <hkern g1="g.ss01" u2="&#xf8;" k="8" />
    <hkern g1="g.ss01" u2="&#xf6;" k="8" />
    <hkern g1="g.ss01" u2="&#xf5;" k="8" />
    <hkern g1="g.ss01" u2="&#xf4;" k="8" />
    <hkern g1="g.ss01" u2="&#xf3;" k="8" />
    <hkern g1="g.ss01" u2="&#xf2;" k="8" />
    <hkern g1="g.ss01" u2="&#xeb;" k="8" />
    <hkern g1="g.ss01" u2="&#xea;" k="8" />
    <hkern g1="g.ss01" u2="&#xe9;" k="8" />
    <hkern g1="g.ss01" u2="&#xe8;" k="8" />
    <hkern g1="g.ss01" u2="&#xe7;" k="8" />
    <hkern g1="g.ss01" u2="&#xe6;" k="8" />
    <hkern g1="g.ss01" u2="&#xe5;" k="8" />
    <hkern g1="g.ss01" u2="&#xe4;" k="8" />
    <hkern g1="g.ss01" u2="&#xe3;" k="8" />
    <hkern g1="g.ss01" u2="&#xe2;" k="8" />
    <hkern g1="g.ss01" u2="&#xe1;" k="8" />
    <hkern g1="g.ss01" u2="&#xe0;" k="8" />
    <hkern g1="g.ss01" u2="y" k="8" />
    <hkern g1="g.ss01" u2="v" k="8" />
    <hkern g1="g.ss01" u2="q" k="8" />
    <hkern g1="g.ss01" u2="o" k="8" />
    <hkern g1="g.ss01" u2="j" k="-82" />
    <hkern g1="g.ss01" u2="g" k="8" />
    <hkern g1="g.ss01" u2="e" k="8" />
    <hkern g1="g.ss01" u2="d" k="8" />
    <hkern g1="g.ss01" u2="c" k="8" />
    <hkern g1="g.ss01" u2="a" k="8" />
    <hkern g1="g.ss01" u2="&#x40;" k="8" />
    <hkern g1="k.ss01" g2="oe.ss01" k="28" />
    <hkern g1="k.ss01" g2="eogonek.ss01" k="28" />
    <hkern g1="k.ss01" g2="emacron.ss01" k="28" />
    <hkern g1="k.ss01" g2="egrave.ss01" k="28" />
    <hkern g1="k.ss01" g2="edotaccent.ss01" k="28" />
    <hkern g1="k.ss01" g2="edieresis.ss01" k="28" />
    <hkern g1="k.ss01" g2="ecircumflex.ss01" k="28" />
    <hkern g1="k.ss01" g2="ecaron.ss01" k="28" />
    <hkern g1="k.ss01" g2="eacute.ss01" k="28" />
    <hkern g1="k.ss01" g2="e.ss01" k="28" />
    <hkern g1="k.ss01" g2="ae.ss01" k="28" />
    <hkern g1="k.ss01" u2="&#x1ce;" k="28" />
    <hkern g1="k.ss01" u2="&#x153;" k="28" />
    <hkern g1="k.ss01" u2="&#x151;" k="28" />
    <hkern g1="k.ss01" u2="&#x14d;" k="28" />
    <hkern g1="k.ss01" u2="&#x123;" k="28" />
    <hkern g1="k.ss01" u2="&#x121;" k="28" />
    <hkern g1="k.ss01" u2="&#x11f;" k="28" />
    <hkern g1="k.ss01" u2="&#x11b;" k="28" />
    <hkern g1="k.ss01" u2="&#x119;" k="28" />
    <hkern g1="k.ss01" u2="&#x117;" k="28" />
    <hkern g1="k.ss01" u2="&#x113;" k="28" />
    <hkern g1="k.ss01" u2="&#x111;" k="28" />
    <hkern g1="k.ss01" u2="&#x10f;" k="28" />
    <hkern g1="k.ss01" u2="&#x10d;" k="28" />
    <hkern g1="k.ss01" u2="&#x10b;" k="28" />
    <hkern g1="k.ss01" u2="&#x107;" k="28" />
    <hkern g1="k.ss01" u2="&#x105;" k="28" />
    <hkern g1="k.ss01" u2="&#x103;" k="28" />
    <hkern g1="k.ss01" u2="&#x101;" k="28" />
    <hkern g1="k.ss01" u2="&#xf8;" k="28" />
    <hkern g1="k.ss01" u2="&#xf6;" k="28" />
    <hkern g1="k.ss01" u2="&#xf5;" k="28" />
    <hkern g1="k.ss01" u2="&#xf4;" k="28" />
    <hkern g1="k.ss01" u2="&#xf3;" k="28" />
    <hkern g1="k.ss01" u2="&#xf2;" k="28" />
    <hkern g1="k.ss01" u2="&#xeb;" k="28" />
    <hkern g1="k.ss01" u2="&#xea;" k="28" />
    <hkern g1="k.ss01" u2="&#xe9;" k="28" />
    <hkern g1="k.ss01" u2="&#xe8;" k="28" />
    <hkern g1="k.ss01" u2="&#xe7;" k="28" />
    <hkern g1="k.ss01" u2="&#xe6;" k="28" />
    <hkern g1="k.ss01" u2="&#xe5;" k="28" />
    <hkern g1="k.ss01" u2="&#xe4;" k="28" />
    <hkern g1="k.ss01" u2="&#xe3;" k="28" />
    <hkern g1="k.ss01" u2="&#xe2;" k="28" />
    <hkern g1="k.ss01" u2="&#xe1;" k="28" />
    <hkern g1="k.ss01" u2="&#xe0;" k="28" />
    <hkern g1="k.ss01" u2="q" k="28" />
    <hkern g1="k.ss01" u2="o" k="28" />
    <hkern g1="k.ss01" u2="g" k="28" />
    <hkern g1="k.ss01" u2="e" k="28" />
    <hkern g1="k.ss01" u2="d" k="28" />
    <hkern g1="k.ss01" u2="c" k="28" />
    <hkern g1="k.ss01" u2="a" k="28" />
    <hkern g1="k.ss01" u2="&#x40;" k="28" />
    <hkern g1="oe.ss01" u2="x" k="15" />
    <hkern g1="oe.ss01" u2="w" k="6" />
    <hkern g1="oe.ss01" u2="&#x2d;" k="-16" />
    <hkern g1="t.ss01" g2="f_t" k="30" />
    <hkern g1="t.ss01" g2="f_j" k="30" />
    <hkern g1="t.ss01" g2="f_f" k="30" />
    <hkern g1="t.ss01" g2="t.ss01" k="30" />
    <hkern g1="t.ss01" g2="oe.ss01" k="18" />
    <hkern g1="t.ss01" g2="eogonek.ss01" k="18" />
    <hkern g1="t.ss01" g2="emacron.ss01" k="18" />
    <hkern g1="t.ss01" g2="egrave.ss01" k="18" />
    <hkern g1="t.ss01" g2="edotaccent.ss01" k="18" />
    <hkern g1="t.ss01" g2="edieresis.ss01" k="18" />
    <hkern g1="t.ss01" g2="ecircumflex.ss01" k="18" />
    <hkern g1="t.ss01" g2="ecaron.ss01" k="18" />
    <hkern g1="t.ss01" g2="eacute.ss01" k="18" />
    <hkern g1="t.ss01" g2="e.ss01" k="18" />
    <hkern g1="t.ss01" g2="ae.ss01" k="18" />
    <hkern g1="t.ss01" g2="fl" k="30" />
    <hkern g1="t.ss01" g2="fi" k="30" />
    <hkern g1="t.ss01" u2="&#x21b;" k="30" />
    <hkern g1="t.ss01" u2="&#x1ce;" k="18" />
    <hkern g1="t.ss01" u2="&#x165;" k="30" />
    <hkern g1="t.ss01" u2="&#x153;" k="18" />
    <hkern g1="t.ss01" u2="&#x151;" k="18" />
    <hkern g1="t.ss01" u2="&#x14d;" k="18" />
    <hkern g1="t.ss01" u2="&#x123;" k="18" />
    <hkern g1="t.ss01" u2="&#x121;" k="18" />
    <hkern g1="t.ss01" u2="&#x11f;" k="18" />
    <hkern g1="t.ss01" u2="&#x11b;" k="18" />
    <hkern g1="t.ss01" u2="&#x119;" k="18" />
    <hkern g1="t.ss01" u2="&#x117;" k="18" />
    <hkern g1="t.ss01" u2="&#x113;" k="18" />
    <hkern g1="t.ss01" u2="&#x111;" k="18" />
    <hkern g1="t.ss01" u2="&#x10f;" k="18" />
    <hkern g1="t.ss01" u2="&#x10d;" k="18" />
    <hkern g1="t.ss01" u2="&#x10b;" k="18" />
    <hkern g1="t.ss01" u2="&#x107;" k="18" />
    <hkern g1="t.ss01" u2="&#x105;" k="18" />
    <hkern g1="t.ss01" u2="&#x103;" k="18" />
    <hkern g1="t.ss01" u2="&#x101;" k="18" />
    <hkern g1="t.ss01" u2="&#xf8;" k="18" />
    <hkern g1="t.ss01" u2="&#xf6;" k="18" />
    <hkern g1="t.ss01" u2="&#xf5;" k="18" />
    <hkern g1="t.ss01" u2="&#xf4;" k="18" />
    <hkern g1="t.ss01" u2="&#xf3;" k="18" />
    <hkern g1="t.ss01" u2="&#xf2;" k="18" />
    <hkern g1="t.ss01" u2="&#xeb;" k="18" />
    <hkern g1="t.ss01" u2="&#xea;" k="18" />
    <hkern g1="t.ss01" u2="&#xe9;" k="18" />
    <hkern g1="t.ss01" u2="&#xe8;" k="18" />
    <hkern g1="t.ss01" u2="&#xe7;" k="18" />
    <hkern g1="t.ss01" u2="&#xe6;" k="18" />
    <hkern g1="t.ss01" u2="&#xe5;" k="18" />
    <hkern g1="t.ss01" u2="&#xe4;" k="18" />
    <hkern g1="t.ss01" u2="&#xe3;" k="18" />
    <hkern g1="t.ss01" u2="&#xe2;" k="18" />
    <hkern g1="t.ss01" u2="&#xe1;" k="18" />
    <hkern g1="t.ss01" u2="&#xe0;" k="18" />
    <hkern g1="t.ss01" u2="t" k="30" />
    <hkern g1="t.ss01" u2="q" k="18" />
    <hkern g1="t.ss01" u2="o" k="18" />
    <hkern g1="t.ss01" u2="g" k="18" />
    <hkern g1="t.ss01" u2="f" k="30" />
    <hkern g1="t.ss01" u2="e" k="18" />
    <hkern g1="t.ss01" u2="d" k="18" />
    <hkern g1="t.ss01" u2="c" k="18" />
    <hkern g1="t.ss01" u2="a" k="18" />
    <hkern g1="t.ss01" u2="&#x40;" k="18" />
    <hkern g1="f_f" g2="g.ss01" k="24" />
    <hkern g1="f_f" u2="&#x7d;" k="-54" />
    <hkern g1="f_f" u2="z" k="18" />
    <hkern g1="f_f" u2="x" k="26" />
    <hkern g1="f_f" u2="w" k="14" />
    <hkern g1="f_f" u2="s" k="18" />
    <hkern g1="f_f" u2="]" k="-56" />
    <hkern g1="f_f" u2="&#x3f;" k="-44" />
    <hkern g1="f_f" u2="&#x2f;" k="20" />
    <hkern g1="f_f" u2="&#x2d;" k="52" />
    <hkern g1="f_f" u2="&#x2a;" k="-18" />
    <hkern g1="f_f" u2="&#x29;" k="-56" />
    <hkern g1="f_t" u2="z" k="14" />
    <hkern g1="f_t" u2="x" k="7" />
    <hkern g1="f_t" u2="s" k="10" />
    <hkern g1="f_t" u2="&#x2d;" k="19" />
    <hkern g1="o_u_t_f_i_t" u2="z" k="14" />
    <hkern g1="o_u_t_f_i_t" u2="x" k="7" />
    <hkern g1="o_u_t_f_i_t" u2="s" k="10" />
    <hkern g1="o_u_t_f_i_t" u2="&#x2d;" k="19" />
    <hkern g1="r_f" g2="g.ss01" k="24" />
    <hkern g1="r_f" u2="&#x7d;" k="-54" />
    <hkern g1="r_f" u2="z" k="18" />
    <hkern g1="r_f" u2="x" k="26" />
    <hkern g1="r_f" u2="w" k="14" />
    <hkern g1="r_f" u2="s" k="18" />
    <hkern g1="r_f" u2="]" k="-56" />
    <hkern g1="r_f" u2="&#x3f;" k="-44" />
    <hkern g1="r_f" u2="&#x2f;" k="20" />
    <hkern g1="r_f" u2="&#x2d;" k="52" />
    <hkern g1="r_f" u2="&#x2a;" k="-18" />
    <hkern g1="r_f" u2="&#x29;" k="-56" />
    <hkern g1="r_t" u2="z" k="14" />
    <hkern g1="r_t" u2="x" k="7" />
    <hkern g1="r_t" u2="s" k="10" />
    <hkern g1="r_t" u2="&#x2d;" k="19" />
    <hkern g1="t_f" g2="g.ss01" k="24" />
    <hkern g1="t_f" u2="&#x7d;" k="-54" />
    <hkern g1="t_f" u2="z" k="18" />
    <hkern g1="t_f" u2="x" k="26" />
    <hkern g1="t_f" u2="w" k="14" />
    <hkern g1="t_f" u2="s" k="18" />
    <hkern g1="t_f" u2="]" k="-56" />
    <hkern g1="t_f" u2="&#x3f;" k="-44" />
    <hkern g1="t_f" u2="&#x2f;" k="20" />
    <hkern g1="t_f" u2="&#x2d;" k="52" />
    <hkern g1="t_f" u2="&#x2a;" k="-18" />
    <hkern g1="t_f" u2="&#x29;" k="-56" />
    <hkern g1="t_t" u2="z" k="14" />
    <hkern g1="t_t" u2="x" k="7" />
    <hkern g1="t_t" u2="s" k="10" />
    <hkern g1="t_t" u2="&#x2d;" k="19" />
    <hkern g1="ampersand.ss01" u2="&#x1ef2;" k="32" />
    <hkern g1="ampersand.ss01" u2="&#x1cd;" k="36" />
    <hkern g1="ampersand.ss01" u2="&#x178;" k="32" />
    <hkern g1="ampersand.ss01" u2="&#x176;" k="32" />
    <hkern g1="ampersand.ss01" u2="&#x104;" k="36" />
    <hkern g1="ampersand.ss01" u2="&#x102;" k="36" />
    <hkern g1="ampersand.ss01" u2="&#x100;" k="36" />
    <hkern g1="ampersand.ss01" u2="&#xdd;" k="32" />
    <hkern g1="ampersand.ss01" u2="&#xc6;" k="36" />
    <hkern g1="ampersand.ss01" u2="&#xc5;" k="36" />
    <hkern g1="ampersand.ss01" u2="&#xc4;" k="36" />
    <hkern g1="ampersand.ss01" u2="&#xc3;" k="36" />
    <hkern g1="ampersand.ss01" u2="&#xc2;" k="36" />
    <hkern g1="ampersand.ss01" u2="&#xc1;" k="36" />
    <hkern g1="ampersand.ss01" u2="&#xc0;" k="36" />
    <hkern g1="ampersand.ss01" u2="Y" k="32" />
    <hkern g1="ampersand.ss01" u2="X" k="32" />
    <hkern g1="ampersand.ss01" u2="W" k="8" />
    <hkern g1="ampersand.ss01" u2="V" k="18" />
    <hkern g1="ampersand.ss01" u2="J" k="48" />
    <hkern g1="ampersand.ss01" u2="A" k="36" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron,oe,ae.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	g2="f,t,tcaron,uni021B,fi,fl,t.ss01,f_f,f_j,f_t"
	k="14" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron,oe,ae.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	g2="v,y,yacute,ydieresis,ycircumflex,ygrave"
	k="8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,edotaccent,eogonek,ecaron,oe,ae.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	g2="apostrophe,quoteright,quotedblright"
	k="28" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng,T_h,m.ss01,n.ss01,nacute.ss01,ncaron.ss01,uni0146.ss01,ntilde.ss01,eng.ss01"
	g2="f,t,tcaron,uni021B,fi,fl,t.ss01,f_f,f_j,f_t"
	k="6" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng,T_h,m.ss01,n.ss01,nacute.ss01,ncaron.ss01,uni0146.ss01,ntilde.ss01,eng.ss01"
	g2="apostrophe,quoteright,quotedblright"
	k="23" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng,T_h,m.ss01,n.ss01,nacute.ss01,ncaron.ss01,uni0146.ss01,ntilde.ss01,eng.ss01"
	g2="quotedbl,quotesingle"
	k="36" />
    <hkern g1="at,b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,ohungarumlaut"
	g2="f,t,tcaron,uni021B,fi,fl,t.ss01,f_f,f_j,f_t"
	k="15" />
    <hkern g1="at,b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,ohungarumlaut"
	g2="v,y,yacute,ydieresis,ycircumflex,ygrave"
	k="18" />
    <hkern g1="at,b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,ohungarumlaut"
	g2="apostrophe,quoteright,quotedblright"
	k="58" />
    <hkern g1="at,b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,ohungarumlaut"
	g2="comma,period"
	k="19" />
    <hkern g1="at,b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,ohungarumlaut"
	g2="quotedbl,quotesingle"
	k="68" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01CD"
	g2="at,a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe,uni01CE,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01CD"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,bar,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron"
	k="8" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01CD"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="52" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01CD"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,o_u_t_f_i_t,o_u_t_f_i_t_t_e_d,o_u_t_f_i_t_t_e_r"
	k="18" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01CD"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01CD"
	k="38" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01CD"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01CD"
	g2="f,t,tcaron,uni021B,fi,fl,t.ss01,f_f,f_j,f_t"
	k="43" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01CD"
	g2="T,Tcaron,uni021A,T_T,T_h,T_l"
	k="101" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01CD"
	g2="v,y,yacute,ydieresis,ycircumflex,ygrave"
	k="69" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01CD"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="85" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01CD"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="105" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01CD"
	g2="apostrophe,quoteright,quotedblright"
	k="117" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01CD"
	g2="colon,semicolon"
	k="14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01CD"
	g2="quotedbl,quotesingle"
	k="124" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="at,a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe,uni01CE,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	k="9" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="T,Tcaron,uni021A,T_T,T_h,T_l"
	k="26" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01CD"
	k="22" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="T,Tcaron,uni021A,T_T,T_h,T_l"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="12" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="apostrophe,quoteright,quotedblright"
	k="7" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="quotedbl,quotesingle"
	k="14" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Ohungarumlaut"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="-6" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Ohungarumlaut"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01CD"
	k="52" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Ohungarumlaut"
	g2="T,Tcaron,uni021A,T_T,T_h,T_l"
	k="50" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Ohungarumlaut"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="61" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Ohungarumlaut"
	g2="apostrophe,quoteright,quotedblright"
	k="9" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Ohungarumlaut"
	g2="comma,period"
	k="36" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Ohungarumlaut"
	g2="quotedbl,quotesingle"
	k="38" />
    <hkern g1="t,uni021B,f_t,o_u_t_f_i_t,r_t,t_t"
	g2="at,a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe,uni01CE,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	k="15" />
    <hkern g1="t,uni021B,f_t,o_u_t_f_i_t,r_t,t_t"
	g2="f,t,tcaron,uni021B,fi,fl,t.ss01,f_f,f_j,f_t"
	k="18" />
    <hkern g1="t,uni021B,f_t,o_u_t_f_i_t,r_t,t_t"
	g2="apostrophe,quoteright,quotedblright"
	k="-18" />
    <hkern g1="t,uni021B,f_t,o_u_t_f_i_t,r_t,t_t"
	g2="comma,period"
	k="34" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="at,a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe,uni01CE,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	k="16" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="28" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01CD"
	k="18" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="f,t,tcaron,uni021B,fi,fl,t.ss01,f_f,f_j,f_t"
	k="27" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="T,Tcaron,uni021A,T_T,T_h,T_l"
	k="16" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="v,y,yacute,ydieresis,ycircumflex,ygrave"
	k="38" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01CD"
	k="85" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at,a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe,uni01CE,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	k="147" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,bar,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron"
	k="24" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="m,n,p,r,ntilde,thorn,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron,m.ss01,n.ss01,nacute.ss01,ncaron.ss01,uni0146.ss01,ntilde.ss01,eng.ss01,r_f,r_t"
	k="94" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="61" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,o_u_t_f_i_t,o_u_t_f_i_t_t_e_d,o_u_t_f_i_t_t_e_r"
	k="94" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01CD"
	k="105" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="b,h,k,l,uni0137,lacute,uni013C,lcaron,lslash,k.ss01,uni0137.ss01"
	k="24" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="f,t,tcaron,uni021B,fi,fl,t.ss01,f_f,f_j,f_t"
	k="71" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="T,Tcaron,uni021A,T_T,T_h,T_l"
	k="47" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="v,y,yacute,ydieresis,ycircumflex,ygrave"
	k="102" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="apostrophe,quoteright,quotedblright"
	k="16" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="colon,semicolon"
	k="86" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,period"
	k="111" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="at,a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe,uni01CE,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	k="9" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="f,t,tcaron,uni021B,fi,fl,t.ss01,f_f,f_j,f_t"
	k="6" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="apostrophe,quoteright,quotedblright"
	k="20" />
    <hkern g1="T,Tcaron,uni021A,T_T"
	g2="at,a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe,uni01CE,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	k="131" />
    <hkern g1="T,Tcaron,uni021A,T_T"
	g2="m,n,p,r,ntilde,thorn,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron,m.ss01,n.ss01,nacute.ss01,ncaron.ss01,uni0146.ss01,ntilde.ss01,eng.ss01,r_f,r_t"
	k="106" />
    <hkern g1="T,Tcaron,uni021A,T_T"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="50" />
    <hkern g1="T,Tcaron,uni021A,T_T"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,o_u_t_f_i_t,o_u_t_f_i_t_t_e_d,o_u_t_f_i_t_t_e_r"
	k="98" />
    <hkern g1="T,Tcaron,uni021A,T_T"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01CD"
	k="101" />
    <hkern g1="T,Tcaron,uni021A,T_T"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="T,Tcaron,uni021A,T_T"
	g2="f,t,tcaron,uni021B,fi,fl,t.ss01,f_f,f_j,f_t"
	k="59" />
    <hkern g1="T,Tcaron,uni021A,T_T"
	g2="T,Tcaron,uni021A,T_T,T_h,T_l"
	k="45" />
    <hkern g1="T,Tcaron,uni021A,T_T"
	g2="v,y,yacute,ydieresis,ycircumflex,ygrave"
	k="119" />
    <hkern g1="T,Tcaron,uni021A,T_T"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="47" />
    <hkern g1="T,Tcaron,uni021A,T_T"
	g2="apostrophe,quoteright,quotedblright"
	k="-20" />
    <hkern g1="T,Tcaron,uni021A,T_T"
	g2="colon,semicolon"
	k="95" />
    <hkern g1="T,Tcaron,uni021A,T_T"
	g2="comma,period"
	k="68" />
    <hkern g1="T,Tcaron,uni021A,T_T"
	g2="quotedbl,quotesingle"
	k="-25" />
    <hkern g1="f,f_f,r_f,t_f"
	g2="at,a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe,uni01CE,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	k="27" />
    <hkern g1="f,f_f,r_f,t_f"
	g2="m,n,p,r,ntilde,thorn,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron,m.ss01,n.ss01,nacute.ss01,ncaron.ss01,uni0146.ss01,ntilde.ss01,eng.ss01,r_f,r_t"
	k="18" />
    <hkern g1="f,f_f,r_f,t_f"
	g2="b,h,k,l,uni0137,lacute,uni013C,lcaron,lslash,k.ss01,uni0137.ss01"
	k="-24" />
    <hkern g1="f,f_f,r_f,t_f"
	g2="f,t,tcaron,uni021B,fi,fl,t.ss01,f_f,f_j,f_t"
	k="28" />
    <hkern g1="f,f_f,r_f,t_f"
	g2="v,y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="f,f_f,r_f,t_f"
	g2="apostrophe,quoteright,quotedblright"
	k="-51" />
    <hkern g1="f,f_f,r_f,t_f"
	g2="comma,period"
	k="52" />
    <hkern g1="f,f_f,r_f,t_f"
	g2="quotedbl,quotesingle"
	k="-63" />
    <hkern g1="apostrophe,quoteright,quotedblright"
	g2="at,a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe,uni01CE,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	k="88" />
    <hkern g1="apostrophe,quoteright,quotedblright"
	g2="m,n,p,r,ntilde,thorn,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron,m.ss01,n.ss01,nacute.ss01,ncaron.ss01,uni0146.ss01,ntilde.ss01,eng.ss01,r_f,r_t"
	k="40" />
    <hkern g1="apostrophe,quoteright,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="43" />
    <hkern g1="apostrophe,quoteright,quotedblright"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,o_u_t_f_i_t,o_u_t_f_i_t_t_e_d,o_u_t_f_i_t_t_e_r"
	k="42" />
    <hkern g1="apostrophe,quoteright,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01CD"
	k="142" />
    <hkern g1="apostrophe,quoteright,quotedblright"
	g2="f,t,tcaron,uni021B,fi,fl,t.ss01,f_f,f_j,f_t"
	k="10" />
    <hkern g1="apostrophe,quoteright,quotedblright"
	g2="T,Tcaron,uni021A,T_T,T_h,T_l"
	k="-24" />
    <hkern g1="apostrophe,quoteright,quotedblright"
	g2="v,y,yacute,ydieresis,ycircumflex,ygrave"
	k="24" />
    <hkern g1="comma,period"
	g2="at,a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe,uni01CE,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	k="19" />
    <hkern g1="comma,period"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="36" />
    <hkern g1="comma,period"
	g2="f,t,tcaron,uni021B,fi,fl,t.ss01,f_f,f_j,f_t"
	k="38" />
    <hkern g1="comma,period"
	g2="T,Tcaron,uni021A,T_T,T_h,T_l"
	k="68" />
    <hkern g1="comma,period"
	g2="v,y,yacute,ydieresis,ycircumflex,ygrave"
	k="50" />
    <hkern g1="comma,period"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="111" />
    <hkern g1="comma,period"
	g2="apostrophe,quoteright,quotedblright"
	k="93" />
    <hkern g1="quotedbl,quotesingle"
	g2="at,a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe,uni01CE,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	k="68" />
    <hkern g1="quotedbl,quotesingle"
	g2="m,n,p,r,ntilde,thorn,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron,m.ss01,n.ss01,nacute.ss01,ncaron.ss01,uni0146.ss01,ntilde.ss01,eng.ss01,r_f,r_t"
	k="21" />
    <hkern g1="quotedbl,quotesingle"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="38" />
    <hkern g1="quotedbl,quotesingle"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek,o_u_t_f_i_t,o_u_t_f_i_t_t_e_d,o_u_t_f_i_t_t_e_r"
	k="14" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01CD"
	k="124" />
    <hkern g1="quotedbl,quotesingle"
	g2="f,t,tcaron,uni021B,fi,fl,t.ss01,f_f,f_j,f_t"
	k="-8" />
    <hkern g1="quotedbl,quotesingle"
	g2="T,Tcaron,uni021A,T_T,T_h,T_l"
	k="-25" />
    <hkern g1="quotedbl,quotesingle"
	g2="v,y,yacute,ydieresis,ycircumflex,ygrave"
	k="3" />
    <hkern g1="quoteleft,quotedblleft"
	g2="at,a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe,uni01CE,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	k="58" />
    <hkern g1="quoteleft,quotedblleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01CD"
	k="109" />
    <hkern g1="quoteleft,quotedblleft"
	g2="f,t,tcaron,uni021B,fi,fl,t.ss01,f_f,f_j,f_t"
	k="-18" />
    <hkern g1="quoteleft,quotedblleft"
	g2="T,Tcaron,uni021A,T_T,T_h,T_l"
	k="-20" />
    <hkern g1="v"
	g2="at,a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe,uni01CE,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01,oe.ss01"
	k="18" />
    <hkern g1="v"
	g2="v,y,yacute,ydieresis,ycircumflex,ygrave"
	k="18" />
    <hkern g1="v"
	g2="comma,period"
	k="50" />
    <hkern g1="v"
	g2="quotedbl,quotesingle"
	k="3" />
  </font>
</defs></svg>
