<?php
/**
 * Custom page walker for this theme.
 *
 * @package Boat Rental
 */

if (!class_exists('Boat_Rental_Walker_Page')) {
    /**
     * CUSTOM PAGE WALKER
     * A custom walker for pages.
     */
    class Boat_Rental_Walker_Page extends Walker_Page
    {

        /**
         * Outputs the beginning of the current element in the tree.
         *
         * @param string $boat_rental_output Used to append additional content. Passed by reference.
         * @param WP_Post $page Page data object.
         * @param int $boat_rental_depth Optional. Depth of page. Used for padding. Default 0.
         * @param array $boat_rental_args Optional. Array of arguments. Default empty array.
         * @param int $current_page Optional. Page ID. Default 0.
         * @since 2.1.0
         *
         * @see Walker::start_el()
         */

        public function start_lvl( &$boat_rental_output, $boat_rental_depth = 0, $boat_rental_args = array() ) {
            $boat_rental_indent  = str_repeat( "\t", $boat_rental_depth );
            $boat_rental_output .= "$boat_rental_indent<ul class='sub-menu'>\n";
        }

        public function start_el(&$boat_rental_output, $page, $boat_rental_depth = 0, $boat_rental_args = array(), $current_page = 0)
        {

            if (isset($boat_rental_args['item_spacing']) && 'preserve' === $boat_rental_args['item_spacing']) {
                $t = "\t";
                $n = "\n";
            } else {
                $t = '';
                $n = '';
            }
            if ($boat_rental_depth) {
                $boat_rental_indent = str_repeat($t, $boat_rental_depth);
            } else {
                $boat_rental_indent = '';
            }

            $boat_rental_css_class = array('page_item', 'page-item-' . $page->ID);

            if (isset($boat_rental_args['pages_with_children'][$page->ID])) {
                $boat_rental_css_class[] = 'page_item_has_children';
            }

            if (!empty($current_page)) {
                $_current_page = get_post($current_page);
                if ($_current_page && in_array($page->ID, $_current_page->ancestors, true)) {
                    $boat_rental_css_class[] = 'current_page_ancestor';
                }
                if ($page->ID === $current_page) {
                    $boat_rental_css_class[] = 'current_page_item';
                } elseif ($_current_page && $page->ID === $_current_page->post_parent) {
                    $boat_rental_css_class[] = 'current_page_parent';
                }
            } elseif (get_option('page_for_posts') === $page->ID) {
                $boat_rental_css_class[] = 'current_page_parent';
            }

            /** This filter is documented in wp-includes/class-walker-page.php */
            $boat_rental_css_classes = implode(' ', apply_filters('page_css_class', $boat_rental_css_class, $page, $boat_rental_depth, $boat_rental_args, $current_page));
            $boat_rental_css_classes = $boat_rental_css_classes ? ' class="' . esc_attr($boat_rental_css_classes) . '"' : '';

            if ('' === $page->post_title) {
                /* translators: %d: ID of a post. */
                $page->post_title = sprintf(__('#%d (no title)', 'boat-rental'), $page->ID);
            }

            $boat_rental_args['link_before'] = empty($boat_rental_args['link_before']) ? '' : $boat_rental_args['link_before'];
            $boat_rental_args['link_after'] = empty($boat_rental_args['link_after']) ? '' : $boat_rental_args['link_after'];

            $boat_rental_atts = array();
            $boat_rental_atts['href'] = get_permalink($page->ID);
            $boat_rental_atts['aria-current'] = ($page->ID === $current_page) ? 'page' : '';

            /** This filter is documented in wp-includes/class-walker-page.php */
            $boat_rental_atts = apply_filters('page_menu_link_attributes', $boat_rental_atts, $page, $boat_rental_depth, $boat_rental_args, $current_page);

            $boat_rental_attributes = '';
            foreach ($boat_rental_atts as $attr => $boat_rental_value) {
                if (!empty($boat_rental_value)) {
                    $boat_rental_value = ('href' === $attr) ? esc_url($boat_rental_value) : esc_attr($boat_rental_value);
                    $boat_rental_attributes .= ' ' . $attr . '="' . $boat_rental_value . '"';
                }
            }

            $boat_rental_args['list_item_before'] = '';
            $boat_rental_args['list_item_after'] = '';
            $boat_rental_args['icon_rennder'] = '';
            // Wrap the link in a div and append a sub menu toggle.
            if (isset($boat_rental_args['show_toggles']) && true === $boat_rental_args['show_toggles']) {
                // Wrap the menu item link contents in a div, used for positioning.
                $boat_rental_args['list_item_after'] = '';
            }


            // Add icons to menu items with children.
            if (isset($boat_rental_args['show_sub_menu_icons']) && true === $boat_rental_args['show_sub_menu_icons']) {
                if (isset($boat_rental_args['pages_with_children'][$page->ID])) {
                    $boat_rental_args['icon_rennder'] = '';
                }
            }

            // Add icons to menu items with children.
            if (isset($boat_rental_args['show_toggles']) && true === $boat_rental_args['show_toggles']) {
                if (isset($boat_rental_args['pages_with_children'][$page->ID])) {

                    $toggle_target_string = '.page_item.page-item-' . $page->ID . ' > .sub-menu';

                    $boat_rental_args['list_item_after'] = '<button type="button" class="theme-aria-button submenu-toggle" data-toggle-target="' . $toggle_target_string . '" data-toggle-type="slidetoggle" data-toggle-duration="250"><span class="btn__content" tabindex="-1"><span class="screen-reader-text">' . __( 'Show sub menu', 'boat-rental' ) . '</span>' . boat_rental_get_theme_svg( 'chevron-down' ) . '</span></button>';
                }
            }

            if (isset($boat_rental_args['show_toggles']) && true === $boat_rental_args['show_toggles']) {

                $boat_rental_output .= $boat_rental_indent . sprintf(
                        '<li%s>%s%s<a%s>%s%s%s</a>%s%s',
                        $boat_rental_css_classes,
                        '<div class="submenu-wrapper">',
                        $boat_rental_args['list_item_before'],
                        $boat_rental_attributes,
                        $boat_rental_args['link_before'],
                        /** This filter is documented in wp-includes/post-template.php */
                        apply_filters('the_title', $page->post_title, $page->ID),
                        $boat_rental_args['link_after'],
                        $boat_rental_args['list_item_after'],
                        '</div>'
                    );

            }else{

                $boat_rental_output .= $boat_rental_indent . sprintf(
                        '<li%s>%s<a%s>%s%s%s%s</a>%s',
                        $boat_rental_css_classes,
                        $boat_rental_args['list_item_before'],
                        $boat_rental_attributes,
                        $boat_rental_args['link_before'],
                        /** This filter is documented in wp-includes/post-template.php */
                        apply_filters('the_title', $page->post_title, $page->ID),
                        $boat_rental_args['icon_rennder'],
                        $boat_rental_args['link_after'],
                        $boat_rental_args['list_item_after']
                    );

            }

            if (!empty($boat_rental_args['show_date'])) {
                if ('modified' === $boat_rental_args['show_date']) {
                    $boat_rental_time = $page->post_modified;
                } else {
                    $boat_rental_time = $page->post_date;
                }

                $boat_rental_date_format = empty($boat_rental_args['date_format']) ? '' : $boat_rental_args['date_format'];
                $boat_rental_output .= ' ' . mysql2date($boat_rental_date_format, $boat_rental_time);
            }
        }
    }
}