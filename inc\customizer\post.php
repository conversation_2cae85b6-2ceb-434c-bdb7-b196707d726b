<?php
/**
* Posts Settings.
*
* @package Boat Rental
*/

$boat_rental_default = boat_rental_get_default_theme_options();

// Single Post Section.
$wp_customize->add_section( 'boat_rental_single_posts_settings',
    array(
    'title'      => esc_html__( 'Single Meta Information Settings', 'boat-rental' ),
    'priority'   => 35,
    'capability' => 'edit_theme_options',
    'panel'      => 'boat_rental_theme_option_panel',
    )
);

$wp_customize->add_setting('boat_rental_display_single_post_image',
    array(
        'default' => $boat_rental_default['boat_rental_display_single_post_image'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_display_single_post_image',
    array(
        'label' => esc_html__('Enable Single Posts Image', 'boat-rental'),
        'section' => 'boat_rental_single_posts_settings',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting('boat_rental_post_author',
    array(
        'default' => $boat_rental_default['boat_rental_post_author'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_post_author',
    array(
        'label' => esc_html__('Enable Posts Author', 'boat-rental'),
        'section' => 'boat_rental_single_posts_settings',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting('boat_rental_post_date',
    array(
        'default' => $boat_rental_default['boat_rental_post_date'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_post_date',
    array(
        'label' => esc_html__('Enable Posts Date', 'boat-rental'),
        'section' => 'boat_rental_single_posts_settings',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting('boat_rental_post_category',
    array(
        'default' => $boat_rental_default['boat_rental_post_category'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_post_category',
    array(
        'label' => esc_html__('Enable Posts Category', 'boat-rental'),
        'section' => 'boat_rental_single_posts_settings',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting('boat_rental_post_tags',
    array(
        'default' => $boat_rental_default['boat_rental_post_tags'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_post_tags',
    array(
        'label' => esc_html__('Enable Posts Tags', 'boat-rental'),
        'section' => 'boat_rental_single_posts_settings',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting( 'boat_rental_single_page_content_alignment',
    array(
    'default'           => $boat_rental_default['boat_rental_single_page_content_alignment'],
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_page_content_alignment',
    )
);
$wp_customize->add_control( 'boat_rental_single_page_content_alignment',
    array(
    'label'       => esc_html__( 'Single Page Content Alignment', 'boat-rental' ),
    'section'     => 'boat_rental_single_posts_settings',
    'type'        => 'select',
    'choices'     => array(
        'left' => esc_html__( 'Left', 'boat-rental' ),
        'center'  => esc_html__( 'Center', 'boat-rental' ),
        'right'    => esc_html__( 'Right', 'boat-rental' ),
        ),
    )
);

$wp_customize->add_setting( 'boat_rental_single_post_content_alignment',
    array(
    'default'           => $boat_rental_default['boat_rental_single_post_content_alignment'],
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_page_content_alignment',
    )
);
$wp_customize->add_control( 'boat_rental_single_post_content_alignment',
    array(
    'label'       => esc_html__( 'Single Post Content Alignment', 'boat-rental' ),
    'section'     => 'boat_rental_single_posts_settings',
    'type'        => 'select',
    'choices'     => array(
        'left' => esc_html__( 'Left', 'boat-rental' ),
        'center'  => esc_html__( 'Center', 'boat-rental' ),
        'right'    => esc_html__( 'Right', 'boat-rental' ),
        ),
    )
);

// Archive Post Section.
$wp_customize->add_section( 'boat_rental_posts_settings',
    array(
    'title'      => esc_html__( 'Archive Meta Information Settings', 'boat-rental' ),
    'priority'   => 36,
    'capability' => 'edit_theme_options',
    'panel'      => 'boat_rental_theme_option_panel',
    )
);

$wp_customize->add_setting('boat_rental_display_archive_post_format_icon',
    array(
        'default' => $boat_rental_default['boat_rental_display_archive_post_format_icon'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_display_archive_post_format_icon',
    array(
        'label' => esc_html__('Enable Posts Format Icon', 'boat-rental'),
        'section' => 'boat_rental_posts_settings',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting('boat_rental_display_archive_post_image',
    array(
        'default' => $boat_rental_default['boat_rental_display_archive_post_image'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_display_archive_post_image',
    array(
        'label' => esc_html__('Enable Posts Image', 'boat-rental'),
        'section' => 'boat_rental_posts_settings',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting('boat_rental_display_archive_post_category',
    array(
        'default' => $boat_rental_default['boat_rental_display_archive_post_category'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_display_archive_post_category',
    array(
        'label' => esc_html__('Enable Posts Category', 'boat-rental'),
        'section' => 'boat_rental_posts_settings',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting('boat_rental_display_archive_post_title',
    array(
        'default' => $boat_rental_default['boat_rental_display_archive_post_title'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_display_archive_post_title',
    array(
        'label' => esc_html__('Enable Posts Title', 'boat-rental'),
        'section' => 'boat_rental_posts_settings',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting('boat_rental_display_archive_post_content',
    array(
        'default' => $boat_rental_default['boat_rental_display_archive_post_content'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_display_archive_post_content',
    array(
        'label' => esc_html__('Enable Posts Content', 'boat-rental'),
        'section' => 'boat_rental_posts_settings',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting('boat_rental_display_archive_post_button',
    array(
        'default' => $boat_rental_default['boat_rental_display_archive_post_button'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_display_archive_post_button',
    array(
        'label' => esc_html__('Enable Posts Button', 'boat-rental'),
        'section' => 'boat_rental_posts_settings',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting('boat_rental_excerpt_limit',
    array(
        'default'           => $boat_rental_default['boat_rental_excerpt_limit'],
        'capability'        => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_number_range',
    )
);
$wp_customize->add_control('boat_rental_excerpt_limit',
    array(
        'label'       => esc_html__('Blog Posts Excerpt limit', 'boat-rental'),
        'section'     => 'boat_rental_posts_settings',
        'type'        => 'number',
        'input_attrs' => array(
           'min'   => 1,
           'max'   => 100,
           'step'   => 1,
        ),
    )
);

$wp_customize->add_setting( 'boat_rental_archive_image_size',
	array(
	'default'           => 'medium',
	'capability'        => 'edit_theme_options',
	'sanitize_callback' => 'boat_rental_sanitize_select',
	)
);
$wp_customize->add_control( 'boat_rental_archive_image_size',
	array(
	'label'       => esc_html__( 'Blog Posts Image Size', 'boat-rental' ),
	'section'     => 'boat_rental_posts_settings',
	'type'        => 'select',
	'choices'               => array(
		'full' => esc_html__( 'Large Size Image', 'boat-rental' ),
		'large' => esc_html__( 'Big Size Image', 'boat-rental' ),
		'medium' => esc_html__( 'Medium Size Image', 'boat-rental' ),
		'small' => esc_html__( 'Small Size Image', 'boat-rental' ),
		'xsmall' => esc_html__( 'Extra Small Size Image', 'boat-rental' ),
		'thumbnail' => esc_html__( 'Thumbnail Size Image', 'boat-rental' ),
	    ),
	)
);

$wp_customize->add_setting('boat_rental_posts_per_columns',
    array(
    'default'           => '3',
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_number_range',
    )
);
$wp_customize->add_control('boat_rental_posts_per_columns',
    array(
    'label'       => esc_html__('Blog Posts Per Column', 'boat-rental'),
    'section'     => 'boat_rental_posts_settings',
    'type'        => 'number',
    'input_attrs' => array(
    'min'   => 1,
    'max'   => 5,
    'step'   => 1,
    ),
    )
);