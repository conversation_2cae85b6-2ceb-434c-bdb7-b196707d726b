<?php
/**
* Header Banner Options.
*
* @package Boat Rental
*/

$boat_rental_default = boat_rental_get_default_theme_options();
$boat_rental_post_category_list = boat_rental_post_category_list();

$wp_customize->add_section( 'boat_rental_header_banner_setting',
    array(
    'title'      => esc_html__( 'Slider Settings', 'boat-rental' ),
    'priority'   => 10,
    'capability' => 'edit_theme_options',
    'panel'      => 'boat_rental_theme_home_pannel',
    )
);

// Show/Hide Site Logo
$wp_customize->add_setting('boat_rental_display_logo', array(
    'default'           => false,
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_checkbox',
));
$wp_customize->add_control('boat_rental_display_logo', array(
    'label'    => esc_html__('Enable / Disable Site Logo', 'boat-rental'),
    'section'  => 'title_tagline',
    'type'     => 'checkbox',
));

// Show/Hide Site Title
$wp_customize->add_setting('boat_rental_display_title', array(
    'default'           => true,
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_checkbox',
));
$wp_customize->add_control('boat_rental_display_title', array(
    'label'    => esc_html__('Enable / Disable Site Title', 'boat-rental'),
    'section'  => 'title_tagline',
    'type'     => 'checkbox',
));

// Show/Hide Site Tagline
$wp_customize->add_setting('boat_rental_display_header_text',
    array(
        'default'           => false,
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_display_header_text',
    array(
        'label' => esc_html__('Enable / Disable Site Tagline', 'boat-rental'),
        'section' => 'title_tagline',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting('boat_rental_header_banner',
    array(
        'default' => $boat_rental_default['boat_rental_header_banner'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_header_banner',
    array(
        'label' => esc_html__('Enable Slider', 'boat-rental'),
        'section' => 'boat_rental_header_banner_setting',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting( 'boat_rental_header_banner_cat',
    array(
    'default'           => '',
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'boat_rental_sanitize_select',
    )
);
$wp_customize->add_control( 'boat_rental_header_banner_cat',
    array(
    'label'       => esc_html__( 'Slider Post Category', 'boat-rental' ),
    'section'     => 'boat_rental_header_banner_setting',
    'type'        => 'select',
    'choices'     => $boat_rental_post_category_list,
    )
);

$wp_customize->add_setting( 'boat_rental_contact_form_shortcode',
    array(
    'default'           => '',
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'sanitize_text_field',
    )
);
$wp_customize->add_control( 'boat_rental_contact_form_shortcode',
    array(
    'label'    => esc_html__( 'Form Shortcode ', 'boat-rental' ),
    'section'  => 'boat_rental_header_banner_setting',
    'type'     => 'text',
    )
);

// Explore Our Boats Setting

$wp_customize->add_section( 'product_column_setting',
    array(
    'title'      => esc_html__( 'Explore Our Boats Setting', 'boat-rental' ),
    'priority'   => 10,
    'capability' => 'edit_theme_options',
    'panel'      => 'boat_rental_theme_home_pannel',
    )
);

$wp_customize->add_setting('boat_rental_header_case_studies',
    array(
        'default' => $boat_rental_default['boat_rental_header_case_studies'],
        'capability' => 'edit_theme_options',
        'sanitize_callback' => 'boat_rental_sanitize_checkbox',
    )
);
$wp_customize->add_control('boat_rental_header_case_studies',
    array(
        'label' => esc_html__('Enable Our Boats', 'boat-rental'),
        'section' => 'product_column_setting',
        'type' => 'checkbox',
    )
);

$wp_customize->add_setting( 'boat_rental_team_section_title',
    array(
    'default'           => $boat_rental_default['boat_rental_team_section_title'],
    'capability'        => 'edit_theme_options',
    'sanitize_callback' => 'sanitize_text_field',
    )
);
$wp_customize->add_control( 'boat_rental_team_section_title',
    array(
    'label'    => esc_html__( 'Title ', 'boat-rental' ),
    'section'  => 'product_column_setting',
    'type'     => 'text',
    )
);

for($i=1; $i<= 3; $i++) {

    $wp_customize->add_setting('boat_rental_price_list_tab_title'.$i,array(
      'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('boat_rental_price_list_tab_title'.$i,array(
      'label' => __('Our Boats Tab Heading ','boat-rental').$i,
      'section' => 'product_column_setting',
      'setting' => 'boat_rental_price_list_tab_title'.$i,
      'type'  => 'text'
    ));
}

if (taxonomy_exists( 'boattaxonomy' )) {
    $categories = get_terms(array(
        'taxonomy'   => 'boattaxonomy',
        'hide_empty' => false,
    ));
    $cat_post = array();
    $cat_post[]= 'select';
    $m = 0;
    foreach($categories as $category){
        if($m==0){
            $default = $category->slug;
            $m++;
        }
        $cat_post[$category->slug] = $category->name;
    }

    $wp_customize->add_setting('boat_rental_category_tab1',array(
        'default'   => 'Web Design',
        'sanitize_callback' => 'boat_rental_sanitize_select',
    ));
    $wp_customize->add_control('boat_rental_category_tab1' ,array(
        'type'    => 'select',
        'choices' => $cat_post,
        'label' => __('Select Category to display Our Boats 1','boat-rental'),
        'section' => 'product_column_setting',
    ));

    $cat_post = array();
    $cat_post[]= 'select';
    $m = 0;
    foreach($categories as $category){
        if($m==0){
            $default = $category->slug;
            $m++;
        }
        $cat_post[$category->slug] = $category->name;
    }

    $wp_customize->add_setting('boat_rental_category_tab2',array(
        'default'   => 'uncategorized',
        'sanitize_callback' => 'boat_rental_sanitize_select',
    ));
    $wp_customize->add_control('boat_rental_category_tab2',array(
        'type'    => 'select',
        'choices' => $cat_post,
        'label' => __('Select Category to display Our Boats 2','boat-rental'),
        'section' => 'product_column_setting',
    ));

    $cat_post = array();
    $cat_post[]= 'select';
    $m = 0;
    foreach($categories as $category){
        if($m==0){
            $default = $category->slug;
            $m++;
        }
        $cat_post[$category->slug] = $category->name;
    }

    $wp_customize->add_setting('boat_rental_category_tab3',array(
        'default'   => 'Web Design',
        'sanitize_callback' => 'boat_rental_sanitize_select',
    ));
    $wp_customize->add_control('boat_rental_category_tab3',array(
        'type'    => 'select',
        'choices' => $cat_post,
        'label' => __('Select Category to display Our Boats 3','boat-rental'),
        'section' => 'product_column_setting',
    ));
}
    