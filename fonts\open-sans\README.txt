Open Sans Variable Font
=======================

This download contains Open Sans as both variable fonts and static fonts.

Open Sans is a variable font with these axes:
  wdth
  wght

This means all the styles are contained in these files:
  Open_Sans/OpenSans-VariableFont_wdth,wght.ttf
  Open_Sans/OpenSans-Italic-VariableFont_wdth,wght.ttf

If your app fully supports variable fonts, you can now pick intermediate styles
that aren’t available as static fonts. Not all apps support variable fonts, and
in those cases you can use the static font files for Open Sans:
  Open_Sans/static/OpenSans_Condensed/OpenSans_Condensed-Light.ttf
  Open_Sans/static/OpenSans_Condensed/OpenSans_Condensed-Regular.ttf
  Open_Sans/static/OpenSans_Condensed/OpenSans_Condensed-Medium.ttf
  Open_Sans/static/OpenSans_Condensed/OpenSans_Condensed-SemiBold.ttf
  Open_Sans/static/OpenSans_Condensed/OpenSans_Condensed-Bold.ttf
  Open_Sans/static/OpenSans_Condensed/OpenSans_Condensed-ExtraBold.ttf
  Open_Sans/static/OpenSans_SemiCondensed/OpenSans_SemiCondensed-Light.ttf
  Open_Sans/static/OpenSans_SemiCondensed/OpenSans_SemiCondensed-Regular.ttf
  Open_Sans/static/OpenSans_SemiCondensed/OpenSans_SemiCondensed-Medium.ttf
  Open_Sans/static/OpenSans_SemiCondensed/OpenSans_SemiCondensed-SemiBold.ttf
  Open_Sans/static/OpenSans_SemiCondensed/OpenSans_SemiCondensed-Bold.ttf
  Open_Sans/static/OpenSans_SemiCondensed/OpenSans_SemiCondensed-ExtraBold.ttf
  Open_Sans/static/OpenSans/OpenSans-Light.ttf
  Open_Sans/static/OpenSans/OpenSans-Regular.ttf
  Open_Sans/static/OpenSans/OpenSans-Medium.ttf
  Open_Sans/static/OpenSans/OpenSans-SemiBold.ttf
  Open_Sans/static/OpenSans/OpenSans-Bold.ttf
  Open_Sans/static/OpenSans/OpenSans-ExtraBold.ttf
  Open_Sans/static/OpenSans_Condensed/OpenSans_Condensed-LightItalic.ttf
  Open_Sans/static/OpenSans_Condensed/OpenSans_Condensed-Italic.ttf
  Open_Sans/static/OpenSans_Condensed/OpenSans_Condensed-MediumItalic.ttf
  Open_Sans/static/OpenSans_Condensed/OpenSans_Condensed-SemiBoldItalic.ttf
  Open_Sans/static/OpenSans_Condensed/OpenSans_Condensed-BoldItalic.ttf
  Open_Sans/static/OpenSans_Condensed/OpenSans_Condensed-ExtraBoldItalic.ttf
  Open_Sans/static/OpenSans_SemiCondensed/OpenSans_SemiCondensed-LightItalic.ttf
  Open_Sans/static/OpenSans_SemiCondensed/OpenSans_SemiCondensed-Italic.ttf
  Open_Sans/static/OpenSans_SemiCondensed/OpenSans_SemiCondensed-MediumItalic.ttf
  Open_Sans/static/OpenSans_SemiCondensed/OpenSans_SemiCondensed-SemiBoldItalic.ttf
  Open_Sans/static/OpenSans_SemiCondensed/OpenSans_SemiCondensed-BoldItalic.ttf
  Open_Sans/static/OpenSans_SemiCondensed/OpenSans_SemiCondensed-ExtraBoldItalic.ttf
  Open_Sans/static/OpenSans/OpenSans-LightItalic.ttf
  Open_Sans/static/OpenSans/OpenSans-Italic.ttf
  Open_Sans/static/OpenSans/OpenSans-MediumItalic.ttf
  Open_Sans/static/OpenSans/OpenSans-SemiBoldItalic.ttf
  Open_Sans/static/OpenSans/OpenSans-BoldItalic.ttf
  Open_Sans/static/OpenSans/OpenSans-ExtraBoldItalic.ttf

Get started
-----------

1. Install the font files you want to use

2. Use your app's font picker to view the font family and all the
available styles

Learn more about variable fonts
-------------------------------

  https://developers.google.com/web/fundamentals/design-and-ux/typography/variable-fonts
  https://variablefonts.typenetwork.com
  https://medium.com/variable-fonts

In desktop apps

  https://theblog.adobe.com/can-variable-fonts-illustrator-cc
  https://helpx.adobe.com/nz/photoshop/using/fonts.html#variable_fonts

Online

  https://developers.google.com/fonts/docs/getting_started
  https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Fonts/Variable_Fonts_Guide
  https://developer.microsoft.com/en-us/microsoft-edge/testdrive/demos/variable-fonts

Installing fonts

  MacOS: https://support.apple.com/en-us/HT201749
  Linux: https://www.google.com/search?q=how+to+install+a+font+on+gnu%2Blinux
  Windows: https://support.microsoft.com/en-us/help/314960/how-to-install-or-remove-a-font-in-windows

Android Apps

  https://developers.google.com/fonts/docs/android
  https://developer.android.com/guide/topics/ui/look-and-feel/downloadable-fonts

License
-------
Please read the full license text (OFL.txt) to understand the permissions,
restrictions and requirements for usage, redistribution, and modification.

You can use them in your products & projects – print or digital,
commercial or otherwise.

This isn't legal advice, please consider consulting a lawyer and see the full
license for all details.
