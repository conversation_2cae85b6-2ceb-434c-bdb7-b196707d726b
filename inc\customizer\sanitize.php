<?php
/**
* Custom Functions.
*
* @package Boat Rental
*/

if( !function_exists( 'boat_rental_sanitize_sidebar_option' ) ) :

    // Sidebar Option Sanitize.
    function boat_rental_sanitize_sidebar_option( $boat_rental_input ){

        $boat_rental_metabox_options = array( 'global-sidebar','left-sidebar','right-sidebar','no-sidebar' );
        if( in_array( $boat_rental_input,$boat_rental_metabox_options ) ){

            return $boat_rental_input;

        }

        return;

    }

endif;

if ( ! function_exists( 'boat_rental_sanitize_checkbox' ) ) :

	/**
	 * Sanitize checkbox.
	 */
	function boat_rental_sanitize_checkbox( $boat_rental_checked ) {

		return ( ( isset( $boat_rental_checked ) && true === $boat_rental_checked ) ? true : false );

	}

endif;


if ( ! function_exists( 'boat_rental_sanitize_select' ) ) :

    /**
     * Sanitize select.
     */
    function boat_rental_sanitize_select( $boat_rental_input, $boat_rental_setting ) {
        $boat_rental_input = sanitize_text_field( $boat_rental_input );
        $boat_rental_choices = $boat_rental_setting->manager->get_control( $boat_rental_setting->id )->choices;
        return ( array_key_exists( $boat_rental_input, $boat_rental_choices ) ? $boat_rental_input : $boat_rental_setting->default );
    }

endif;